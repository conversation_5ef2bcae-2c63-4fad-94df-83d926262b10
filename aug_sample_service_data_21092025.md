# GoSea Database Schema Analysis: AG Holiday Provider Service Relations

## Overview
This document analyzes the database schema relationships for the `provider_services` table, specifically focusing on existing data with `providerId = provider_ag_holiday`. This analysis covers all related tables and their interconnections within the GoSea platform.

## Entity Relationship Diagram

```mermaid
erDiagram
    users ||--|| profiles : has
    users ||--|| providers : owns
    providers ||--o{ boats : owns
    providers ||--o{ provider_services : offers
    providers ||--o{ provider_operating_areas : operates_in
    
    provider_services ||--|| service_types : categorized_by
    service_types ||--|| service_categories : belongs_to
    
    provider_services ||--o{ service_assignments : assigned_to
    boats ||--o{ service_assignments : used_in
    
    provider_services ||--o{ service_packages : has
    package_types ||--o{ service_packages : defines
    
    provider_services ||--o{ service_schedules : scheduled_for
    provider_services ||--o{ service_availability : available_on
    
    provider_services ||--o{ service_age_pricing : priced_by
    provider_services ||--o{ service_age_ranges : age_restricted_by
    age_categories ||--o{ service_age_pricing : applies_to
    age_categories ||--o{ service_age_ranges : defines
    
    provider_services ||--o{ service_routes : uses
    routes ||--o{ service_routes : available_for
    jetties ||--o{ routes : departure_from
    destinations ||--o{ routes : arrival_at
    
    provider_services ||--o{ bookings : booked_for
    bookings ||--o{ payments : generates
    
    jetties ||--o{ provider_operating_areas : accessible_from
```

## AG Holiday Provider Data Structure

### 1. Core Provider Information

#### Users Table
```sql
-- user_ag_holiday
{
  id: 'user_ag_holiday',
  email: '<EMAIL>',
  role: 'BOAT_OWNER',
  emailVerified: true,
  isActive: true
}
```

#### Profiles Table
```sql
-- profile_ag_holiday
{
  id: 'profile_ag_holiday',
  userId: 'user_ag_holiday',
  firstName: 'Koh',
  lastName: 'Siew Hua',
  phone: '+***********',
  language: 'en',
  personalAddress1: '67 Lorong Damai',
  personalCity: 'Kota Bharu',
  personalPostcode: '15200',
  personalState: 'Kelantan'
}
```

#### Providers Table
```sql
-- provider_ag_holiday
{
  id: 'provider_ag_holiday',
  userId: 'user_ag_holiday',
  companyName: 'AG Holiday',
  displayName: 'AG Holiday',
  description: 'Family-friendly holiday tours and activities in Terengganu and surrounding areas.',
  brn: 'AG-2023-004',
  operatingLicense: 'ML-2023-004',
  businessPhone: '+***********',
  businessEmail: '<EMAIL>',
  websiteUrl: 'https://agholiday.com',
  businessAddress1: '321 Beachfront Drive',
  businessCity: 'Kota Bharu',
  businessPostcode: '16000',
  businessState: 'Kelantan',
  logoUrl: '/images/providers/ag-holiday-logo.jpg',
  coverImageUrl: '/images/providers/ag-holiday-cover.jpg',
  rating: 4.5,
  reviewCount: 123,
  totalBookings: 290,
  isVerified: true,
  isActive: true,
  verifiedAt: '2023-03-05',
  verifiedBy: 'admin'
}
```

### 2. Operating Areas

#### Provider Operating Areas Table
```sql
-- poa_ag_kt
{
  id: 'poa_ag_kt',
  providerId: 'provider_ag_holiday',
  jettyId: 'jeti_kampung_mangkuk', -- Setiu jetty
  isActive: true
}
```

### 3. Fleet Management

#### Boats Table
```sql
-- boat_1 (AG Holiday)
{
  id: 'boat_1',
  ownerId: 'user_ag_holiday',
  providerId: 'provider_ag_holiday',
  name: 'Boat 1',
  description: 'Family-friendly boat for leisure activities',
  capacity: 10,
  basePrice: 70.00,
  status: 'APPROVED',
  registrationNumber: 'MY-AG-2024-001',
  yearBuilt: 2023,
  engineType: 'Single Outboard 150HP',
  length: 8.0,
  safetyRating: 'A',
  serviceType: 'Snorkeling & Passenger Boat',
  location: 'Setiu',
  isActive: true,
  amenities: {
    seating: 'Comfortable bench seating',
    shade: 'Large sun canopy',
    storage: 'Multiple storage compartments',
    comfort: 'Cushioned seating and backrests'
  }
}

-- boat_2 (AG Holiday)
{
  id: 'boat_2',
  ownerId: 'user_ag_holiday',
  providerId: 'provider_ag_holiday',
  name: 'Boat 2',
  description: 'Family-friendly boat for leisure activities',
  capacity: 10,
  basePrice: 70.00,
  status: 'APPROVED',
  registrationNumber: 'MY-AG-2024-002',
  yearBuilt: 2023,
  engineType: 'Single Outboard 150HP',
  length: 8.0,
  safetyRating: 'A',
  serviceType: 'Snorkeling & Passenger Boat',
  location: 'Setiu',
  isActive: true,
  amenities: {
    seating: 'Comfortable bench seating',
    shade: 'Large sun canopy',
    storage: 'Multiple storage compartments',
    comfort: 'Cushioned seating and backrests'
  }
}
```

### 4. Service Structure

#### Service Categories & Types
```sql
-- Service Category: Island Hopping
{
  id: 'sc_island_hopping',
  name: 'Island Hopping',
  code: 'ISLAND_HOPPING',
  description: 'Multi-island exploration tours',
  requiresDestination: true,
  sortOrder: 1,
  isActive: true
}

-- Service Type: Full Day Island Hopping
{
  id: 'st_full_day_island_hopping',
  categoryId: 'sc_island_hopping',
  name: 'Full Day Island Hopping',
  code: 'FULL_DAY_ISLAND_HOPPING',
  description: 'Full day island hopping experience',
  defaultDuration: 480, -- 8 hours
  requiresRoute: true,
  allowsMultipleDestinations: true,
  isActive: true
}
```

#### Provider Services Table (Main Focus)
```sql
-- ps_daytrip_snorkeling (AG Holiday's main service)
{
  id: 'ps_daytrip_snorkeling',
  providerId: 'provider_ag_holiday',
  serviceTypeId: 'st_full_day_island_hopping',
  name: 'Day Trip Snorkeling',
  description: 'Day trip snorkeling Pulau Redang. Experience enhanced comfort with more inclusive features of premium package.',
  basePrice: 80.00,
  duration: 480, -- 8 hours
  maxCapacity: 15,
  includedItems: [
    '7 checkpoints',
    '2 ways boat transfer',
    'Snorkeling equipment',
    'Experienced guide',
    'Safety jackets',
    'Lunch box'
  ],
  excludedItems: [
    'Jetty parking fee',
    'Jetty access fee by Majlis Daerah (children under 5 years free)'
  ],
  isActive: true,
  images: [
    '/images/services/day-trip-snorkeling-1.jpg',
    '/images/services/day-trip-snorkeling-2.jpg',
    '/images/services/day-trip-snorkeling-3.jpg'
  ],
  itinerary: [
    { time: '08:15 AM', activity: 'Standby for departure', location: 'Jeti Kampung Mangkuk' },
    { time: '08:30 AM', activity: 'Breakfast (PREMIUM PACKAGE)', location: 'Jeti Kampung Mangkuk' },
    { time: '08:45 AM', activity: 'Depart for Redang Island', location: '' },
    { time: '09:30 AM', activity: 'OOTD at Little Maldives', location: 'Little Maldives' },
    { time: '09:50 AM', activity: 'Water Confidence', location: '' },
    { time: '10:30 AM', activity: 'Snorkeling at Nemo Point', location: 'Nemo Point' },
    { time: '11:30 AM', activity: 'Snorkeling at Turtle Bay', location: 'Turtle Bay' },
    { time: '01:00 PM', activity: 'Rest at Teluk Dalam Beach', location: 'Teluk Dalam Beach' },
    { time: '01:00 PM', activity: 'Tour Redang Village (PREMIUM PACKAGE)', location: 'Pulau Redang' },
    { time: '01:00 PM', activity: 'Lunch Buffet (PREMIUM PACKAGE)', location: '' },
    { time: '02:30 PM', activity: 'Snorkeling at Coral Point', location: 'Coral Point' },
    { time: '03:30 PM', activity: 'Snorkeling at Gua Kawah', location: 'Gua Kawah' },
    { time: '04:30 PM', activity: 'Return to Jeti Kampung Mangkuk', location: 'Jeti Kampung Mangkuk' },
    { time: '05:15 PM', activity: 'Hi-Tea (PREMIUM PACKAGE)', location: 'Jeti Kampung Mangkuk' }
  ]
}
```

### 5. Service Assignments (Boat-to-Service Mapping)

#### Service Assignments Table
```sql
-- sa_daytrip_snorkeling (Primary boat assignment)
{
  id: 'sa_daytrip_snorkeling',
  serviceId: 'ps_daytrip_snorkeling',
  boatId: 'boat_1', -- Boat 1 (10 capacity)
  isPrimary: true,
  maxCapacityOverride: 8, -- Reduced for premium snorkeling equipment storage
  isActive: true
}

-- sa_daytrip_snorkeling_backup (Secondary boat assignment)
{
  id: 'sa_daytrip_snorkeling_backup',
  serviceId: 'ps_daytrip_snorkeling',
  boatId: 'boat_2', -- Boat 2 (10 capacity)
  isPrimary: false,
  maxCapacityOverride: null, -- Use full capacity (10) for backup boat
  isActive: true
}
```

**Total Service Capacity Calculation:**
- Primary Boat (boat_1): 8 passengers (with override)
- Secondary Boat (boat_2): 10 passengers (full capacity)
- **Total Available Capacity: 18 passengers**

### 6. Package Management

#### Package Types
```sql
-- Basic Package Type
{
  id: 'pkg_basic',
  name: 'Basic Package',
  code: 'BASIC',
  description: 'Essential services and amenities',
  isDefault: true,
  sortOrder: 1,
  isActive: true
}

-- Premium Package Type
{
  id: 'pkg_premium',
  name: 'Premium Package',
  code: 'PREMIUM',
  description: 'Enhanced services with premium amenities',
  isDefault: false,
  sortOrder: 3,
  isActive: true
}
```

#### Service Packages Table
```sql
-- sp_daytrip_regular (Regular Package)
{
  id: 'sp_daytrip_regular',
  serviceId: 'ps_daytrip_snorkeling',
  packageTypeId: 'pkg_basic',
  basePrice: 100.00,
  priceModifier: 1.0,
  includedItems: [
    '7 checkpoints',
    '2 ways boat transfer',
    'Snorkeling equipment',
    'Experienced guide',
    'Safety jackets',
    'Lunch box'
  ],
  excludedItems: [
    'Jetty parking fee',
    'Jetty access fee by Majlis Daerah (children under 5 years free)'
  ],
  isActive: true,
  agePricing: {
    adult: 100.00,
    child: 80.00,
    toddler: 0.00,
    senior: 80.00,
    pwd: 80.00
  }
}

-- sp_daytrip_premium (Premium Package)
{
  id: 'sp_daytrip_premium',
  serviceId: 'ps_daytrip_snorkeling',
  packageTypeId: 'pkg_premium',
  basePrice: 110.00,
  priceModifier: 1.5,
  includedItems: [
    '7 checkpoints',
    '2 ways boat transfer',
    'Snorkeling equipment & Insurance',
    'Experienced guide',
    'Safety jackets',
    'Breakfast at jetty',
    'Lunch buffet, unlimited drinks and hi-teas'
  ],
  excludedItems: [
    'Jetty parking fee',
    'Jetty access fee by Majlis Daerah (children under 5 years free)'
  ],
  isActive: true,
  agePricing: {
    adult: 130.00,
    child: 110.00,
    toddler: 0.00,
    senior: 110.00,
    pwd: 110.00
  },
  description: 'Premium package with breakfast, buffet lunch, and unlimited drinks.'
}
```

### 7. Scheduling System

#### Service Schedules Table
```sql
-- ss_daytrip_morning (Daily morning schedule)
{
  id: 'ss_daytrip_morning',
  serviceId: 'ps_daytrip_snorkeling',
  dayOfWeek: null, -- Every day (0-6)
  departureTime: '08:00',
  availableCapacity: 18, -- Combined capacity from both boats
  isActive: true
}
```

**Note:** When `dayOfWeek` is NULL but `departureTime` is specified, the service runs every day (0-6) to avoid requiring daily entries for each day.

## Key Relationships Summary

### Primary Foreign Key Relationships
1. **provider_services.providerId** → **providers.id**
2. **provider_services.serviceTypeId** → **service_types.id**
3. **service_assignments.serviceId** → **provider_services.id**
4. **service_assignments.boatId** → **boats.id**
5. **service_packages.serviceId** → **provider_services.id**
6. **service_schedules.serviceId** → **provider_services.id**

### Capacity Management Logic
- **Service Max Capacity**: Defined in `provider_services.maxCapacity` (15)
- **Actual Available Capacity**: Sum of all assigned boat capacities with overrides
  - Boat 1: 8 (with override)
  - Boat 2: 10 (full capacity)
  - **Total: 18 passengers**
- **Schedule Capacity**: Reflects actual available capacity (18)

### Pricing Structure (Full Variation Model)
AG Holiday implements the **Full Variation** pricing model:
- ✅ **Packages**: Regular and Premium packages available
- ✅ **Age-based Pricing**: Different prices for adults, children, toddlers, seniors, PWD
- ✅ **Dynamic Pricing**: Package-specific age pricing in `service_packages.agePricing`

## Data Dependencies and Constraints

### Creation Order (Due to Foreign Key Constraints)
1. **users** → **profiles**
2. **users** → **providers**
3. **service_categories** → **service_types**
4. **providers** → **boats**
5. **providers** + **service_types** → **provider_services**
6. **provider_services** + **boats** → **service_assignments**
7. **package_types** + **provider_services** → **service_packages**
8. **provider_services** → **service_schedules**

### Business Rules Enforced
1. **Boat Assignment**: Every service must have at least one boat assigned
2. **Capacity Override**: Service assignments can override boat capacity for specific services
3. **Provider Ownership**: Boats can only be assigned to services from the same provider
4. **Package Pricing**: Age-based pricing can be defined at both service and package levels
5. **Schedule Flexibility**: NULL dayOfWeek with departureTime means daily operation

This comprehensive analysis demonstrates how the `provider_services` table serves as the central hub connecting providers, boats, packages, schedules, and pricing structures within the GoSea platform, specifically illustrated through the AG Holiday provider implementation.
