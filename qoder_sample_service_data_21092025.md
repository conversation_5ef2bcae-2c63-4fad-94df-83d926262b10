# Provider Services Relationship Analysis for AG Holiday (provider_ag_holiday)

## Overview

This document analyzes the relationships between the `provider_services` table and all other related tables in the database for the specific provider with ID `provider_ag_holiday`.

## Database Schema Relationships

### Core Provider Service Entity

The `provider_services` table is the central entity that connects to multiple related tables in the system. For provider `provider_ag_holiday`, there is one service created with the following details:

- **Service ID**: `ps_daytrip_snorkeling`
- **Service Name**: "Day Trip Snorkeling"
- **Provider ID**: `provider_ag_holiday`
- **Service Type**: "Full Day Island Hopping" (ID: `st_daytrip_snorkeling`)

## Direct Relationships

### 1. Provider Table
- **Relationship**: Many-to-One
- **Foreign Key**: `providerId` in `provider_services` references `id` in `providers`
- **Details**: Links the service to the AG Holiday provider entity

### 2. ServiceType Table
- **Relationship**: Many-to-One
- **Foreign Key**: `serviceTypeId` in `provider_services` references `id` in `service_types`
- **Details**: Defines the type of service (Full Day Island Hopping)

### 3. ServiceRoute Table
- **Relationship**: One-to-Many
- **Foreign Key**: `serviceId` in `service_routes` references `id` in `provider_services`
- **Details**: Links the service to specific routes
- **Example**: Route from "Jeti Kampung Mangkuk" to "Pulau Redang" (ID: `sr_daytrip_redang`)

### 4. ServiceAssignment Table
- **Relationship**: One-to-Many
- **Foreign Key**: `serviceId` in `service_assignments` references `id` in `provider_services`
- **Details**: Links the service to specific boats
- **Examples**:
  - Primary boat assignment: "Boat 1" (ID: `boat_1`)
  - Backup boat assignment: "Boat 2" (ID: `boat_2`)

### 5. ServicePackage Table
- **Relationship**: One-to-Many
- **Foreign Key**: `serviceId` in `service_packages` references `id` in `provider_services`
- **Details**: Defines different package options for the service
- **Examples**:
  - Regular package (ID: `sp_daytrip_regular`)
  - Premium package (ID: `sp_daytrip_premium`)

### 6. ServiceAgeRange Table
- **Relationship**: One-to-Many
- **Foreign Key**: `serviceId` in `service_age_ranges` references `id` in `provider_services`
- **Details**: Defines age categories for pricing
- **Examples**:
  - Adults (13+ years)
  - Children (3-12 years)
  - Toddlers (0-2 years)

### 7. ServiceAgePricing Table
- **Relationship**: One-to-Many
- **Foreign Key**: `serviceId` in `service_age_pricing` references `id` in `provider_services`
- **Details**: Defines pricing for different age categories
- **Examples**:
  - Adult pricing: RM100.00 (regular), RM130.00 (premium)
  - Child pricing: RM80.00 (regular), RM110.00 (premium)
  - Toddler pricing: RM0.00 (both packages)

### 8. ServiceSchedule Table
- **Relationship**: One-to-Many
- **Foreign Key**: `serviceId` in `service_schedules` references `id` in `provider_services`
- **Details**: Defines when the service is available
- **Example**: Daily at 08:00 AM with 18 capacity (combining both boats)

### 9. Booking Table
- **Relationship**: One-to-Many
- **Foreign Key**: `serviceId` in `bookings` references `id` in `provider_services`
- **Details**: Links customer bookings to this service

## Visual Relationship Diagram

```mermaid
graph TD
    A[Provider<br/>provider_ag_holiday] -->|owns| B[ProviderService<br/>ps_daytrip_snorkeling]
    B --> C[ServiceType<br/>st_daytrip_snorkeling]
    B --> D[ServiceRoute<br/>sr_daytrip_redang]
    B --> E[ServiceAssignment<br/>sa_daytrip_snorkeling<br/>sa_daytrip_snorkeling_backup]
    B --> F[ServicePackage<br/>sp_daytrip_regular<br/>sp_daytrip_premium]
    B --> G[ServiceAgeRange<br/>Adults, Children, Toddlers]
    B --> H[ServiceAgePricing<br/>Pricing by age category]
    B --> I[ServiceSchedule<br/>ss_daytrip_morning]
    B --> J[Booking<br/>Customer bookings]
    D --> K[Route<br/>Jeti Kampung Mangkuk to Redang]
    E --> L[Boat<br/>boat_1, boat_2]
```

## Detailed Relationship Data

### Provider Information
- **Provider ID**: `provider_ag_holiday`
- **Company Name**: AG Holiday
- **Display Name**: AG Holiday
- **User ID**: `user_ag_holiday`

### Service Details
- **Service ID**: `ps_daytrip_snorkeling`
- **Name**: Day Trip Snorkeling
- **Description**: Day trip snorkeling Pulau Redang. Experience enhanced comfort with more inclusive features of premium package.
- **Base Price**: RM80.00
- **Duration**: 480 minutes (8 hours)
- **Max Capacity**: 15
- **Included Items**: 7 checkpoints, 2 ways boat transfer, Snorkeling equipment, Experienced guide, Safety jackets, Lunch box
- **Excluded Items**: Jetty parking fee, Jetty access fee by Majlis Daerah (children under 5 years free)

### Service Route
- **Route ID**: `sr_daytrip_redang`
- **Route Details**: From "Jeti Kampung Mangkuk" to "Pulau Redang"
- **Price Modifier**: 1.0 (no adjustment)

### Service Assignments
1. **Primary Assignment**:
   - **ID**: `sa_daytrip_snorkeling`
   - **Boat ID**: `boat_1`
   - **Boat Name**: Boat 1
   - **Capacity Override**: 8 (reduced from 10 for premium gear storage)

2. **Backup Assignment**:
   - **ID**: `sa_daytrip_snorkeling_backup`
   - **Boat ID**: `boat_2`
   - **Boat Name**: Boat 2
   - **Capacity Override**: null (uses full 10 capacity)

### Service Packages
1. **Regular Package**:
   - **ID**: `sp_daytrip_regular`
   - **Package Type**: Regular (ID: `pt_regular`)
   - **Base Price**: RM100.00
   - **Price Modifier**: 1.0

2. **Premium Package**:
   - **ID**: `sp_daytrip_premium`
   - **Package Type**: Premium (ID: `pt_premium`)
   - **Base Price**: RM110.00
   - **Price Modifier**: 1.5
   - **Additional Features**: Breakfast at jetty, Lunch buffet, Unlimited drinks and hi-teas

### Age Categories and Pricing
1. **Adults (13+ years)**:
   - **Age Range ID**: (Generated)
   - **Min Age**: 13
   - **Max Age**: null
   - **Regular Price**: RM100.00
   - **Premium Price**: RM130.00

2. **Children (3-12 years)**:
   - **Age Range ID**: (Generated)
   - **Min Age**: 3
   - **Max Age**: 12
   - **Regular Price**: RM80.00
   - **Premium Price**: RM110.00

3. **Toddlers (0-2 years)**:
   - **Age Range ID**: (Generated)
   - **Min Age**: 0
   - **Max Age**: 2
   - **Regular Price**: RM0.00
   - **Premium Price**: RM0.00

### Service Schedule
- **Schedule ID**: `ss_daytrip_morning`
- **Departure Time**: 08:00
- **Available Capacity**: 18 (combined capacity of both boats)
- **Days**: Daily (null dayOfWeek)

## Summary

The `provider_services` table for AG Holiday (`provider_ag_holiday`) forms the core of a complex relationship network that connects to 9 different tables in the database. This service is well-integrated with the system, providing:

1. Multiple package options for customers
2. Age-based pricing flexibility
3. Multiple boat assignments for capacity management
4. Route-based service delivery
5. Scheduled availability
6. Customer booking integration

This comprehensive relationship structure allows for flexible pricing, capacity management, and service delivery while maintaining data integrity through proper relational constraints.