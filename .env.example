# GoSea Platform Environment Configuration

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=*****************************************/gosea_db
POSTGRES_DB=gosea_db
POSTGRES_USER=gosea
POSTGRES_PASSWORD=password

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Backend API Configuration
API_PORT=5000
NODE_ENV=development

# Frontend API URL (for Next.js)
NEXT_PUBLIC_API_URL=http://localhost:5001

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Google OAuth Configuration
# IMPORTANT: Replace these with your actual Google OAuth credentials from Google Cloud Console
# Get credentials from: https://console.cloud.google.com/apis/credentials
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
# Note: The redirect URI configured in Google Cloud Console should be:
# http://localhost:5001/api/auth/google/callback (for development)
# https://yourdomain.com/api/auth/google/callback (for production)
GOOGLE_REDIRECT_URI=http://localhost:5001/api/auth/google/callback

# Session Configuration
SESSION_SECRET=your-session-secret-key
SESSION_EXPIRES_IN=24h

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# Email Mode: false = MailHog (development), true = Real Email (Gmail)
USE_REAL_EMAIL=false

# SMTP Configuration (MailHog for development)
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>

# Gmail SMTP Configuration (for real emails)
# To use Gmail: Set USE_REAL_EMAIL=true and configure below
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your-gmail-app-password
FROM_EMAIL=<EMAIL>

# Email Templates
EMAIL_VERIFICATION_TEMPLATE=email-verification
PASSWORD_RESET_TEMPLATE=password-reset
BOOKING_CONFIRMATION_TEMPLATE=booking-confirmation

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0

# Cache TTL (Time To Live) in seconds
CACHE_TTL_SHORT=300    # 5 minutes
CACHE_TTL_MEDIUM=1800  # 30 minutes
CACHE_TTL_LONG=3600    # 1 hour

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# File Upload Settings
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf

# Image Processing
IMAGE_QUALITY=80
IMAGE_MAX_WIDTH=1920
IMAGE_MAX_HEIGHT=1080
THUMBNAIL_WIDTH=300
THUMBNAIL_HEIGHT=200

# =============================================================================
# PAYMENT CONFIGURATION
# =============================================================================
# Payment Gateway (To be configured later)
PAYMENT_GATEWAY_URL=
PAYMENT_GATEWAY_KEY=
PAYMENT_GATEWAY_SECRET=
PAYMENT_WEBHOOK_SECRET=

# Payment Settings
DEPOSIT_PERCENTAGE=30
COMMISSION_RATE=0.10
PAYMENT_HOLD_DAYS=7

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
NOTIFICATION_QUEUE_NAME=notifications

# Notification Timing
BOOKING_REMINDER_HOURS=24
PAYMENT_REMINDER_HOURS=48

# =============================================================================
# BUSINESS CONFIGURATION
# =============================================================================
# Service Types
AVAILABLE_SERVICE_TYPES=snorkeling,passenger
AVAILABLE_LOCATIONS=redang,perhentian

# Booking Settings
MIN_BOOKING_ADVANCE_HOURS=24
MAX_BOOKING_ADVANCE_DAYS=365
CANCELLATION_POLICY_HOURS=48

# Affiliate Settings
AFFILIATE_COMMISSION_RATE=0.10
AFFILIATE_PAYOUT_THRESHOLD=100
AFFILIATE_HOLD_PERIOD_DAYS=7

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Debug Settings
DEBUG=true
LOG_LEVEL=debug
ENABLE_CORS=true

# Development URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:5000
MAILHOG_URL=http://localhost:8025

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
# Logging Configuration
LOG_FORMAT=combined
LOG_FILE_PATH=/app/logs/app.log
ERROR_LOG_PATH=/app/logs/error.log

# Health Check
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# =============================================================================
# RATE LIMITING
# =============================================================================
# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# CORS Settings
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# =============================================================================
# TIMEZONE & LOCALIZATION
# =============================================================================
# Application Settings
DEFAULT_TIMEZONE=Asia/Kuala_Lumpur
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,ms

# Date/Time Formats
DATE_FORMAT=YYYY-MM-DD
TIME_FORMAT=HH:mm:ss
DATETIME_FORMAT=YYYY-MM-DD HH:mm:ss

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Feature Toggles
ENABLE_GOOGLE_OAUTH=true
ENABLE_EMAIL_OTP=true
ENABLE_AFFILIATE_SYSTEM=true
ENABLE_PAYMENT_PROCESSING=false
ENABLE_ANALYTICS=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Security Headers
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CSRF_PROTECTION=true

# Password Policy
MIN_PASSWORD_LENGTH=8
REQUIRE_PASSWORD_UPPERCASE=true
REQUIRE_PASSWORD_LOWERCASE=true
REQUIRE_PASSWORD_NUMBERS=true
REQUIRE_PASSWORD_SYMBOLS=false

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Database Backup
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/app/backups

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
# Test Database
TEST_DATABASE_URL=*****************************************/gosea_test_db

# Test Settings
TEST_JWT_SECRET=test-jwt-secret
TEST_SMTP_HOST=mailhog
TEST_REDIS_URL=redis://redis:6379/1
