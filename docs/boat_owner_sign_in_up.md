# Boat Owner/Provider Sign-In and Sign-Up Flow

## Overview

This document outlines the comprehensive authentication and onboarding flow for boat owners and service providers in the GoSea platform. The system implements a **separate, dedicated authentication flow** for boat owners that is distinct from customer authentication. The flow uses **manual email-only registration** and includes an **admin approval process** before boat owners can access the platform.

## Design Principles

### 1. Separate Authentication Flow
- **Dedicated Boat Owner Sign-In Modal**: Accessed from 'Boat Owner' menu selection in navbar
- **Separate Sign-Up Page**: Full-page registration form for boat owners
- **No Shared Components**: Completely independent from customer authentication
- **Clear User Journey**: Eliminates confusion between customer and boat owner flows

### 2. Manual Email-Only Authentication
- **No Google OAuth**: Manual email and password registration only
- **Enhanced Security**: Full control over boat owner verification process
- **Business Verification**: Integrated with admin approval workflow
- **Professional Focus**: Designed for business users, not social login

### 3. Admin Approval Workflow
- **Two-Step Verification**: Email verification + Admin approval
- **Admin Notification**: Automatic email to admin upon successful registration
- **Pending Account State**: Boat owners cannot sign in until admin approval
- **Approval Dashboard**: Admin interface for reviewing and approving boat owner accounts

## Database Schema Foundation

### User Model Structure
```sql
model User {
  id                      String                   @id @default(cuid())
  email                   String                   @unique
  role                    UserRole                 -- BOAT_OWNER role
  isActive                Boolean                  @default(true)
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  password                String                   -- Required for manual auth
  emailVerified           Boolean                  @default(false)
  emailVerifiedAt         DateTime?
  lastLoginAt             DateTime?
  loginAttempts           Int                      @default(0)
  lockedUntil             DateTime?
  
  -- Admin Approval Fields
  isApproved              Boolean                  @default(false)
  approvedAt              DateTime?
  approvedBy              String?                  -- Admin user ID
  rejectionReason         String?
  
  -- Relationships
  boats                   Boat[]                   @relation("BoatOwner")
  provider                Provider?                @relation("ProviderOwner")
  profile                 Profile?
  userSessions            UserSession[]
  emailVerificationTokens EmailVerificationToken[]
  passwordResetTokens     PasswordResetToken[]
}
```

### Provider Model Structure
```sql
model Provider {
  id               String                  @id @default(cuid())
  userId           String                  @unique
  companyName      String
  displayName      String
  description      String?
  brn              String?                 -- Business Registration Number
  operatingLicense String?
  contactPhone     String
  contactEmail     String
  -- address field removed - leverages user profile address instead
  logoUrl          String?
  coverImageUrl    String?
  rating           Decimal                 @default(0)
  reviewCount      Int                     @default(0)
  isVerified       Boolean                 @default(false)
  isActive         Boolean                 @default(true)
  isAutoGenerated  Boolean                 @default(false)
  createdAt        DateTime                @default(now())
  updatedAt        DateTime                @updatedAt
  
  -- Relationships
  user             User                    @relation("ProviderOwner")
  boats            Boat[]                  @relation("ProviderBoats")
  services         ProviderService[]
  operatingAreas   ProviderOperatingArea[]
  bookings         Booking[]
}
```

### Profile Model Structure
```sql
model Profile {
  id                 String    @id @default(cuid())
  userId             String    @unique
  firstName          String
  lastName           String
  phone              String?
  profilePicture     String?
  dateOfBirth        DateTime?
  emergencyContact   String?
  companyName        String?   -- For business profiles
  brn                String?   -- Business Registration Number
  agencyName         String?
  language           String    @default("en")
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt
  profileCompletedAt DateTime? -- Tracks profile completion
  address1           String?
  address2           String?
  city               String?
  postcode           String?
  state              String?
  
  -- Relationships
  user               User      @relation(fields: [userId], references: [id])
}
```

## Authentication Flow Architecture

### 1. Separate Authentication System

Boat owners have a completely separate authentication system from customers:

- **Entry Point**: 'Boat Owner' menu item in navbar
- **Sign-In Modal**: Dedicated modal for boat owner authentication
- **Sign-Up Page**: Full-page registration form at `/boat-owner/signup`
- **Admin Approval**: Required before account activation

### 2. Account States

Boat owner accounts progress through multiple states:

1. **Pending Email Verification**: After registration, awaiting email confirmation
2. **Pending Admin Approval**: Email verified, awaiting admin approval
3. **Approved**: Admin approved, can sign in and access platform
4. **Rejected**: Admin rejected, cannot access platform

### 3. Manual Email-Only Registration

**No Google OAuth integration** - boat owners must use manual email registration for:
- Enhanced security and control
- Professional business verification
- Integration with admin approval workflow
- Compliance with business requirements

### 4. Boat Owner Registration Flow

#### Step 1: Access Registration
```javascript
// User Journey:
// 1. Click 'Boat Owner' in navbar
// 2. Click 'Sign In' in dropdown
// 3. See sign-in modal
// 4. Click 'Don't have an account? Sign up'
// 5. Redirect to /boat-owner/signup
```

#### Step 2: Registration Form Submission
```javascript
// Frontend Registration Payload
{
  email: "<EMAIL>",
  password: "SecurePassword123!",
  firstName: "John",
  lastName: "Doe",
  phone: "+***********",
  companyName: "Marine Adventures Ltd",
  brn: "************", // Business Registration Number
  role: "BOAT_OWNER"
}
```

#### Step 3: Backend Processing
```javascript
// Enhanced authService.js - registerBoatOwner() method
async registerBoatOwner(userData) {
  const { email, password, firstName, lastName, phone, companyName, brn } = userData;
  
  // Email normalization and duplicate checking
  const normalizedEmail = email.toLowerCase().trim();
  
  // Password strength validation
  // Hash password
  
  // Transaction: Create User + Profile + Provider + EmailVerificationToken
  const result = await prisma.$transaction(async (tx) => {
    // Create User with BOAT_OWNER role (not approved yet)
    const user = await tx.user.create({
      data: {
        email: normalizedEmail,
        password: hashedPassword,
        role: "BOAT_OWNER",
        emailVerified: false,
        isApproved: false // Key difference - requires admin approval
      }
    });
    
    // Create Profile with business information
    const profile = await tx.profile.create({
      data: {
        userId: user.id,
        firstName,
        lastName,
        phone: encryptedPhone,
        companyName,
        brn
      }
    });
    
    // Create Provider record (pending approval) - using profile address
    const provider = await tx.provider.create({
      data: {
        userId: user.id,
        companyName,
        displayName: companyName,
        contactEmail: email,
        contactPhone: phone,
        brn,
        // address field removed - leverages user profile address instead
        isActive: false, // Inactive until approved
        isVerified: false
      }
    });
    
    // Create email verification token
    const verificationToken = await tx.emailVerificationToken.create({
      data: {
        userId: user.id,
        token: emailToken,
        expiresAt: emailExpiresAt
      }
    });
    
    return { user, profile, provider, verificationToken };
  });
  
  // Send verification email to boat owner
  await emailService.sendBoatOwnerVerificationEmail(
    result.user.email,
    result.profile.firstName,
    emailToken
  );
  
  return {
    success: true,
    message: 'Registration successful. Please check your email to verify your account.'
  };
}
```

#### Step 4: Email Verification
- Boat owner receives verification email
- Clicks verification link
- Backend sets `emailVerified: true`
- **Automatic admin notification email sent**

#### Step 5: Admin Notification
```javascript
// After email verification, notify admin
async notifyAdminOfNewBoatOwner(user, profile, provider) {
  await emailService.sendAdminApprovalNotification({
    adminEmail: process.env.ADMIN_EMAIL,
    boatOwner: {
      name: `${profile.firstName} ${profile.lastName}`,
      email: user.email,
      companyName: profile.companyName,
      brn: profile.brn,
      registrationDate: user.createdAt
    },
    approvalLink: `${process.env.FRONTEND_URL}/admin/boat-owners/pending`
  });
}
```

### 5. Boat Owner Sign-In Flow

#### Step 1: Access Sign-In
- Click 'Boat Owner' in navbar
- Click 'Sign In' in dropdown
- Dedicated boat owner sign-in modal opens

#### Step 2: Authentication Request
```javascript
// Frontend Login Payload
{
  email: "<EMAIL>",
  password: "SecurePassword123!"
}
```

#### Step 3: Enhanced Authentication Validation
```javascript
// authService.js - loginBoatOwner() method
async loginBoatOwner(email, password, ipAddress, userAgent) {
  const normalizedEmail = email.toLowerCase().trim();
  
  // Find boat owner user
  const user = await prisma.user.findFirst({
    where: {
      email: normalizedEmail,
      role: 'BOAT_OWNER' // Ensure only boat owners can use this endpoint
    },
    include: { profile: true, provider: true }
  });
  
  if (!user) {
    throw new Error('Invalid email or password');
  }
  
  // Standard security checks
  if (isAccountLocked(user)) {
    throw new Error('Account is locked');
  }
  
  if (!user.isActive) {
    throw new Error('Account is deactivated');
  }
  
  // Email verification check
  if (!user.emailVerified) {
    throw new Error('Please verify your email address first');
  }
  
  // NEW: Admin approval check
  if (!user.isApproved) {
    throw new Error('Your account is pending admin approval. You will receive an email once approved.');
  }
  
  // Password validation
  const isPasswordValid = await comparePassword(password, user.password);
  if (!isPasswordValid) {
    // Handle failed login attempts
    throw new Error('Invalid email or password');
  }
  
  // Generate session and tokens
  const sessionData = generateSessionData(user, ipAddress, userAgent);
  
  return {
    success: true,
    accessToken: sessionData.accessToken,
    refreshToken: sessionData.refreshToken,
    user: sessionData.user,
    message: 'Login successful'
  };
}
```

## Admin Approval Workflow

### 1. Admin Notification System

When a boat owner verifies their email, the system automatically notifies administrators:

```javascript
// Email template for admin notification
const adminNotificationTemplate = {
  subject: "New Boat Owner Registration - Approval Required",
  body: `
    A new boat owner has registered and verified their email:
    
    Name: ${profile.firstName} ${profile.lastName}
    Email: ${user.email}
    Company: ${profile.companyName}
    BRN: ${profile.brn}
    Registration Date: ${user.createdAt}
    
    Please review and approve this account:
    ${approvalLink}
  `
};
```

### 2. Admin Dashboard Integration

**Current Status Analysis:**
- **Existing Dashboard**: A comprehensive admin dashboard already exists at `/admin/dashboard`
- **Current Capabilities**: 
  - User management with BOAT_OWNER role filtering
  - Provider verification functionality (`updateProviderVerification()`)
  - Boat status management with approval workflow (APPROVED, PENDING_APPROVAL, REJECTED states)
  - Role-based filtering and search capabilities
- **Missing Features**: 
  - Specific boat owner account approval functionality (isApproved field)
  - Email-to-dashboard direct links for admin approval process
  - Boat owner approval notification workflow

**Required Enhancements:**
- Add boat owner approval fields to User model (`isApproved`, `approvedAt`, `approvedBy`, `rejectionReason`)
- Extend existing admin dashboard with boat owner approval section
- Create email-to-dashboard deep links for seamless admin approval process
- Integrate boat owner approval with existing provider verification workflow

**Admin Actions (New Endpoints Required):**
```javascript
// Get pending boat owner approvals
GET /api/admin/boat-owners/pending

// Approve boat owner account
POST /api/admin/boat-owners/:id/approve
{
  approved: true,
  approvedBy: adminUserId,
  approvalNotes: "Company verified, all documents in order"
}

// Reject boat owner account
POST /api/admin/boat-owners/:id/reject
{
  approved: false,
  rejectionReason: "Invalid business registration number"
}
```

**Email-to-Dashboard Links:**
- Direct links in admin notification emails to pending approvals: `${FRONTEND_URL}/admin/dashboard?tab=boat-owners&filter=pending&highlight=${userId}`
- One-click approval links with admin authentication verification
- Dashboard deep linking to specific boat owner records for efficient review process

### 3. Boat Owner Notification

After admin action, boat owners receive notification emails:

**Approval Email:**
- Welcome message and next steps
- Link to sign in and complete provider profile
- Platform guidelines and policies

**Rejection Email:**
- Reason for rejection
- Steps to resolve issues
- Option to re-register with corrections

## Provider Profile Completion Flow

### 1. Post-Approval Profile Enhancement

After admin approval, boat owners can sign in and enhance their provider profiles:

**Additional Provider Fields:**
```javascript
// Enhanced Provider Profile (address removed - uses profile address)
{
  description: "Professional boat services for snorkeling and tours",
  operatingLicense: "*********",
  // address field removed - system uses user profile address instead
  logoUrl: "/uploads/provider-logos/logo.jpg",
  coverImageUrl: "/uploads/provider-covers/cover.jpg",
  operatingAreas: [
    {
      jettyId: "jetty_001",
      serviceRadius: 50 // km
    }
  ]
}

// Address retrieved from user profile:
// profile.address1, profile.address2, profile.city, profile.postcode, profile.state
```

### 2. Provider Profile Creation API

**Endpoint:** `POST /api/providers/profile`
```javascript
// Backend Provider Creation
async createProviderProfile(userId, providerData) {
  const provider = await prisma.provider.create({
    data: {
      userId,
      companyName: providerData.companyName,
      displayName: providerData.displayName,
      description: providerData.description,
      brn: providerData.brn,
      operatingLicense: providerData.operatingLicense,
      contactPhone: providerData.contactPhone,
      contactEmail: providerData.contactEmail,
      address: providerData.address,
      isVerified: false, // Requires admin verification
      isActive: true
    }
  });
  
  // Update user profile completion status
  await prisma.profile.update({
    where: { userId },
    data: { profileCompletedAt: new Date() }
  });
  
  return provider;
}
```

## Security and Validation

### 1. Email Validation and Duplicate Prevention

**Enhanced Email Checking:**
- Case-insensitive email matching
- Gmail dot and alias variant detection
- Database unique constraint enforcement
- Real-time duplicate validation

### 2. Password Security Requirements

**Password Validation Rules:**
- Minimum 8 characters
- At least one uppercase letter
- At least one special character
- Secure hashing using bcrypt

### 3. Account Security Features

**Security Measures:**
- Account locking after failed login attempts
- Email verification requirement
- Session management with JWT tokens
- IP address and user agent tracking
- Password reset functionality

## Frontend Implementation

### 1. Navbar Integration

**Boat Owner Menu Item:**
```javascript
// Navbar.js - Boat Owner dropdown
const boatOwnerMenuItems = [
  {
    label: 'Sign In',
    onClick: () => setShowBoatOwnerSignIn(true)
  },
  {
    label: 'Learn More',
    href: '/boat-owner/info'
  }
];
```

### 2. Boat Owner Sign-In Modal

**BoatOwnerSignInModal.js:**
```javascript
const BoatOwnerSignInModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch(`${apiUrl}/api/auth/boat-owner/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        // Store tokens and redirect to boat owner dashboard
        localStorage.setItem('accessToken', data.accessToken);
        window.location.href = '/boat-owner/dashboard';
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">Boat Owner Sign In</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <svg className="w-6 h-6" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({...formData, password: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isLoading ? 'Signing In...' : 'Sign In'}
          </button>

          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <a href="/boat-owner/signup" className="text-blue-600 hover:text-blue-800">
                Sign up
              </a>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};
```

### 3. Boat Owner Sign-Up Page

**pages/boat-owner/signup.js:**
```javascript
const BoatOwnerSignUp = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    companyName: '',
    brn: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch(`${apiUrl}/api/auth/boat-owner/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setShowSuccess(true);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Registration Successful!</h2>
          <p className="text-gray-600 mb-6">
            Please check your email to verify your account. Once verified, an admin will review your application.
          </p>
          <a href="/" className="text-blue-600 hover:text-blue-800">
            Return to Homepage
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Boat Owner Registration
          </h1>

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First Name *
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name *
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number *
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({...formData, phone: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="+***********"
                required
              />
            </div>

            {/* Business Information */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Name *
              </label>
              <input
                type="text"
                value={formData.companyName}
                onChange={(e) => setFormData({...formData, companyName: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Registration Number (BRN) *
              </label>
              <input
                type="text"
                value={formData.brn}
                onChange={(e) => setFormData({...formData, brn: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="************"
                required
              />
            </div>

            {/* Password */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Password *
                </label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({...formData, password: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Confirm Password *
                </label>
                <input
                  type="password"
                  value={formData.confirmPassword}
                  onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 font-medium"
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </button>

            <div className="text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <button
                  type="button"
                  onClick={() => window.history.back()}
                  className="text-blue-600 hover:text-blue-800"
                >
                  Sign in
                </button>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BoatOwnerSignUp;
```

## API Endpoints

### 1. Boat Owner Authentication Endpoints

- `POST /api/auth/boat-owner/register` - Boat owner registration with business details
- `POST /api/auth/boat-owner/login` - Boat owner authentication (manual only)
- `POST /api/auth/boat-owner/logout` - Boat owner logout
- `POST /api/auth/boat-owner/refresh` - Token refresh
- `POST /api/auth/boat-owner/verify-email` - Email verification
- `POST /api/auth/boat-owner/resend-verification` - Resend verification email
- `POST /api/auth/boat-owner/forgot-password` - Password reset request
- `POST /api/auth/boat-owner/reset-password` - Password reset confirmation

### 2. Admin Approval Endpoints

- `GET /api/admin/boat-owners/pending` - List pending boat owner approvals
- `POST /api/admin/boat-owners/:id/approve` - Approve boat owner account
- `POST /api/admin/boat-owners/:id/reject` - Reject boat owner account
- `GET /api/admin/boat-owners/approved` - List approved boat owners
- `GET /api/admin/boat-owners/rejected` - List rejected boat owners

### 3. Boat Owner Profile Endpoints

- `GET /api/boat-owner/profile` - Get boat owner profile
- `PUT /api/boat-owner/profile` - Update boat owner profile
- `POST /api/boat-owner/upload-documents` - Upload verification documents

### 3. Boat Management Endpoints

- `GET /api/boats` - List user's boats
- `POST /api/boats` - Create new boat
- `PUT /api/boats/:id` - Update boat information
- `DELETE /api/boats/:id` - Delete boat

## User Experience Flow

### 1. New Boat Owner Registration Journey

```mermaid
sequenceDiagram
    participant User as New Boat Owner
    participant Frontend as Frontend App
    participant Backend as Backend API
    participant DB as Database
    participant EmailService as Email Service
    participant Admin as Admin

    User->>Frontend: Click 'Boat Owner' in navbar
    User->>Frontend: Click 'Sign In' in dropdown
    Frontend->>User: Show BoatOwnerSignInModal
    User->>Frontend: Click 'Sign up' link
    Frontend->>User: Redirect to /boat-owner/signup
    
    User->>Frontend: Fill registration form with business details
    Frontend->>Backend: POST /api/auth/boat-owner/register
    Backend->>DB: Create User + Profile + Provider (all pending)
    Backend->>EmailService: Send verification email to boat owner
    Backend-->>Frontend: Registration success
    Frontend-->>User: Show success message
    
    User->>EmailService: Click verification link in email
    EmailService->>Backend: Verify email token
    Backend->>DB: Set emailVerified: true
    Backend->>EmailService: Send admin notification email
    Backend-->>User: Email verified confirmation
    
    EmailService->>Admin: Admin receives approval notification
    Admin->>Frontend: Access admin dashboard
    Admin->>Backend: GET /api/admin/boat-owners/pending
    Backend-->>Admin: List of pending approvals
    Admin->>Backend: POST /api/admin/boat-owners/:id/approve
    Backend->>DB: Set isApproved: true, isActive: true
    Backend->>EmailService: Send approval email to boat owner
    
    User->>EmailService: Receive approval email
    User->>Frontend: Access sign-in modal
    User->>Backend: POST /api/auth/boat-owner/login
    Backend-->>Frontend: JWT tokens + user data
    Frontend-->>User: Redirect to boat owner dashboard
```

### 2. Existing Approved Boat Owner Sign-In Journey

```mermaid
sequenceDiagram
    participant User as Approved Boat Owner
    participant Frontend as Frontend App
    participant Backend as Backend API
    participant DB as Database

    User->>Frontend: Click 'Boat Owner' in navbar
    User->>Frontend: Click 'Sign In' in dropdown
    Frontend->>User: Show BoatOwnerSignInModal
    User->>Frontend: Enter credentials
    Frontend->>Backend: POST /api/auth/boat-owner/login
    Backend->>DB: Validate credentials + approval status
    Backend->>DB: Update lastLoginAt
    Backend-->>Frontend: JWT tokens + user data
    Frontend-->>User: Redirect to boat owner dashboard
```

### 3. Admin Approval Process

```mermaid
sequenceDiagram
    participant BoatOwner as Boat Owner
    participant Backend as Backend API
    participant EmailService as Email Service
    participant Admin as Admin
    participant DB as Database

    BoatOwner->>Backend: Email verification completed
    Backend->>EmailService: Send admin notification
    EmailService->>Admin: New boat owner approval needed
    
    Admin->>Backend: GET /api/admin/boat-owners/pending
    Backend->>DB: Query pending approvals
    Backend-->>Admin: List of pending boat owners
    
    alt Approve Account
        Admin->>Backend: POST /api/admin/boat-owners/:id/approve
        Backend->>DB: Set isApproved: true, approvedAt: now()
        Backend->>EmailService: Send approval email
        EmailService->>BoatOwner: Account approved notification
    else Reject Account
        Admin->>Backend: POST /api/admin/boat-owners/:id/reject
        Backend->>DB: Set rejection reason
        Backend->>EmailService: Send rejection email
        EmailService->>BoatOwner: Account rejected with reason
    end
```

## Error Handling and Edge Cases

### 1. Registration Error Scenarios

- **Duplicate Email**: "An account with this email already exists. Please sign in instead."
- **Weak Password**: Real-time validation with specific requirements
- **Invalid BRN Format**: "Please enter a valid Business Registration Number"
- **Invalid Phone**: Malaysia phone number format validation
- **Missing Business Information**: "Company name and BRN are required for boat owner registration"
- **Network Errors**: Retry mechanism with user feedback

### 2. Sign-In Error Scenarios

- **Account Not Found**: "Invalid email or password" (generic message for security)
- **Email Not Verified**: "Please verify your email address first. Check your inbox for the verification email."
- **Pending Admin Approval**: "Your account is pending admin approval. You will receive an email once approved."
- **Account Rejected**: "Your account application was not approved. Reason: [rejection_reason]"
- **Account Locked**: Clear message with unlock timeframe
- **Invalid Credentials**: Generic error message for security

### 3. Admin Approval Error Scenarios

- **Missing Business Documents**: Request for additional verification
- **Invalid Business Registration**: "Business registration could not be verified"
- **Duplicate Business Registration**: "This business is already registered with another account"
- **Incomplete Application**: "Additional information required for approval"

### 4. Email Delivery Issues

- **Verification Email Not Received**: Resend option with rate limiting
- **Admin Notification Failure**: Fallback notification system
- **Approval Email Delivery**: Multiple delivery attempts with tracking

## Integration Points

### 1. External Services

- **Google OAuth**: Identity provider integration
- **Email Service**: SMTP configuration for notifications
- **File Storage**: Image upload and storage for provider assets
- **Payment Gateway**: Future integration for subscription billing

### 2. Internal Services

- **User Management**: Core authentication and authorization
- **Provider Management**: Business profile and verification
- **Boat Management**: Asset registration and management
- **Booking System**: Service availability and reservations

## Security Considerations

### 1. Data Protection

- **Personal Information**: Encrypted storage of sensitive data
- **Business Information**: Secure handling of BRN and licenses
- **Authentication Tokens**: JWT security best practices
- **Session Management**: Secure session handling and expiry

### 2. Access Control

- **Role-Based Permissions**: Strict enforcement of boat owner privileges
- **Resource Ownership**: Users can only access their own data
- **Admin Overrides**: Super admin access for platform management
- **API Rate Limiting**: Protection against abuse and attacks

## Monitoring and Analytics

### 1. Registration Metrics

- Registration completion rates by method (manual vs Google OAuth)
- Provider profile completion rates
- Time to complete onboarding process
- Common abandonment points in the flow

### 2. Authentication Metrics

- Login success/failure rates
- Account lockout frequency
- Password reset requests
- Session duration and activity patterns

## Future Enhancements

### 1. Enhanced Admin Dashboard

- **Batch Approval**: Approve multiple boat owners at once
- **Advanced Filtering**: Filter by registration date, business type, location
- **Auto-Approval Rules**: Automatic approval based on business verification
- **Approval Analytics**: Track approval rates and processing times

### 2. Business Verification Integration

- **BRN Verification API**: Real-time business registration validation
- **Document Upload**: Support for business licenses and certificates
- **Address Verification**: Integration with postal services
- **Credit Check Integration**: Financial verification for boat owners

### 3. Enhanced Communication

- **In-App Notifications**: Real-time approval status updates
- **SMS Notifications**: Critical updates via SMS
- **Video Call Verification**: Optional video interviews for verification
- **Live Chat Support**: Direct communication with approval team

### 4. Advanced Security

- **Two-Factor Authentication**: SMS or app-based 2FA for boat owners
- **Device Management**: Track and manage signed-in devices
- **Suspicious Activity Detection**: Automated security monitoring
- **IP Whitelisting**: Restrict access to specific locations

### 5. Improved User Experience

- **Progressive Registration**: Save progress and resume later
- **Mobile-Optimized Flow**: Dedicated mobile registration experience
- **Multilingual Support**: Registration forms in multiple languages
- **Help Documentation**: Comprehensive guides for boat owners

---

## Implementation Priorities

### 🔴 Critical Missing Components (Must Build)

#### 1. Database Schema Updates
- **User Model**: Add boat owner approval fields (`isApproved`, `approvedAt`, `approvedBy`, `rejectionReason`)
- **Provider Model**: Remove `address` field from schema
- **Migration**: Create database migration to implement these changes

#### 2. New API Endpoints
- `POST /api/auth/boat-owner/register` - Boat owner registration
- `POST /api/auth/boat-owner/login` - Boat owner authentication
- `GET /api/admin/boat-owners/pending` - List pending approvals
- `POST /api/admin/boat-owners/:id/approve` - Approve boat owner
- `POST /api/admin/boat-owners/:id/reject` - Reject boat owner

#### 3. Email System Enhancements
- Admin notification email templates
- Approval/rejection email templates
- Email-to-dashboard deep linking functionality
- Admin notification triggers upon email verification

#### 4. Frontend Components
- `BoatOwnerSignInModal.js` - Dedicated sign-in modal
- `/pages/boat-owner/signup.js` - Registration page
- Admin dashboard boat owner approval section
- Email verification and approval status pages

### 🟡 Existing Infrastructure to Extend

#### 1. Admin Dashboard (/admin/dashboard)
- ✅ **Already Exists**: Comprehensive admin interface
- ✅ **Already Exists**: User management with BOAT_OWNER role filtering
- ✅ **Already Exists**: Provider verification functionality
- ✅ **Already Exists**: Boat status management with approval workflows
- ✅ **Already Exists**: Role-based filtering and pagination
- 🔧 **Needs Extension**: Boat owner approval section
- 🔧 **Needs Extension**: Email-to-dashboard deep links

#### 2. Authentication System
- ✅ **Already Exists**: JWT token management
- ✅ **Already Exists**: Role-based access control
- ✅ **Already Exists**: Email verification system
- ✅ **Already Exists**: Password security validation
- 🔧 **Needs Extension**: BOAT_OWNER approval status checking
- 🔧 **Needs Extension**: Admin approval notification triggers

#### 3. Provider Management
- ✅ **Already Exists**: Provider model and CRUD operations
- ✅ **Already Exists**: Provider verification workflow
- ✅ **Already Exists**: Business information handling
- 🔧 **Needs Modification**: Remove address field dependency
- 🔧 **Needs Modification**: Use profile address instead

### 🟢 Ready to Use (No Changes Required)

#### 1. User Profile System
- ✅ Comprehensive address fields (`address1`, `address2`, `city`, `postcode`, `state`)
- ✅ Business information fields (`companyName`, `brn`)
- ✅ Contact information management
- ✅ Profile completion tracking

#### 2. Email Infrastructure
- ✅ SMTP configuration and email service
- ✅ Email template system
- ✅ Email verification workflow
- ✅ Email delivery and retry mechanisms

#### 3. Security Framework
- ✅ Password hashing and validation
- ✅ Account locking mechanisms
- ✅ Session management
- ✅ API rate limiting

### 📋 Implementation Sequence

#### Phase 1: Database and Backend (Priority 1)
1. Create database migration for User and Provider model changes
2. Implement boat owner registration API endpoint
3. Implement boat owner login API endpoint with approval checking
4. Create admin approval API endpoints
5. Update provider creation logic to remove address dependency

#### Phase 2: Admin Dashboard Integration (Priority 2)
1. Extend existing admin dashboard with boat owner approval section
2. Implement email-to-dashboard deep linking
3. Add approval/rejection actions to user management interface
4. Integrate with existing filtering and pagination systems

#### Phase 3: Frontend Components (Priority 3)
1. Create BoatOwnerSignInModal component
2. Build boat owner registration page
3. Update navbar with boat owner menu
4. Implement approval status pages and messaging

#### Phase 4: Email System Integration (Priority 4)
1. Create admin notification email templates
2. Implement approval/rejection email templates
3. Add email-to-dashboard link generation
4. Configure automatic admin notifications

#### Phase 5: Testing and Refinement (Priority 5)
1. End-to-end testing of registration flow
2. Admin approval workflow testing
3. Email delivery and linking testing
4. Security and validation testing

---

## Summary of Key Design Changes

### 1. **Separate Authentication Flow**
- Dedicated boat owner sign-in modal accessed from navbar
- Separate full-page registration at `/boat-owner/signup`
- No shared components with customer authentication
- Clear distinction between user types

### 2. **Manual Email-Only Authentication**
- Removed Google OAuth integration completely
- Enhanced security through manual registration
- Better control over business verification process
- Professional focus for business users

### 3. **Admin Approval Workflow**
- Two-step verification: email + admin approval
- Automatic admin notification upon email verification
- Pending account state until approval
- Rejection handling with reason tracking

### 4. **Enhanced User Experience**
- Clear user journey without confusion
- Professional registration form with business details
- Status tracking throughout approval process
- Dedicated error handling for each scenario

### 5. **Database Schema Updates Required**
- **User Model**: Add `isApproved`, `approvedAt`, `approvedBy`, `rejectionReason` fields to User model
- **Provider Model**: Remove `address` field to leverage user profile address instead
- **Admin Dashboard**: Extend existing dashboard with boat owner approval functionality
- **Authentication Middleware**: Update to check approval status for BOAT_OWNER role
- **Email System**: Add email-to-dashboard deep linking for admin approval process

### 6. **Address Field Consolidation**
**Current Approach:**
- Provider model currently has separate `address` field
- User profile already contains comprehensive address fields: `address1`, `address2`, `city`, `postcode`, `state`

**Updated Approach:**
- Remove `address` field from Provider model
- Leverage existing user profile address fields for all address-related information
- Update provider creation and display logic to reference `user.profile.address*` fields
- Maintain data consistency by using single source of truth for address information

### 7. **Admin Dashboard Enhancement Strategy**
**Leveraging Existing Infrastructure:**
- Current admin dashboard already supports user management with BOAT_OWNER role filtering
- Existing provider verification functionality can be extended for boat owner approval
- Boat status management already includes approval workflows (APPROVED, PENDING_APPROVAL, REJECTED)
- Role-based filtering and pagination systems are already implemented

**Integration Plan:**
- Add new "Boat Owner Approvals" section to existing dashboard tabs
- Extend current user management with approval-specific actions
- Create email-to-dashboard deep links using existing authentication and routing
- Integrate with existing notification and status management systems

This comprehensive design ensures a professional, secure, and admin-controlled onboarding process specifically tailored for boat owners while maintaining clear separation from the customer authentication system.

---