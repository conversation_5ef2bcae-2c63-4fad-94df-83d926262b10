# GoSea Platform - Current Database Schema Analysis

## Overview
This document provides a comprehensive analysis of the existing GoSea platform database schema, including all tables, relationships, and current limitations.

## Database Technology
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Schema Location**: `backend/prisma/schema.prisma`

## Current Table Structure

### Core User Management

#### 1. Users Table (`users`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Unique user identifier |
| email | String | UNIQUE, NOT NULL | User email address |
| role | UserRole | NOT NULL | User role (CUSTOMER, BOAT_OWNER, AFFILIATE_AGENT, ADMIN) |
| isActive | Boolean | DEFAULT true | Account status |
| password | String | NULLABLE | Hashed password (null for OAuth users) |
| googleId | String | UNIQUE, NULLABLE | Google OAuth identifier |
| emailVerified | Boolean | DEFAULT false | Email verification status |
| emailVerifiedAt | DateTime | NULLABLE | Email verification timestamp |
| lastLoginAt | DateTime | NULLABLE | Last login timestamp |
| loginAttempts | Int | DEFAULT 0 | Failed login attempts counter |
| lockedUntil | DateTime | NULLABLE | Account lock expiration |
| createdAt | DateTime | DEFAULT now() | Account creation timestamp |
| updatedAt | DateTime | AUTO UPDATE | Last update timestamp |

**Relationships:**
- One-to-Many: boats (as owner)
- One-to-Many: bookings (as customer)
- One-to-One: profile
- One-to-Many: userSessions, emailVerificationTokens, passwordResetTokens, affiliateLinks

#### 2. Profiles Table (`profiles`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Profile identifier |
| userId | String | FK, UNIQUE | Reference to users table |
| firstName | String | NOT NULL | User's first name |
| lastName | String | NOT NULL | User's last name |
| phone | String | NULLABLE | Contact phone number |
| profilePicture | String | NULLABLE | Profile image URL |
| dateOfBirth | DateTime | NULLABLE | User's birth date |
| address1 | String | NULLABLE | Primary address line |
| address2 | String | NULLABLE | Secondary address line |
| postcode | String | NULLABLE | Postal code |
| city | String | NULLABLE | City |
| state | String | NULLABLE | State/Province |
| emergencyContact | String | NULLABLE | Emergency contact information |
| companyName | String | NULLABLE | Company name (for boat owners) |
| brn | String | NULLABLE | Business registration number |
| agencyName | String | NULLABLE | Agency name (for affiliates) |
| language | String | DEFAULT "en" | Preferred language |
| profileCompletedAt | DateTime | NULLABLE | Profile completion timestamp |

### Boat Management

#### 3. Boats Table (`boats`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Boat identifier |
| ownerId | String | FK, NOT NULL | Reference to users table |
| name | String | NOT NULL | Boat name |
| description | String | NULLABLE | Boat description |
| serviceType | ServiceType | NOT NULL | Service type (SNORKELING, PASSENGER) |
| location | Location | NOT NULL | Operating location (REDANG, PERHENTIAN) |
| capacity | Int | NOT NULL | Maximum passenger capacity |
| basePrice | Decimal | NOT NULL | Base pricing |
| galleryImages | Json | NULLABLE | Array of image objects |
| packageDetails | Json | NULLABLE | Package information |
| itinerary | Json | NULLABLE | Activity schedule |
| includedItems | String[] | NULLABLE | Included items array |
| agePricing | Json | NULLABLE | Age-based pricing structure |
| status | BoatStatus | DEFAULT DRAFT | Approval status |
| isActive | Boolean | DEFAULT true | Active status |

**Relationships:**
- Many-to-One: owner (users)
- One-to-Many: photos, packages, amenities, availability, bookings

#### 4. Boat Photos Table (`boat_photos`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Photo identifier |
| boatId | String | FK, NOT NULL | Reference to boats table |
| url | String | NOT NULL | Image URL |
| caption | String | NULLABLE | Photo caption |
| order | Int | DEFAULT 0 | Display order |

#### 5. Boat Packages Table (`boat_packages`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Package identifier |
| boatId | String | FK, NOT NULL | Reference to boats table |
| name | String | NOT NULL | Package name |
| description | String | NULLABLE | Package description |
| price | Decimal | NOT NULL | Package price |
| duration | String | NULLABLE | Duration description |
| maxCapacity | Int | NULLABLE | Maximum capacity for package |
| isActive | Boolean | DEFAULT true | Package availability |

#### 6. Boat Amenities Table (`boat_amenities`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Amenity identifier |
| boatId | String | FK, NOT NULL | Reference to boats table |
| name | String | NOT NULL | Amenity name |
| description | String | NULLABLE | Amenity description |
| category | String | NULLABLE | Amenity category |

#### 7. Boat Availability Table (`boat_availability`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Availability identifier |
| boatId | String | FK, NOT NULL | Reference to boats table |
| date | DateTime | NOT NULL | Available date |
| isAvailable | Boolean | DEFAULT true | Availability status |
| availableSlots | Json | NULLABLE | Time slots with capacity |

**Unique Constraint:** (boatId, date)

### Booking Management

#### 8. Bookings Table (`bookings`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Booking identifier |
| customerId | String | FK, NOT NULL | Reference to users table |
| boatId | String | FK, NOT NULL | Reference to boats table |
| serviceType | ServiceType | NOT NULL | Booked service type |
| serviceDate | DateTime | NOT NULL | Service date |
| serviceTime | String | NOT NULL | Service time slot |
| passengerCount | Int | NOT NULL | Total passenger count |
| passengerBreakdown | Json | NULLABLE | Age-based passenger breakdown |
| totalAmount | Decimal | NOT NULL | Total booking amount |
| discountCode | String | NULLABLE | Applied discount code |
| discountAmount | Decimal | DEFAULT 0 | Discount amount |
| status | BookingStatus | DEFAULT PENDING | Booking status |
| contactName | String | NOT NULL | Contact person name |
| contactPhone | String | NOT NULL | Contact phone number |
| contactEmail | String | NOT NULL | Contact email |
| affiliateId | String | NULLABLE | Affiliate reference |
| commission | Decimal | NULLABLE | Affiliate commission |

**Relationships:**
- Many-to-One: customer (users), boat, affiliateLink, discount
- One-to-Many: payments, notifications

### Supporting Tables

#### 9. Payments Table (`payments`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Payment identifier |
| bookingId | String | FK, NOT NULL | Reference to bookings table |
| amount | Decimal | NOT NULL | Payment amount |
| currency | String | DEFAULT "MYR" | Payment currency |
| status | String | NOT NULL | Payment status |
| paymentMethod | String | NOT NULL | Payment method used |
| stripePaymentIntentId | String | NULLABLE | Stripe payment intent ID |
| paidAt | DateTime | NULLABLE | Payment completion timestamp |

#### 10. Discount Codes Table (`discount_codes`)
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | String | PK, CUID | Discount code identifier |
| code | String | UNIQUE, NOT NULL | Discount code |
| discountType | String | NOT NULL | Type (percentage/fixed) |
| discountValue | Decimal | NOT NULL | Discount value |
| minAmount | Decimal | NULLABLE | Minimum order amount |
| maxDiscount | Decimal | NULLABLE | Maximum discount amount |
| validFrom | DateTime | NULLABLE | Valid from date |
| validUntil | DateTime | NULLABLE | Valid until date |
| usageLimit | Int | NULLABLE | Usage limit |
| usedCount | Int | DEFAULT 0 | Current usage count |
| isActive | Boolean | DEFAULT true | Active status |

## Current Enums

### UserRole
- CUSTOMER
- BOAT_OWNER  
- AFFILIATE_AGENT
- ADMIN

### ServiceType
- SNORKELING
- PASSENGER

### Location
- REDANG
- PERHENTIAN

### BoatStatus
- DRAFT
- PENDING_APPROVAL
- APPROVED
- REJECTED
- INACTIVE

### BookingStatus
- PENDING
- CONFIRMED
- CANCELLED
- COMPLETED

## Current Limitations

### 1. **Oversimplified Location Model**
- Only supports 2 destinations (REDANG, PERHENTIAN)
- No support for departure jetties (Jetty Kampung Mangkuk, Jetty Merang, Jetty Penarik)
- No distinction between departure and destination points
- Cannot handle multiple destinations per service

### 2. **Limited Service Type Structure**
- Only 2 service types (SNORKELING, PASSENGER)
- No subcategories or service variations
- Cannot handle complex service offerings

### 3. **Provider vs Boat Confusion**
- Current model treats each boat as an individual entity
- No clear provider/operator concept
- Cannot group multiple boats under one operator
- Search results show individual boats instead of providers

### 4. **Inflexible Search Model**
- Location enum doesn't support jetty-based search
- No route or journey concept (departure → destination)
- Cannot filter by departure point

### 5. **Booking Workflow Issues**
- Service type selection not prioritized in booking flow
- No clear separation between service selection and boat selection
- Cannot handle provider-level service offerings

## Database Relationship Diagram

```mermaid
erDiagram
    Users ||--o{ Boats : owns
    Users ||--o{ Bookings : makes
    Users ||--o| Profiles : has
    
    Boats ||--o{ BoatPhotos : contains
    Boats ||--o{ BoatPackages : offers
    Boats ||--o{ BoatAmenities : has
    Boats ||--o{ BoatAvailability : schedules
    Boats ||--o{ Bookings : receives
    
    Bookings ||--o{ Payments : generates
    Bookings }o--|| DiscountCodes : applies
    
    Users {
        string id PK
        string email UK
        UserRole role
        boolean isActive
        string password
        string googleId UK
    }
    
    Boats {
        string id PK
        string ownerId FK
        string name
        ServiceType serviceType
        Location location
        int capacity
        decimal basePrice
        BoatStatus status
    }
    
    Bookings {
        string id PK
        string customerId FK
        string boatId FK
        ServiceType serviceType
        datetime serviceDate
        string serviceTime
        int passengerCount
        decimal totalAmount
        BookingStatus status
    }
```

## Current API Endpoints

### Boat Search
- `GET /api/boats/search` - Search boats with filters
- `GET /api/boats/:id` - Get boat details
- `GET /api/boats` - Get featured boats

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user

### Payments
- `POST /api/payments/create-intent` - Create payment intent
- `POST /api/payments/create-deposit-intent` - Create deposit payment

## Conclusion

The current database schema provides a solid foundation for basic boat booking functionality but has significant limitations for the complex boat service structure described in the requirements. The main issues are:

1. **Lack of provider/operator concept**
2. **Oversimplified location and service models**
3. **No support for jetty-based departure points**
4. **Limited service type categorization**
5. **Inflexible search and booking workflow**

The proposed redesign addresses these limitations by introducing proper provider management, flexible location handling, comprehensive service categorization, and jetty-based search functionality.
