# Google OAuth Setup Guide for GoSea Platform

This guide will help you set up Google OAuth authentication for the GoSea platform.

## Problem Overview

If you're seeing an error like:
- "400. That's an error. The server cannot process the request because it is malformed."
- "The OAuth client was not found"
- URL contains `client_id=your-google-client-id`

This means the Google OAuth credentials are not properly configured.

## Solution: Complete Setup Steps

### Step 1: Create Google Cloud Project and OAuth Credentials

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/

2. **Create or Select a Project**
   - Click on the project dropdown at the top
   - Click "New Project" or select an existing project
   - Give it a name like "GoSea Platform"

3. **Enable Google+ API** (if not already enabled)
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API"
   - Click "Enable"

4. **Configure OAuth Consent Screen**
   - Go to "APIs & Services" > "OAuth consent screen"
   - Choose User Type:
     - **External**: For testing with any Google account
     - **Internal**: Only for Google Workspace organization users
   - Click "Create"
   
   **App Information:**
   - App name: `GoSea Platform`
   - User support email: Your email
   - App logo: (optional)
   - Application home page: `http://localhost:3000` (development)
   - Application privacy policy link: (optional for testing)
   - Application terms of service link: (optional for testing)
   - Authorized domains: (leave empty for localhost testing)
   - Developer contact information: Your email
   
   **Scopes:**
   - Click "Add or Remove Scopes"
   - Add these scopes:
     - `userinfo.email`
     - `userinfo.profile`
     - `openid`
   - Click "Update" then "Save and Continue"
   
   **Test Users** (if using External):
   - Add email addresses of users who can test the OAuth flow
   - Click "Save and Continue"

5. **Create OAuth 2.0 Client ID**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client ID"
   - Application type: **Web application**
   - Name: `GoSea Web Client` (or your preferred name)
   
   **Authorized JavaScript origins:**
   - `http://localhost:3000` (frontend)
   - `http://localhost:5001` (backend API)
   - Add production URLs when deploying
   
   **Authorized redirect URIs:**
   - `http://localhost:5001/api/auth/google/callback` (REQUIRED for development)
   - Add production URL when deploying: `https://yourdomain.com/api/auth/google/callback`
   
   - Click "Create"

6. **Copy Your Credentials**
   - A dialog will appear with your Client ID and Client Secret
   - **IMPORTANT**: Copy both values immediately
   - You can also view them later from the Credentials page

### Step 2: Update Environment Variables

1. **Open your `.env` file** in the project root

2. **Replace the placeholder values** with your actual credentials:

```bash
# Google OAuth Configuration
GOOGLE_CLIENT_ID=YOUR_ACTUAL_CLIENT_ID_HERE
GOOGLE_CLIENT_SECRET=YOUR_ACTUAL_CLIENT_SECRET_HERE
GOOGLE_REDIRECT_URI=http://localhost:5001/api/auth/google/callback
```

3. **Also update `.env.local`** with the same values

4. **Keep these credentials secure**:
   - Never commit actual credentials to version control
   - Use different credentials for development and production
   - Store production credentials in secure environment variable management

### Step 3: Restart Your Application

After updating the environment variables, restart your backend server:

```bash
# If using Docker
docker-compose restart backend

# If running locally
# Stop the backend server (Ctrl+C) and start it again
cd backend
npm start
```

### Step 4: Test Google OAuth

1. **Open your application** at `http://localhost:3000`

2. **Click "Sign in with Google"** or "Sign up with Google"

3. **You should see**:
   - Google's OAuth consent screen
   - A list of permissions being requested
   - Option to select your Google account

4. **After authorizing**:
   - You'll be redirected back to your application
   - You should be logged in successfully

## Troubleshooting

### Error: "The OAuth client was not found"
- **Cause**: Using placeholder credentials
- **Fix**: Follow Step 1 and Step 2 above to set up real credentials

### Error: "Redirect URI mismatch"
- **Cause**: The redirect URI in your code doesn't match what's configured in Google Cloud Console
- **Fix**: Make sure you added `http://localhost:5001/api/auth/google/callback` to Authorized redirect URIs
- **Note**: The URI must match exactly (including http/https, port, and path)

### Error: "Access blocked: This app's request is invalid"
- **Cause**: OAuth consent screen not properly configured
- **Fix**: Complete the OAuth consent screen configuration (Step 1, part 4)

### Error: "This app isn't verified"
- **Cause**: Your app is in testing mode with External user type
- **Fix**: This is normal for development. Click "Advanced" > "Go to GoSea Platform (unsafe)" to continue
- **Production**: Submit your app for verification or use Internal user type

### Backend shows: "ERROR: GOOGLE_CLIENT_ID is not configured"
- **Cause**: Environment variables not loaded or still using placeholder values
- **Fix**: 
  1. Verify `.env` file has actual credentials (not placeholders)
  2. Restart the backend server
  3. Check that `ENABLE_GOOGLE_OAUTH=true` in `.env`

### OAuth works but user data not saved
- **Cause**: Database connection or schema issues
- **Fix**: 
  1. Check database connection in `.env`
  2. Run migrations: `cd backend && npx prisma migrate dev`
  3. Check backend logs for errors

## Production Deployment

When deploying to production:

1. **Create separate OAuth credentials** for production in Google Cloud Console

2. **Add production redirect URIs**:
   - `https://yourdomain.com/api/auth/google/callback`
   - `https://api.yourdomain.com/api/auth/google/callback` (if using separate API domain)

3. **Update production environment variables**:
   ```bash
   GOOGLE_CLIENT_ID=production_client_id
   GOOGLE_CLIENT_SECRET=production_client_secret
   GOOGLE_REDIRECT_URI=https://yourdomain.com/api/auth/google/callback
   ```

4. **Submit for OAuth verification** (if needed):
   - Go to OAuth consent screen in Google Cloud Console
   - Click "Publish App"
   - Submit for verification if you need more than 100 users

## Security Best Practices

1. **Never commit credentials** to version control
   - `.env` files should be in `.gitignore`
   - Use `.env.example` for documentation only

2. **Use different credentials** for each environment:
   - Development
   - Staging
   - Production

3. **Rotate credentials** periodically:
   - Create new OAuth client
   - Update environment variables
   - Delete old OAuth client

4. **Monitor OAuth usage**:
   - Check Google Cloud Console for unusual activity
   - Set up alerts for quota limits

5. **Limit OAuth scopes**:
   - Only request necessary permissions
   - Current scopes: email, profile, openid

## Additional Resources

- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Google Cloud Console](https://console.cloud.google.com/)
- [OAuth 2.0 Playground](https://developers.google.com/oauthplayground/) (for testing)

## Support

If you continue to experience issues:
1. Check the backend logs for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure the backend server has been restarted after updating `.env`
4. Check that `ENABLE_GOOGLE_OAUTH=true` in your `.env` file

