# Google OAuth Verification Checklist

## ✅ Backend Configuration - COMPLETE
- [x] Google Client ID is set in `.env`
- [x] Google Client Secret is set in `.env`
- [x] Backend container has loaded the credentials
- [x] No error messages in backend logs
- [x] `ENABLE_GOOGLE_OAUTH=true` is set

## 🔍 Next Step: Verify Google Cloud Console Configuration

### Required Redirect URI in Google Cloud Console

Your application is configured to use:
```
http://localhost:5001/api/auth/google/callback
```

**IMPORTANT**: This EXACT URI must be added to your Google Cloud Console OAuth 2.0 Client configuration.

### How to Verify:

1. **Go to Google Cloud Console**:
   - Visit: https://console.cloud.google.com/apis/credentials
   - Select your project

2. **Find your OAuth 2.0 Client ID**:
   - Look for the client with ID: `************-82n0mi419e33lp0ovrkotl65nlut5av8.apps.googleusercontent.com`
   - Click on it to edit

3. **Check "Authorized redirect URIs"**:
   - You should see: `http://localhost:5001/api/auth/google/callback`
   - If it's not there, click "ADD URI" and add it
   - **Important**: The URI must match EXACTLY (including http, port, and path)

4. **Also add (optional but recommended)**:
   - `http://localhost:3000/auth/google/callback` (frontend callback)

5. **Save the changes**

### Common Mistakes to Avoid:

❌ **Wrong**: `http://localhost:3000/auth/google/callback` (frontend port)
❌ **Wrong**: `http://localhost:5001/auth/google/callback` (missing /api)
❌ **Wrong**: `http://localhost:5000/api/auth/google/callback` (wrong port)
✅ **Correct**: `http://localhost:5001/api/auth/google/callback`

### Test the OAuth Flow:

1. **Open your application**: http://localhost:3000

2. **Click "Sign in with Google"** or **"Sign up with Google"**

3. **Expected behavior**:
   - You should be redirected to Google's OAuth consent screen
   - You'll see a list of permissions being requested
   - After authorizing, you should be redirected back to your application
   - You should be logged in successfully

4. **If you still see "invalid_client" error**:
   - Double-check the redirect URI in Google Cloud Console
   - Make sure you saved the changes in Google Cloud Console
   - Wait a few minutes for changes to propagate (Google's cache)
   - Try in an incognito/private browser window

### Debugging Tips:

**Check the error URL**: If you get an error, look at the URL in your browser. It will show:
- The client_id being used
- The redirect_uri being requested
- The error message

**Example error URL**:
```
https://accounts.google.com/signin/oauth/error?
  authError=...
  &client_id=************-82n0mi419e33lp0ovrkotl65nlut5av8.apps.googleusercontent.com
  &redirect_uri=http://localhost:5001/api/auth/google/callback
```

Compare the `redirect_uri` in the error URL with what you have in Google Cloud Console.

### Still Having Issues?

1. **Clear browser cache and cookies**
2. **Try in incognito/private mode**
3. **Check backend logs**: `docker compose logs backend -f`
4. **Verify the OAuth consent screen is configured** in Google Cloud Console
5. **Make sure your Google account is added as a test user** (if using External user type)

## Production Deployment

When deploying to production, remember to:
1. Create separate OAuth credentials for production
2. Add production redirect URI: `https://yourdomain.com/api/auth/google/callback`
3. Update `.env` with production credentials
4. Submit app for verification if needed (for more than 100 users)

