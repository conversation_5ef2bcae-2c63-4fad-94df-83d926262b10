# Gmail Setup Guide for GoSea Platform

## Current Configuration

The system is now configured to use the Gmail account: **<EMAIL>**

## Required: Generate Gmail App Password

To send emails through Gmail, you need to generate an App Password. Follow these steps:

### Step 1: Enable 2-Factor Authentication (if not already enabled)

1. Go to your Google Account: https://myaccount.google.com/
2. Click on "Security" in the left sidebar
3. Under "Signing in to Google", click on "2-Step Verification"
4. Follow the setup process if not already enabled

### Step 2: Generate App Password

1. Go to your Google Account: https://myaccount.google.com/
2. Click on "Security" in the left sidebar
3. Under "Signing in to Google", click on "App passwords"
4. You might need to sign in again
5. Click "Select app" and choose "Mail"
6. Click "Select device" and choose "Other (Custom name)"
7. Enter "GoSea Platform" as the name
8. Click "Generate"
9. **Copy the 16-character password** (it will look like: `abcd efgh ijkl mnop`)

### Step 3: Update Environment Variables

Replace `your-gmail-app-password` in both `.env` and `.env.local` files with the generated app password:

```bash
GMAIL_APP_PASSWORD=abcd efgh ijkl mnop
```

**Important**: Use the app password exactly as generated (with or without spaces - both work).

### Step 4: Restart Backend

After updating the app password, restart the backend:

```bash
docker compose restart backend
```

## Verification

After setup, you can verify the email configuration by:

1. **Check backend logs** for email initialization:
   ```bash
   docker compose logs backend | grep -i email
   ```
   
   You should see: `Email transporter initialized with Gmail (production mode)`

2. **Test by creating a new boat owner account** - you should receive the verification email in the actual Gmail inbox.

## Current Email Settings

- **Gmail Account**: <EMAIL>
- **From Address**: <EMAIL>
- **Mode**: Real Email (USE_REAL_EMAIL=true)
- **App Password**: [TO BE CONFIGURED]

## Troubleshooting

### "Invalid login" or "Authentication failed"
- **Cause**: Incorrect app password or 2FA not enabled
- **Fix**: 
  1. Ensure 2-Factor Authentication is enabled
  2. Generate a new app password
  3. Update `.env` and `.env.local` files
  4. Restart backend

### "Less secure app access"
- **Cause**: Trying to use regular password instead of app password
- **Fix**: Use App Password, not your regular Gmail password

### Still receiving emails in MailHog
- **Cause**: Backend not restarted or environment variables not loaded
- **Fix**: 
  1. Verify `USE_REAL_EMAIL=true` in both `.env` files
  2. Restart backend: `docker compose restart backend`
  3. Check logs: `docker compose logs backend | grep -i email`

### Emails going to spam
- **Cause**: Gmail's spam filters
- **Fix**: 
  1. Check spam folder
  2. Mark as "Not Spam"
  3. Add <EMAIL> to contacts

## Testing

After configuration:

1. **Create a new boat owner account**
2. **Check the Gmail inbox** (<EMAIL>) for verification email
3. **Click the verification link** in the email
4. **Account should be verified** and admin notification sent

## Security Notes

- **Never commit the app password** to version control
- **Use different app passwords** for different applications
- **Revoke app passwords** when no longer needed
- **Monitor Gmail security** for unusual activity

## Production Considerations

For production deployment:
- Consider using a dedicated email service (SendGrid, Mailgun, etc.)
- Use environment-specific email addresses
- Implement email rate limiting
- Set up email delivery monitoring
- Configure proper SPF/DKIM records for better deliverability
