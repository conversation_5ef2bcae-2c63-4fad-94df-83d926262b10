# Google OAuth Quick Fix Guide

## 🚨 Error: "The OAuth client was not found"

### Quick Diagnosis
Check your `.env` file. If you see this:
```bash
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```
**These are placeholder values!** You need real credentials.

---

## ⚡ Quick Fix (5 minutes)

### 1. Get Google OAuth Credentials
1. Go to: https://console.cloud.google.com/apis/credentials
2. Create Project (if needed)
3. Click "Create Credentials" > "OAuth 2.0 Client ID"
4. Configure consent screen if prompted:
   - User Type: External
   - App name: GoSea
   - Add your email
   - Scopes: email, profile, openid
5. Create OAuth Client:
   - Type: Web application
   - Authorized redirect URIs: `http://localhost:5001/api/auth/google/callback`
6. Copy the Client ID and Client Secret

### 2. Update .env File
Replace these lines in your `.env` file:
```bash
GOOGLE_CLIENT_ID=paste_your_actual_client_id_here
GOOGLE_CLIENT_SECRET=paste_your_actual_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:5001/api/auth/google/callback
```

### 3. Restart Backend
```bash
# If using Docker
docker-compose restart backend

# If running locally
# Stop the server (Ctrl+C) and restart it
```

### 4. Test
- Go to http://localhost:3000
- Click "Sign in with Google"
- Should work now! ✅

---

## 🔍 Other Common Errors

### "Redirect URI mismatch"
**Fix**: Add this exact URI to Google Cloud Console:
```
http://localhost:5001/api/auth/google/callback
```

### "Access blocked: This app's request is invalid"
**Fix**: Complete OAuth consent screen configuration in Google Cloud Console

### Backend logs: "GOOGLE_CLIENT_ID is not configured"
**Fix**: 
1. Check `.env` has real credentials (not placeholders)
2. Restart backend server
3. Verify `ENABLE_GOOGLE_OAUTH=true`

---

## 📋 Checklist

- [ ] Created Google Cloud Project
- [ ] Created OAuth 2.0 Client ID
- [ ] Added redirect URI: `http://localhost:5001/api/auth/google/callback`
- [ ] Copied Client ID and Client Secret
- [ ] Updated `.env` with real credentials
- [ ] Updated `.env.local` with real credentials
- [ ] Restarted backend server
- [ ] Tested Google sign-in

---

## 📚 Need More Help?
See the complete guide: `docs/GOOGLE_OAUTH_SETUP.md`

