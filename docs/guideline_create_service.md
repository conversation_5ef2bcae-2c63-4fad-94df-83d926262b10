# GoSea Service Creation Guidelines

## Overview
This comprehensive guide provides step-by-step instructions for creating a complete bookable service in the GoSea platform. Follow these steps to ensure proper setup of all required components and database relationships.

## Prerequisites
- GoSea platform running locally or on server
- Database access (PostgreSQL)
- Admin or Provider account access
- Understanding of the GoSea booking system architecture

## Table of Contents
1. [User Setup](#1-user-setup)
2. [Provider Registration](#2-provider-registration)
3. [Boat Registration](#3-boat-registration)
4. [Service Categories & Types](#4-service-categories--types)
5. [Package Creation](#5-package-creation)
6. [Age Range Configuration](#6-age-range-configuration)
7. [Service Definition](#7-service-definition)
8. [Route Configuration](#8-route-configuration)
9. [Pricing Configuration](#9-pricing-configuration)
10. [Scheduling Setup](#10-scheduling-setup)
11. [Testing & Validation](#11-testing--validation)

---

## 1. User Setup

### 1.1 Create Boat Owner Account
```sql
-- Create user with BOAT_OWNER role
INSERT INTO users (id, email, role, emailVerified, createdAt, updatedAt)
VALUES (
  'user_boat_owner_001',
  '<EMAIL>',
  'BOAT_OWNER',
  true,
  NOW(),
  NOW()
);
```

### 1.2 Complete User Profile
```sql
-- Create profile for the boat owner
INSERT INTO profiles (
  id, userId, firstName, lastName, phone, 
  companyName, language, createdAt, updatedAt
)
VALUES (
  'profile_001',
  'user_boat_owner_001',
  'John',
  'Doe',
  '+***********',
  'Marine Adventures Sdn Bhd',
  'en',
  NOW(),
  NOW()
);
```

### 1.3 Validation Requirements
- ✅ Email must be unique
- ✅ Role must be 'BOAT_OWNER' for service providers
- ✅ Profile must include firstName, lastName, and phone
- ✅ Company name required for business operations

---

## 2. Provider Registration

### 2.1 Create Provider Entity
```sql
-- Create provider company
INSERT INTO providers (
  id, userId, companyName, displayName, description,
  contactPhone, contactEmail, isActive, createdAt, updatedAt
)
VALUES (
  'provider_marine_001',
  'user_boat_owner_001',
  'Marine Adventures Sdn Bhd',
  'Marine Adventures',
  'Premium marine experiences in Malaysian waters',
  '+***********',
  '<EMAIL>',
  true,
  NOW(),
  NOW()
);
```

### 2.2 Set Operating Areas
```sql
-- Link provider to operating jetties
INSERT INTO provider_operating_areas (id, providerId, jettyId, createdAt, updatedAt)
VALUES 
  ('poa_001', 'provider_marine_001', 'jetty_kuala_terengganu', NOW(), NOW()),
  ('poa_002', 'provider_marine_001', 'jetty_kuala_besut', NOW(), NOW());
```

### 2.3 Validation Requirements
- ✅ Provider must be linked to a valid user with BOAT_OWNER role
- ✅ Company name and display name are required
- ✅ Contact information must be provided
- ✅ At least one operating area must be defined

---

## 3. Boat Registration

### 3.1 Register Boat
```sql
-- Create boat entry
INSERT INTO boats (
  id, ownerId, providerId, name, description, capacity,
  status, registrationNumber, yearBuilt, engineType,
  isActive, createdAt, updatedAt
)
VALUES (
  'boat_speedboat_001',
  'user_boat_owner_001',
  'provider_marine_001',
  'Ocean Explorer',
  'High-speed boat perfect for island hopping',
  12,
  'APPROVED',
  'MY-TR-2024-001',
  2023,
  'Twin Outboard 250HP',
  true,
  NOW(),
  NOW()
);
```

### 3.2 Add Boat Amenities
```sql
-- Add boat amenities
INSERT INTO boat_amenities (id, boatId, name, icon)
VALUES 
  ('amenity_001', 'boat_speedboat_001', 'Life Jackets', 'life-jacket'),
  ('amenity_002', 'boat_speedboat_001', 'Snorkeling Gear', 'mask'),
  ('amenity_003', 'boat_speedboat_001', 'First Aid Kit', 'medical'),
  ('amenity_004', 'boat_speedboat_001', 'Cooler Box', 'cooler');
```

### 3.3 Validation Requirements
- ✅ Boat must be owned by a valid BOAT_OWNER user
- ✅ Boat must be assigned to a provider
- ✅ Capacity must be greater than 0
- ✅ Status must be 'APPROVED' for bookable services
- ✅ Registration number should be unique

---

## 4. Service Categories & Types

### 4.1 Verify Service Categories
```sql
-- Check existing service categories
SELECT * FROM service_categories WHERE isActive = true;

-- Example categories that should exist:
-- 'ISLAND_HOPPING', 'SNORKELING', 'DIVING', 'FISHING', 'SUNSET_CRUISE'
```

### 4.2 Verify Service Types
```sql
-- Check existing service types
SELECT st.*, sc.name as category_name 
FROM service_types st
JOIN service_categories sc ON st.categoryId = sc.id
WHERE st.isActive = true;

-- Example service types:
-- 'HALF_DAY_SNORKELING', 'FULL_DAY_ISLAND_HOPPING', 'SUNSET_CRUISE'
```

### 4.3 Create Custom Service Type (if needed)
```sql
-- Create new service type if required
INSERT INTO service_types (
  id, categoryId, name, code, description,
  defaultDuration, requiresRoute, isActive, createdAt, updatedAt
)
VALUES (
  'st_premium_snorkeling',
  'sc_snorkeling',
  'Premium Snorkeling Experience',
  'PREMIUM_SNORKELING',
  'Premium snorkeling with professional guide and equipment',
  240, -- 4 hours in minutes
  true,
  true,
  NOW(),
  NOW()
);
```

---

## 5. Package Creation

### 5.1 Verify Package Types
```sql
-- Check existing package types
SELECT * FROM package_types WHERE isActive = true ORDER BY sortOrder;

-- Standard package types: BASIC, STANDARD, PREMIUM, DELUXE
```

### 5.2 Create Service Packages
```sql
-- Create service packages for different tiers
INSERT INTO service_packages (
  id, serviceId, packageTypeId, basePrice, priceModifier,
  includedItems, excludedItems, isActive, createdAt, updatedAt
)
VALUES 
  -- Basic Package
  ('sp_basic_001', 'ps_snorkeling_001', 'pt_basic', 80.00, 1.0,
   ARRAY['Snorkeling mask', 'Life jacket', 'Bottled water'],
   ARRAY['Lunch', 'Underwater camera'],
   true, NOW(), NOW()),
  
  -- Premium Package  
  ('sp_premium_001', 'ps_snorkeling_001', 'pt_premium', 120.00, 1.5,
   ARRAY['Premium snorkeling gear', 'Life jacket', 'Lunch', 'Bottled water', 'Towel'],
   ARRAY['Underwater camera', 'Professional photos'],
   true, NOW(), NOW());
```

---

## 6. Age Range Configuration

### 6.1 Verify Age Categories
```sql
-- Check existing age categories
SELECT * FROM age_categories WHERE isActive = true ORDER BY sortOrder;

-- Standard categories: ADULTS, CHILDREN, TODDLERS, SENIORS, PWD
```

### 6.2 Configure Service Age Ranges
```sql
-- Define age ranges for the service
INSERT INTO service_age_ranges (
  id, serviceId, ageCategoryId, minAge, maxAge, isActive, createdAt, updatedAt
)
VALUES 
  ('sar_001', 'ps_snorkeling_001', 'ac_adults', 13, NULL, true, NOW(), NOW()),
  ('sar_002', 'ps_snorkeling_001', 'ac_children', 3, 12, true, NOW(), NOW()),
  ('sar_003', 'ps_snorkeling_001', 'ac_toddlers', 0, 2, true, NOW(), NOW());
```

### 6.3 Configure Age-Based Pricing
```sql
-- Set pricing for different age groups
INSERT INTO service_age_pricing (
  id, serviceId, ageCategoryId, price, priceType, isActive, createdAt, updatedAt
)
VALUES
  ('sap_001', 'ps_snorkeling_001', 'ac_adults', 80.00, 'FIXED', true, NOW(), NOW()),
  ('sap_002', 'ps_snorkeling_001', 'ac_children', 60.00, 'FIXED', true, NOW(), NOW()),
  ('sap_003', 'ps_snorkeling_001', 'ac_toddlers', 0.00, 'FIXED', true, NOW(), NOW());
```

### 6.4 Age Restriction Best Practices

**Important**: Only create age ranges for categories that your service actually supports. The system will:
- Show **only configured age categories** for services with specific age ranges
- Show **all default age categories** for services without specific age restrictions

**Common Age Restriction Scenarios:**

1. **Diving Services** (Safety restrictions - 12+ years only):
```sql
-- Only adults and 12-year-olds allowed
INSERT INTO service_age_ranges (id, serviceId, ageCategoryId, minAge, maxAge, isActive, createdAt, updatedAt)
VALUES
  ('sar_diving_adults', 'service_id', 'ac_adults', 13, NULL, true, NOW(), NOW()),
  ('sar_diving_youth', 'service_id', 'ac_children', 12, 12, true, NOW(), NOW());
```

2. **Family-Friendly Services** (Adults, children, toddlers):
```sql
-- Family snorkeling - all ages welcome
INSERT INTO service_age_ranges (id, serviceId, ageCategoryId, minAge, maxAge, isActive, createdAt, updatedAt)
VALUES
  ('sar_family_adults', 'service_id', 'ac_adults', 13, NULL, true, NOW(), NOW()),
  ('sar_family_children', 'service_id', 'ac_children', 3, 12, true, NOW(), NOW()),
  ('sar_family_toddlers', 'service_id', 'ac_toddlers', 0, 2, true, NOW(), NOW());
```

3. **No Age Restrictions** (Fishing, sunset cruises):
```sql
-- Don't create any service_age_ranges entries
-- System will automatically show all 5 default age categories
```

---

## 7. Service Definition

### 7.1 Create Provider Service
```sql
-- Create the main service
INSERT INTO provider_services (
  id, providerId, serviceTypeId, name, description,
  basePrice, duration, maxCapacity, includedItems, excludedItems,
  specialInstruction, isActive, createdAt, updatedAt
)
VALUES (
  'ps_snorkeling_001',
  'provider_marine_001',
  'st_premium_snorkeling',
  'Premium Coral Reef Snorkeling',
  'Explore pristine coral reefs with professional guides and premium equipment',
  80.00,
  240, -- 4 hours
  12,
  ARRAY['Snorkeling equipment', 'Life jackets', 'Professional guide', 'Bottled water'],
  ARRAY['Lunch', 'Transportation to jetty', 'Underwater photography'],
  'Please bring swimwear, towel, and sunscreen. No prior snorkeling experience required.',
  true,
  NOW(),
  NOW()
);
```

### 7.2 Assign Boat to Service
```sql
-- Link boat to service
INSERT INTO service_assignments (
  id, serviceId, boatId, isPrimary, isActive, assignedAt
)
VALUES (
  'sa_001',
  'ps_snorkeling_001',
  'boat_speedboat_001',
  true,
  true,
  NOW()
);
```

---

## 8. Route Configuration

### 8.1 Verify Routes Exist
```sql
-- Check available routes from operating jetties
SELECT r.*, j.name as jetty_name, d.name as destination_name
FROM routes r
JOIN jetties j ON r.departureJettyId = j.id
JOIN destinations d ON r.destinationId = d.id
WHERE r.isActive = true;
```

### 8.2 Link Service to Routes
```sql
-- Connect service to available routes
INSERT INTO service_routes (
  id, serviceId, routeId, priceModifier, isActive, createdAt, updatedAt
)
VALUES 
  ('sr_001', 'ps_snorkeling_001', 'route_terengganu_redang', 1.0, true, NOW(), NOW()),
  ('sr_002', 'ps_snorkeling_001', 'route_besut_perhentian', 1.2, true, NOW(), NOW());
```

---

## 9. Pricing Configuration

### 9.1 Dynamic Pricing Setup
The GoSea system supports four pricing scenarios:

1. **Basic**: Single passenger field only
2. **Package-Only**: Packages + single passenger field  
3. **Age-Based**: Age breakdown, no packages
4. **Full Variation**: Packages + age breakdown

### 9.2 Pricing Scenario Detection
The system automatically detects the pricing scenario based on:
- Presence of service packages
- Presence of age-based pricing
- Service configuration

### 9.3 Price Calculation Logic
```javascript
// Example pricing calculation
const calculatePrice = (service, passengers, selectedPackage) => {
  let basePrice = service.basePrice;
  
  // Apply package modifier if selected
  if (selectedPackage) {
    basePrice = selectedPackage.basePrice * selectedPackage.priceModifier;
  }
  
  // Apply age-based pricing if configured
  if (service.serviceAgePricing.length > 0) {
    // Calculate based on age breakdown
    return calculateAgeBasedPrice(service, passengers);
  }
  
  // Simple passenger count multiplication
  return basePrice * passengers.total;
};
```

---

## 10. Scheduling Setup

### 10.1 Create Service Schedules
```sql
-- Create recurring schedules
INSERT INTO service_schedules (
  id, serviceId, dayOfWeek, departureTime, availableCapacity, isActive, createdAt, updatedAt
)
VALUES 
  -- Daily morning departure
  ('ss_001', 'ps_snorkeling_001', NULL, '09:00', 12, true, NOW(), NOW()),
  -- Daily afternoon departure  
  ('ss_002', 'ps_snorkeling_001', NULL, '14:00', 12, true, NOW(), NOW());
```

### 10.2 Schedule Configuration Notes
- `dayOfWeek = NULL` means the schedule runs every day (0-6)
- `dayOfWeek = 0` is Sunday, `dayOfWeek = 6` is Saturday
- `availableCapacity` should not exceed boat capacity
- Multiple schedules can be created for different times

### 10.3 Override Specific Dates (Optional)
```sql
-- Override availability for specific dates
INSERT INTO service_availability (
  id, serviceId, date, timeSlot, availableCapacity, totalCapacity, 
  priceModifier, isActive, createdAt, updatedAt
)
VALUES (
  'sa_001',
  'ps_snorkeling_001',
  '2024-12-25', -- Christmas Day
  '09:00',
  0, -- Fully booked
  12,
  1.0,
  true,
  NOW(),
  NOW()
);
```

---

## 11. Testing & Validation

### 11.1 Database Integrity Checks
```sql
-- Verify all relationships are properly set up
SELECT 
  ps.name as service_name,
  p.displayName as provider_name,
  st.name as service_type,
  b.name as boat_name,
  COUNT(sr.id) as route_count,
  COUNT(sp.id) as package_count,
  COUNT(ss.id) as schedule_count
FROM provider_services ps
JOIN providers p ON ps.providerId = p.id
JOIN service_types st ON ps.serviceTypeId = st.id
LEFT JOIN service_assignments sa ON ps.id = sa.serviceId
LEFT JOIN boats b ON sa.boatId = b.id
LEFT JOIN service_routes sr ON ps.id = sr.serviceId
LEFT JOIN service_packages sp ON ps.id = sp.serviceId
LEFT JOIN service_schedules ss ON ps.id = ss.serviceId
WHERE ps.id = 'ps_snorkeling_001'
GROUP BY ps.id, ps.name, p.displayName, st.name, b.name;
```

### 11.2 API Testing
```bash
# Test service retrieval (includes age pricing in service.agePricing)
curl -X GET "http://localhost:5001/api/services/ps_snorkeling_001"

# Test age ranges (shows only configured age categories for the service)
curl -X GET "http://localhost:5001/api/services/ps_snorkeling_001/age-ranges"

# Test timeslots
curl -X GET "http://localhost:5001/api/services/ps_snorkeling_001/timeslots?date=2024-08-20"
```

### 11.3 API Endpoint Differences

**Service Detail API** (`/api/services/:id`):
- Returns complete service information including packages and age pricing
- `service.agePricing` array shows pricing for configured age categories
- Used by service detail pages to display pricing information

**Age Ranges API** (`/api/services/:id/age-ranges`):
- Returns age categories with min/max age restrictions for the service
- Shows only age categories configured in `service_age_ranges` table
- If no age ranges configured, returns all 5 default age categories
- Used by booking pages to show age category selection with proper age ranges
- `hasOverride: true` indicates service-specific age restrictions

**Key Difference**:
- Service detail shows **pricing** for age categories
- Age ranges shows **age restrictions** and **which categories are available**

### 11.3 Frontend Testing
1. Navigate to service detail page
2. Test booking flow with different passenger combinations
3. Verify pricing calculations
4. Test date and time selection
5. Complete a test booking

### 11.4 Validation Checklist
- ✅ Service appears in search results
- ✅ Service detail page loads correctly
- ✅ Pricing displays properly for all scenarios
- ✅ Age ranges are configurable
- ✅ Packages are selectable (if configured)
- ✅ Date picker shows available dates
- ✅ Time slots are populated
- ✅ Booking flow completes successfully
- ✅ Confirmation email is sent
- ✅ Booking appears in dashboard

---

## Database Entity Relationships

```mermaid
erDiagram
    User ||--o| Provider : owns
    User ||--o{ Boat : owns
    Provider ||--o{ ProviderService : offers
    Provider ||--o{ Boat : manages
    ProviderService ||--o{ ServicePackage : has
    ProviderService ||--o{ ServiceAgePricing : has
    ProviderService ||--o{ ServiceSchedule : has
    ProviderService ||--o{ ServiceAssignment : assigned_to
    ProviderService ||--o{ ServiceRoute : serves
    Boat ||--o{ ServiceAssignment : assigned_to
    ServiceType ||--o{ ProviderService : categorizes
    Route ||--o{ ServiceRoute : used_by
    PackageType ||--o{ ServicePackage : defines
    AgeCategory ||--o{ ServiceAgePricing : applies_to
```

---

## Common Issues & Troubleshooting

### Issue 1: Service Not Appearing in Search
**Cause**: Service or related entities are inactive
**Solution**: Check `isActive` flags on service, provider, boat, and service type

### Issue 2: No Available Time Slots
**Cause**: Missing service schedules or all slots are booked
**Solution**: Verify service_schedules table has entries for the service

### Issue 3: Pricing Not Calculating Correctly
**Cause**: Missing age pricing or package configuration
**Solution**: Ensure age ranges and pricing are properly configured

### Issue 4: Booking Fails
**Cause**: Capacity exceeded or validation errors
**Solution**: Check boat capacity vs. service maxCapacity and passenger counts

### Issue 5: Age Categories Inconsistent Between Pages
**Cause**: Service detail page shows different age categories than booking page
**Solution**:
- Check if service has specific age ranges configured in `service_age_ranges` table
- Service detail API (`/api/services/:id`) shows age pricing from `service_age_pricing`
- Age ranges API (`/api/services/:id/age-ranges`) shows configured age restrictions
- If no age ranges configured, booking page shows all 5 default categories

**Verification Commands:**
```sql
-- Check configured age ranges for a service
SELECT sar.*, ac.name, ac.code
FROM service_age_ranges sar
JOIN age_categories ac ON sar.ageCategoryId = ac.id
WHERE sar.serviceId = 'your_service_id';

-- Check age pricing for a service
SELECT sap.*, ac.name, ac.code
FROM service_age_pricing sap
JOIN age_categories ac ON sap.ageCategoryId = ac.id
WHERE sap.serviceId = 'your_service_id';
```

### Issue 6: Wrong Age Ranges Displayed
**Cause**: Incorrect min/max age configuration for service type
**Solution**: Update age ranges to match service requirements
```sql
-- Example: Fix diving service to only allow 12+ years
UPDATE service_age_ranges
SET minAge = 12, maxAge = 12
WHERE serviceId = 'diving_service_id' AND ageCategoryId = 'ac_children';

UPDATE service_age_ranges
SET minAge = 13, maxAge = NULL
WHERE serviceId = 'diving_service_id' AND ageCategoryId = 'ac_adults';
```

---

## Conclusion

Following this guide ensures a complete, bookable service is properly configured in the GoSea system. All database relationships are established, pricing is configured, and the service is ready for customer bookings.

For additional support or custom configurations, refer to the GoSea API documentation or contact the development team.
