# 💰 GoSea Pricing Data Storage Documentation
**Date**: September 28, 2025  
**Version**: 1.0  
**Author**: System Documentation

## 📋 Overview

This document explains how pricing data is stored in the GoSea database for the four pricing models implemented in the boat owner service creation system. Each pricing model uses different combinations of database tables and fields to store pricing information.

## 🗄️ Database Schema Overview

### Core Pricing Tables

#### **1. ProviderService** (`provider_services`)
The main service table that contains base pricing information and determines which pricing model is used.

```prisma
model ProviderService {
  id                  String                @id @default(cuid())
  providerId          String
  serviceTypeId       String
  name                String
  basePrice           Decimal               // Base price for basic pricing
  agePricing          Json?                 // Legacy field (nullable)
  // ... other fields
  servicePackages     ServicePackage[]      // Related packages
  serviceAgePricing   ServiceAgePricing[]   // Age-based pricing
  serviceAgeRanges    ServiceAgeRange[]     // Age range definitions
}
```

#### **2. ServicePackage** (`service_packages`)
Stores package-based pricing information with optional age-specific pricing within packages.

```prisma
model ServicePackage {
  id            String          @id @default(cuid())
  serviceId     String
  packageTypeId String
  basePrice     Decimal         // Package base price
  agePricing    Json?           // Age-specific pricing within package
  priceModifier Decimal         @default(1.0)
  includedItems String[]
  excludedItems String[]
  // ... other fields
}
```

#### **3. ServiceAgePricing** (`service_age_pricing`)
Stores age-based pricing for services that use age-differentiated pricing.

```prisma
model ServiceAgePricing {
  id               String          @id @default(cuid())
  serviceId        String
  ageCategoryId    String
  price            Decimal         // Price for this age category
  priceType        String          @default("FIXED")
  percentageOfBase Decimal?
  // ... other fields
}
```

#### **4. ServiceAgeRange** (`service_age_ranges`)
Defines age ranges for services that use age-based pricing.

```prisma
model ServiceAgeRange {
  id            String          @id @default(cuid())
  serviceId     String
  ageCategoryId String
  minAge        Int             // Minimum age for this category
  maxAge        Int?            // Maximum age (nullable for open-ended)
  // ... other fields
}
```

## 🎯 Pricing Models Implementation

### **1. Basic Pricing Model**
**Description**: Fixed price per person, regardless of age or package selection.

#### **Tables Used:**
- ✅ `ProviderService` (primary)
- ❌ `ServicePackage` (not used)
- ❌ `ServiceAgePricing` (not used)
- ❌ `ServiceAgeRange` (not used)

#### **Data Storage:**
```sql
-- ProviderService table
INSERT INTO provider_services (
  id, name, basePrice, ...
) VALUES (
  'cmg3p9p05001bet9z6r6shb3g',
  'Premium Diving Expedition',
  200.00,
  ...
);
```

#### **Example Data Structure:**
```json
{
  "id": "cmg3p9p05001bet9z6r6shb3g",
  "name": "Premium Diving Expedition",
  "basePrice": "200",
  "packages": [],
  "agePricing": [],
  "ageRanges": []
}
```

#### **Price Calculation:**
- **All customers**: RM 200.00 per person

---

### **2. Age-Based Pricing Model**
**Description**: Different prices based on age categories (Adults, Children, Seniors).

#### **Tables Used:**
- ✅ `ProviderService` (base service info)
- ❌ `ServicePackage` (not used)
- ✅ `ServiceAgePricing` (age-specific prices)
- ✅ `ServiceAgeRange` (age range definitions)

#### **Data Storage:**
```sql
-- ProviderService table
INSERT INTO provider_services (
  id, name, basePrice, ...
) VALUES (
  'cmg3p84ox000jet9z0a2nq1k9',
  'Romantic Sunset Cruise',
  90.00,  -- Base price (typically matches adult price)
  ...
);

-- ServiceAgePricing table
INSERT INTO service_age_pricing (
  serviceId, ageCategoryId, price, priceType
) VALUES 
  ('cmg3p84ox000jet9z0a2nq1k9', 'ac_adults', 90.00, 'FIXED'),
  ('cmg3p84ox000jet9z0a2nq1k9', 'ac_children', 60.00, 'FIXED'),
  ('cmg3p84ox000jet9z0a2nq1k9', 'ac_seniors', 80.00, 'FIXED');

-- ServiceAgeRange table
INSERT INTO service_age_ranges (
  serviceId, ageCategoryId, minAge, maxAge
) VALUES 
  ('cmg3p84ox000jet9z0a2nq1k9', 'ac_adults', 13, 59),
  ('cmg3p84ox000jet9z0a2nq1k9', 'ac_children', 3, 12),
  ('cmg3p84ox000jet9z0a2nq1k9', 'ac_seniors', 60, 99);
```

#### **Example Data Structure:**
```json
{
  "id": "cmg3p84ox000jet9z0a2nq1k9",
  "name": "Romantic Sunset Cruise",
  "basePrice": "90",
  "packages": [],
  "agePricing": [
    {"ageCategory": "Adults", "price": "90"},
    {"ageCategory": "Children", "price": "60"},
    {"ageCategory": "Seniors", "price": "80"}
  ]
}
```

#### **Price Calculation:**
- **Adults (13-59)**: RM 90.00 per person
- **Children (3-12)**: RM 60.00 per person  
- **Seniors (60-99)**: RM 80.00 per person

---

### **3. Package-Based Pricing Model**
**Description**: Multiple package tiers with different prices, same price for all ages within each package.

#### **Tables Used:**
- ✅ `ProviderService` (base service info)
- ✅ `ServicePackage` (package prices)
- ❌ `ServiceAgePricing` (not used)
- ❌ `ServiceAgeRange` (not used)

#### **Data Storage:**
```sql
-- ProviderService table
INSERT INTO provider_services (
  id, name, basePrice, ...
) VALUES (
  'cmg3p7ozj000aet9z4msgjrko',
  'Island Hopping Adventure',
  0.00,  -- Base price set to 0 when packages are used
  ...
);

-- ServicePackage table
INSERT INTO service_packages (
  serviceId, packageTypeId, basePrice, agePricing, ...
) VALUES 
  ('cmg3p7ozj000aet9z4msgjrko', 'pt_premium', 150.00, NULL, ...),
  ('cmg3p7ozj000aet9z4msgjrko', 'pt_deluxe', 220.00, NULL, ...);
```

#### **Example Data Structure:**
```json
{
  "id": "cmg3p7ozj000aet9z4msgjrko",
  "name": "Island Hopping Adventure",
  "basePrice": "0",
  "packages": [
    {"packageType": "Premium", "basePrice": "150", "agePricing": null},
    {"packageType": "Deluxe", "basePrice": "220", "agePricing": null}
  ],
  "agePricing": []
}
```

#### **Price Calculation:**
- **Premium Package**: RM 150.00 per person (all ages)
- **Deluxe Package**: RM 220.00 per person (all ages)

---

### **4. Full Variation Pricing Model** (Package + Age-Based)
**Description**: Multiple packages with age-specific pricing within each package.

#### **Tables Used:**
- ✅ `ProviderService` (base service info)
- ✅ `ServicePackage` (package info with age pricing JSON)
- ✅ `ServiceAgePricing` (fallback age pricing)
- ✅ `ServiceAgeRange` (age range definitions)

#### **Data Storage:**
```sql
-- ProviderService table
INSERT INTO provider_services (
  id, name, basePrice, ...
) VALUES (
  'cmg3qu4eg000empbpc5lrk9wu',
  'FINAL COMPLEX SERVICE TEST',
  100.00,  -- Base price (typically matches adult standard price)
  ...
);

-- ServicePackage table with JSON agePricing
INSERT INTO service_packages (
  serviceId, packageTypeId, basePrice, agePricing, ...
) VALUES 
  ('cmg3qu4eg000empbpc5lrk9wu', 'pt_standard', 100.00, 
   '{"adult": 100, "child": 70, "senior": 90}', ...),
  ('cmg3qu4eg000empbpc5lrk9wu', 'pt_deluxe', 150.00, 
   '{"adult": 150, "child": 105, "senior": 135}', ...);

-- ServiceAgePricing table (fallback pricing)
INSERT INTO service_age_pricing (
  serviceId, ageCategoryId, price, priceType
) VALUES 
  ('cmg3qu4eg000empbpc5lrk9wu', 'ac_adults', 100.00, 'FIXED'),
  ('cmg3qu4eg000empbpc5lrk9wu', 'ac_children', 70.00, 'FIXED'),
  ('cmg3qu4eg000empbpc5lrk9wu', 'ac_seniors', 90.00, 'FIXED');

-- ServiceAgeRange table
INSERT INTO service_age_ranges (
  serviceId, ageCategoryId, minAge, maxAge
) VALUES 
  ('cmg3qu4eg000empbpc5lrk9wu', 'ac_adults', 13, 59),
  ('cmg3qu4eg000empbpc5lrk9wu', 'ac_children', 3, 12),
  ('cmg3qu4eg000empbpc5lrk9wu', 'ac_seniors', 60, 99);
```

#### **Example Data Structure:**
```json
{
  "id": "cmg3qu4eg000empbpc5lrk9wu",
  "name": "FINAL COMPLEX SERVICE TEST",
  "basePrice": "100",
  "packages": [
    {
      "packageType": "Standard",
      "basePrice": "100",
      "agePricing": {"adult": 100, "child": 70, "senior": 90}
    },
    {
      "packageType": "Deluxe", 
      "basePrice": "150",
      "agePricing": {"adult": 150, "child": 105, "senior": 135}
    }
  ],
  "agePricing": [
    {"ageCategory": "Adults", "price": "100"},
    {"ageCategory": "Children", "price": "70"},
    {"ageCategory": "Seniors", "price": "90"}
  ]
}
```

#### **Price Calculation:**
**Standard Package:**
- Adults (13-59): RM 100.00 per person
- Children (3-12): RM 70.00 per person
- Seniors (60-99): RM 90.00 per person

**Deluxe Package:**
- Adults (13-59): RM 150.00 per person
- Children (3-12): RM 105.00 per person
- Seniors (60-99): RM 135.00 per person

## 🔗 Relationships and Foreign Keys

### **Primary Relationships:**
```
ProviderService (1) ←→ (N) ServicePackage
ProviderService (1) ←→ (N) ServiceAgePricing  
ProviderService (1) ←→ (N) ServiceAgeRange
ServicePackage (N) ←→ (1) PackageType
ServiceAgePricing (N) ←→ (1) AgeCategory
ServiceAgeRange (N) ←→ (1) AgeCategory
```

### **Foreign Key Constraints:**
- `ServicePackage.serviceId` → `ProviderService.id`
- `ServicePackage.packageTypeId` → `PackageType.id`
- `ServiceAgePricing.serviceId` → `ProviderService.id`
- `ServiceAgePricing.ageCategoryId` → `AgeCategory.id`
- `ServiceAgeRange.serviceId` → `ProviderService.id`
- `ServiceAgeRange.ageCategoryId` → `AgeCategory.id`

## 🛠️ Service Creation Logic

### **Pricing Model Determination:**
The `pricingModel` field in the service creation request determines which tables are populated:

```javascript
// Service creation wizard logic
switch (pricingModel) {
  case 'basic':
    // Only populate ProviderService.basePrice
    break;
    
  case 'ageBased':
    // Populate ServiceAgePricing + ServiceAgeRange
    break;
    
  case 'packageOnly':
    // Populate ServicePackage (agePricing = null)
    break;
    
  case 'packageAndAge':
  case 'fullVariation':
    // Populate ServicePackage (with agePricing JSON) + 
    // ServiceAgePricing + ServiceAgeRange
    break;
}
```

### **JSON Field Usage:**
The `ServicePackage.agePricing` JSON field stores age-specific pricing within packages:

```json
{
  "adult": 150,
  "child": 105, 
  "senior": 135
}
```

This allows complex pricing matrices where each package can have different prices for different age groups.

## 📊 Query Examples

### **Retrieve Basic Pricing:**
```sql
SELECT basePrice FROM provider_services WHERE id = 'service_id';
```

### **Retrieve Age-Based Pricing:**
```sql
SELECT sap.price, ac.name as age_category
FROM service_age_pricing sap
JOIN age_categories ac ON sap.ageCategoryId = ac.id
WHERE sap.serviceId = 'service_id';
```

### **Retrieve Package Pricing:**
```sql
SELECT sp.basePrice, sp.agePricing, pt.name as package_name
FROM service_packages sp
JOIN package_types pt ON sp.packageTypeId = pt.id  
WHERE sp.serviceId = 'service_id';
```

### **Retrieve Full Variation Pricing:**
```sql
-- Get packages with age pricing
SELECT sp.basePrice, sp.agePricing, pt.name as package_name
FROM service_packages sp
JOIN package_types pt ON sp.packageTypeId = pt.id
WHERE sp.serviceId = 'service_id';

-- Get fallback age pricing
SELECT sap.price, ac.name as age_category
FROM service_age_pricing sap
JOIN age_categories ac ON sap.ageCategoryId = ac.id
WHERE sap.serviceId = 'service_id';
```

## 🎯 Summary

The GoSea pricing system uses a flexible, relational approach that supports four distinct pricing models through strategic use of multiple tables and JSON fields. The system ensures data integrity through foreign key constraints while providing the flexibility needed for complex pricing scenarios.

**Key Design Principles:**
1. **Separation of Concerns**: Each pricing aspect (packages, age ranges, pricing) has its own table
2. **Flexibility**: JSON fields allow complex pricing within packages
3. **Consistency**: All models use the same base service structure
4. **Scalability**: New pricing models can be added without schema changes
5. **Data Integrity**: Foreign key constraints ensure referential integrity

---
**Document Version**: 1.0  
**Last Updated**: September 28, 2025  
**Status**: Production Ready ✅
