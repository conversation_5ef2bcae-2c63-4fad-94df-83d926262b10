# GoSea Database Schema Optimization Proposal

## Executive Summary

This document proposes an optimized database schema for GoSea's user management system that eliminates redundancy, improves data organization, and enhances performance while maintaining backward compatibility and data integrity.

## Proposed Schema Structure

### 1. Optimized User Table (`users`)
**Purpose**: Core authentication and authorization data only
**Changes**: Minimal changes, focus on core functionality

```prisma
model User {
  id                      String                   @id @default(cuid())
  email                   String                   @unique
  role                    UserRole
  isActive                Boolean                  @default(true)
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  
  // Authentication fields
  password                String?
  googleId                String?                  @unique
  emailVerified           Boolean                  @default(false)
  emailVerifiedAt         DateTime?
  lastLoginAt             DateTime?
  loginAttempts           Int                      @default(0)
  lockedUntil             DateTime?
  
  // Approval workflow (role-agnostic)
  isApproved              Boolean                  @default(false)
  approvedAt              DateTime?
  approvedBy              String?
  rejectionReason         String?
  
  // Relationships
  profile                 Profile?
  provider                Provider?                @relation("ProviderOwner")
  // ... other existing relationships
  
  @@map("users")
}
```

### 2. Cleaned Profile Table (`profiles`)
**Purpose**: Personal information only - NO business data
**Major Changes**: Remove all business-related fields

```prisma
model Profile {
  id                 String    @id @default(cuid())
  userId             String    @unique
  
  // Personal Information Only
  firstName          String
  lastName           String
  phone              String?                      // Encrypted
  profilePicture     String?
  dateOfBirth        DateTime?
  emergencyContact   String?                      // Encrypted
  
  // Personal Address (not business address)
  personalAddress1   String?                      // Encrypted
  personalAddress2   String?                      // Encrypted  
  personalCity       String?                      // Encrypted
  personalPostcode   String?                      // Encrypted
  personalState      String?                      // Encrypted
  
  // User Preferences
  language           String    @default("en")
  timezone           String?   @default("Asia/Kuala_Lumpur")
  
  // Metadata
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt
  profileCompletedAt DateTime?
  
  user               User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("profiles")
}
```

### 3. Enhanced Provider Table (`providers`)
**Purpose**: Complete business information consolidation
**Major Changes**: Consolidate ALL business data here

```prisma
model Provider {
  id               String                  @id @default(cuid())
  userId           String                  @unique
  
  // Business Identity (consolidated from Profile)
  companyName      String                  // Moved from Profile
  displayName      String
  brn              String?                 // Moved from Profile
  agencyName       String?                 // Moved from Profile
  description      String?
  
  // Business Licenses & Certifications
  operatingLicense String?
  licenseExpiryDate DateTime?              // NEW: Track license validity
  certifications   Json?                   // NEW: Store multiple certifications
  
  // Business Contact (standardized)
  businessPhone    String                  // Renamed from contactPhone, encrypted
  businessEmail    String?                 // Optional, different from user email
  websiteUrl       String?                 // NEW: Business website
  
  // Business Address (separate from personal)
  businessAddress1 String?                 // Encrypted
  businessAddress2 String?                 // Encrypted
  businessCity     String?                 // Encrypted
  businessPostcode String?                 // Encrypted
  businessState    String?                 // Encrypted
  
  // Business Branding
  logoUrl          String?
  coverImageUrl    String?
  brandColors      Json?                   // NEW: Brand color scheme
  
  // Business Metrics
  rating           Decimal                 @default(0)
  reviewCount      Int                     @default(0)
  totalBookings    Int                     @default(0)  // NEW: Performance metric
  
  // Business Status
  isVerified       Boolean                 @default(false)
  isActive         Boolean                 @default(true)
  verifiedAt       DateTime?               // NEW: Verification timestamp
  verifiedBy       String?                 // NEW: Who verified
  
  // System Fields
  isAutoGenerated  Boolean                 @default(false)
  createdAt        DateTime                @default(now())
  updatedAt        DateTime                @updatedAt
  
  // Relationships
  user             User                    @relation("ProviderOwner", fields: [userId], references: [id], onDelete: Cascade)
  boats            Boat[]                  @relation("ProviderBoats")
  bookings         Booking[]
  operatingAreas   ProviderOperatingArea[]
  services         ProviderService[]
  
  // Indexes for performance
  @@index([companyName])
  @@index([brn])
  @@index([isVerified, isActive])
  @@map("providers")
}
```

## Key Improvements

### 1. Eliminated Redundancy
**Before**: 
- companyName in both Profile and Provider
- brn in both Profile and Provider
- Contact info scattered across tables

**After**:
- Single source of truth for all business data in Provider
- Personal data clearly separated in Profile
- Consistent encryption handling

### 2. Enhanced Data Organization

#### Clear Separation of Concerns:
- **User**: Authentication & authorization only
- **Profile**: Personal information only
- **Provider**: Business information only

#### Role-Based Data Access:
- **Customers**: User + Profile (2 tables)
- **Boat Owners**: User + Profile + Provider (3 tables, but optimized)
- **Admins**: User + Profile (minimal data for management)

### 3. Improved Data Integrity

#### New Constraints:
```sql
-- Ensure business phone is encrypted consistently
ALTER TABLE providers ADD CONSTRAINT check_business_phone_encrypted 
CHECK (business_phone IS NULL OR length(business_phone) > 20);

-- Ensure BRN format for Malaysian businesses
ALTER TABLE providers ADD CONSTRAINT check_brn_format 
CHECK (brn IS NULL OR brn ~ '^[0-9]{10}$');

-- Ensure only BOAT_OWNER role can have providers
-- (Implemented at application level with database trigger)
```

#### Encryption Standardization:
- All sensitive data (phone, address) consistently encrypted
- Clear distinction between encrypted and plain text fields
- Unified encryption key management

### 4. Performance Optimizations

#### Optimized Indexes:
```sql
-- Provider business search
CREATE INDEX idx_providers_business_search ON providers(company_name, is_verified, is_active);

-- User role-based queries  
CREATE INDEX idx_users_role_active ON users(role, is_active, is_approved);

-- Profile completion tracking
CREATE INDEX idx_profiles_completion ON profiles(profile_completed_at) WHERE profile_completed_at IS NOT NULL;
```

#### Query Optimization Examples:

**Customer Profile Query** (Improved):
```sql
SELECT u.id, u.email, u.role, p.first_name, p.last_name, p.phone
FROM users u 
JOIN profiles p ON u.id = p.user_id 
WHERE u.email = ? AND u.is_active = true;
```

**Boat Owner Dashboard Query** (Optimized):
```sql
SELECT 
  u.id, u.email, u.role, u.is_approved,
  p.first_name, p.last_name, p.phone,
  pr.company_name, pr.display_name, pr.rating, pr.is_verified
FROM users u 
JOIN profiles p ON u.id = p.user_id 
JOIN providers pr ON u.id = pr.user_id 
WHERE u.email = ? AND u.role = 'BOAT_OWNER';
```

## Migration Strategy

### Phase 1: Schema Preparation (Zero Downtime)
1. **Add new columns** to Provider table
2. **Create migration scripts** for data transfer
3. **Set up data validation** procedures
4. **Prepare rollback scripts**

### Phase 2: Data Migration (Minimal Downtime)
1. **Copy business data** from Profile to Provider
2. **Validate data consistency** 
3. **Update application code** to use new schema
4. **Test all user flows**

### Phase 3: Cleanup (Zero Downtime)
1. **Remove business columns** from Profile table
2. **Update indexes** and constraints
3. **Clean up unused data**
4. **Performance testing**

### Migration Script Example:
```sql
-- Step 1: Add new columns to Provider
ALTER TABLE providers 
ADD COLUMN business_phone VARCHAR(255),
ADD COLUMN business_address1 VARCHAR(255),
ADD COLUMN business_address2 VARCHAR(255),
ADD COLUMN business_city VARCHAR(255),
ADD COLUMN business_postcode VARCHAR(50),
ADD COLUMN business_state VARCHAR(100);

-- Step 2: Migrate data from Profile to Provider
UPDATE providers pr
SET 
  business_phone = p.phone,
  business_address1 = p.address1,
  business_address2 = p.address2,
  business_city = p.city,
  business_postcode = p.postcode,
  business_state = p.state
FROM profiles p
WHERE pr.user_id = p.user_id;

-- Step 3: Validate migration
SELECT COUNT(*) FROM providers pr
JOIN profiles p ON pr.user_id = p.user_id
WHERE pr.company_name != p.company_name OR pr.brn != p.brn;
-- Should return 0

-- Step 4: Remove redundant columns from Profile (after app update)
ALTER TABLE profiles 
DROP COLUMN company_name,
DROP COLUMN brn,
DROP COLUMN agency_name;
```

## Benefits Analysis

### 1. Storage Optimization
- **Reduced Redundancy**: ~30% reduction in duplicate data storage
- **Improved Indexing**: More efficient indexes on consolidated data
- **Better Compression**: Related data stored together compresses better

### 2. Performance Improvements
- **Faster Queries**: Reduced JOIN complexity for business data
- **Better Caching**: Single source of truth improves cache efficiency
- **Optimized Indexes**: Role-specific query optimization

### 3. Maintenance Benefits
- **Single Source of Truth**: Business data updates in one location
- **Reduced Complexity**: Clear data ownership and responsibility
- **Better Testing**: Easier to validate data consistency

### 4. Security Enhancements
- **Consistent Encryption**: All sensitive data handled uniformly
- **Clear Data Classification**: Personal vs business data separation
- **Audit Trail**: Better tracking of business data changes

## Risk Mitigation

### 1. Migration Risks
- **Data Loss Prevention**: Multiple validation checkpoints
- **Rollback Plan**: Complete rollback procedures documented
- **Testing Strategy**: Comprehensive testing in staging environment

### 2. Application Compatibility
- **Gradual Migration**: Phased approach minimizes disruption
- **Backward Compatibility**: Temporary support for old schema
- **API Versioning**: Maintain API compatibility during transition

### 3. Performance Risks
- **Load Testing**: Validate performance improvements
- **Monitoring**: Real-time performance monitoring during migration
- **Optimization**: Query optimization and index tuning

## Implementation Timeline

### Week 1-2: Preparation
- Schema design finalization
- Migration script development
- Testing environment setup

### Week 3-4: Development
- Application code updates
- API endpoint modifications
- Unit test updates

### Week 5-6: Testing
- Integration testing
- Performance testing
- User acceptance testing

### Week 7: Migration
- Production migration execution
- Monitoring and validation
- Performance optimization

## Success Metrics

### Performance Metrics:
- **Query Response Time**: Target 20% improvement
- **Database Size**: Target 15% reduction
- **Index Efficiency**: Target 25% improvement

### Data Quality Metrics:
- **Data Consistency**: 100% consistency validation
- **Migration Accuracy**: Zero data loss tolerance
- **System Uptime**: 99.9% availability during migration

## Edge Cases and Special Considerations

### 1. User Type Transitions
**Scenario**: Customer becomes Boat Owner
**Solution**:
- Create Provider record when role changes to BOAT_OWNER
- Migrate relevant business data from Profile to Provider
- Maintain audit trail of role changes

### 2. Data Privacy Compliance
**GDPR/PDPA Considerations**:
- Clear data classification (personal vs business)
- Separate retention policies for different data types
- Enhanced data export/deletion capabilities

### 3. Multi-Business Owners
**Future Consideration**:
- Current schema supports one business per user
- Future enhancement: One-to-many User-Provider relationship
- Requires additional business selection logic

### 4. Backup and Recovery
**Enhanced Strategy**:
- Separate backup schedules for different data types
- Encrypted backup validation procedures
- Point-in-time recovery for business-critical data

## Application Code Impact

### 1. API Endpoint Changes
**Before**:
```javascript
// Get boat owner profile
const profile = await prisma.user.findUnique({
  where: { email },
  include: { profile: true, provider: true }
});
const companyName = profile.profile.companyName; // Redundant data
```

**After**:
```javascript
// Get boat owner profile (optimized)
const profile = await prisma.user.findUnique({
  where: { email },
  include: { profile: true, provider: true }
});
const companyName = profile.provider.companyName; // Single source of truth
```

### 2. Form Handling Updates
**Registration Flow Changes**:
- Personal info → Profile table only
- Business info → Provider table only
- Clear separation in form validation logic

### 3. Search and Filtering
**Business Search Optimization**:
```sql
-- Old: Search across multiple tables
SELECT * FROM users u
JOIN profiles p ON u.id = p.user_id
JOIN providers pr ON u.id = pr.user_id
WHERE p.company_name ILIKE '%search%' OR pr.company_name ILIKE '%search%';

-- New: Single table search
SELECT * FROM providers pr
JOIN users u ON pr.user_id = u.id
WHERE pr.company_name ILIKE '%search%' AND pr.is_active = true;
```

## Monitoring and Alerting

### 1. Data Consistency Monitoring
```sql
-- Daily consistency check
SELECT 'Profile-Provider Mismatch' as issue_type, COUNT(*) as count
FROM profiles p
JOIN providers pr ON p.user_id = pr.user_id
WHERE p.company_name IS NOT NULL; -- Should be 0 after migration
```

### 2. Performance Monitoring
- Query execution time tracking
- Index usage statistics
- Cache hit rate monitoring
- Database size growth tracking

### 3. Migration Health Checks
- Data integrity validation
- Encryption key validation
- Foreign key constraint validation
- Application error rate monitoring

## Future Enhancements

### 1. Advanced Business Features
- **Multi-location Support**: Business can operate from multiple locations
- **Franchise Management**: Support for franchise business models
- **Business Partnerships**: Joint business operations support

### 2. Enhanced Analytics
- **Business Performance Metrics**: Revenue, booking trends, customer satisfaction
- **Operational Analytics**: Capacity utilization, seasonal patterns
- **Competitive Analysis**: Market positioning insights

### 3. Integration Capabilities
- **Third-party Business Systems**: Accounting, CRM integration
- **Government APIs**: License verification, business registration validation
- **Payment Processors**: Enhanced financial data integration

---

*This optimized schema provides a solid foundation for GoSea's continued growth while addressing current inefficiencies and preparing for future scalability needs.*
