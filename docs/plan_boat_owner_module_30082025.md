# Comprehensive Boat Owner Management Module - Development Plan

**Project:** GoSea Platform Boat Owner Dashboard  
**Date:** August 30, 2025  
**Version:** 1.0  

## Executive Summary

This document outlines the development plan for a comprehensive boat owner management module that will provide boat owners with complete control over their business operations on the GoSea platform. The module builds upon the existing boat owner registration and approval system to deliver a full-featured dashboard for managing profiles, boats, services, and bookings.

## Current System Analysis

### Existing Infrastructure
- ✅ **Authentication System:** Boat owner registration with admin approval workflow
- ✅ **Database Schema:** User, Profile, Provider, Boat, and Service models established
- ✅ **Admin Dashboard:** Approval workflow for boat owner registrations
- ✅ **Email System:** Verification and notification workflows
- ✅ **Role-Based Access:** BOAT_OWNER role with proper permissions

### Reference Implementation
**AG Holiday Provider** serves as the reference implementation:
- **User ID:** `user_ag_holiday`
- **Provider ID:** `provider_ag_holiday`
- **Company:** AG Holiday (Family-friendly holiday tours)
- **Services:** Day Trip Snorkeling with packages and age-based pricing
- **Boats:** 2 boats with comprehensive specifications
- **Operating Areas:** Setiu jetty operations

## Module Architecture

### 1. Core Module Structure
```
/frontend/src/pages/boat-owner/
├── dashboard/
│   ├── index.js                 # Main dashboard overview
│   ├── profile.js               # Profile management
│   ├── boats/
│   │   ├── index.js             # Boat fleet overview
│   │   ├── create.js            # Add new boat
│   │   ├── [id]/
│   │   │   ├── edit.js          # Edit boat details
│   │   │   └── schedule.js      # Boat availability
│   └── services/
│       ├── index.js             # Services overview
│       ├── create.js            # Create new service
│       ├── [id]/
│       │   ├── edit.js          # Edit service
│       │   ├── pricing.js       # Pricing management
│       │   ├── routes.js        # Route configuration
│       │   └── schedule.js      # Service scheduling
├── bookings/
│   ├── index.js                 # Bookings overview
│   ├── [id].js                  # Booking details
│   └── calendar.js              # Calendar view
└── analytics/
    └── index.js                 # Performance analytics
```

### 2. Component Library
```
/frontend/src/components/boat-owner/
├── layout/
│   ├── BoatOwnerLayout.js       # Main layout wrapper
│   ├── Sidebar.js               # Navigation sidebar
│   └── Header.js                # Dashboard header
├── profile/
│   ├── ProfileForm.js           # Profile editing form
│   ├── BusinessInfoCard.js      # Business information display
│   └── DocumentUpload.js        # Document management
├── boats/
│   ├── BoatCard.js              # Boat display card
│   ├── BoatForm.js              # Boat creation/editing form
│   ├── BoatGallery.js           # Image gallery management
│   ├── BoatSpecifications.js    # Technical specifications
│   └── AvailabilityCalendar.js  # Availability management
├── services/
│   ├── ServiceCard.js           # Service display card
│   ├── ServiceForm.js           # Service creation/editing
│   ├── PricingMatrix.js         # Age-based pricing
│   ├── PackageManager.js        # Service packages
│   ├── RouteSelector.js         # Route configuration
│   └── ScheduleManager.js       # Service scheduling
├── bookings/
│   ├── BookingCard.js           # Booking display
│   ├── BookingDetails.js        # Detailed booking view
│   ├── BookingCalendar.js       # Calendar interface
│   └── BookingFilters.js        # Filtering options
└── analytics/
    ├── StatsCard.js             # Statistics display
    ├── RevenueChart.js          # Revenue analytics
    └── BookingTrends.js         # Booking trend analysis
```

## Database Schema Requirements

### Current Schema Compatibility
The existing schema supports the boat owner module with these key relationships:
- **User** → **Provider** (1:1) via `userId`
- **Provider** → **Boat** (1:N) via `providerId`
- **Provider** → **ProviderService** (1:N) via `providerId`
- **Boat** → **ServiceAssignment** (1:N) for service-boat relationships

### Required Schema Enhancements
```sql
-- Add boat owner specific fields to Provider model
ALTER TABLE providers ADD COLUMN dashboard_preferences JSON;
ALTER TABLE providers ADD COLUMN notification_settings JSON;
ALTER TABLE providers ADD COLUMN business_hours JSON;

-- Add service management fields
ALTER TABLE provider_services ADD COLUMN auto_approval BOOLEAN DEFAULT false;
ALTER TABLE provider_services ADD COLUMN cancellation_policy TEXT;
ALTER TABLE provider_services ADD COLUMN terms_conditions TEXT;

-- Add boat maintenance tracking
CREATE TABLE boat_maintenance_logs (
  id VARCHAR(191) PRIMARY KEY,
  boat_id VARCHAR(191) NOT NULL,
  maintenance_type VARCHAR(50) NOT NULL,
  description TEXT,
  cost DECIMAL(10,2),
  scheduled_date DATETIME,
  completed_date DATETIME,
  status VARCHAR(20) DEFAULT 'SCHEDULED',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (boat_id) REFERENCES boats(id)
);
```

## API Endpoint Specifications

### 1. Profile Management Endpoints
```javascript
// Get boat owner profile
GET /api/boat-owner/profile
Response: { success: true, data: { user, profile, provider } }

// Update boat owner profile
PUT /api/boat-owner/profile
Body: { firstName, lastName, phone, companyName, description, businessHours }

// Upload business documents
POST /api/boat-owner/documents/upload
Body: FormData with files

// Get business documents
GET /api/boat-owner/documents
Response: { success: true, data: [{ id, type, filename, uploadedAt }] }
```

### 2. Boat Fleet Management Endpoints
```javascript
// Get all boats for boat owner
GET /api/boat-owner/boats
Response: { success: true, data: [boats], pagination }

// Create new boat
POST /api/boat-owner/boats
Body: { name, description, capacity, specifications, images }

// Update boat details
PUT /api/boat-owner/boats/:id
Body: { name, description, capacity, specifications }

// Update boat availability
PUT /api/boat-owner/boats/:id/availability
Body: { availabilityRules, blockedDates }

// Get boat maintenance logs
GET /api/boat-owner/boats/:id/maintenance
Response: { success: true, data: [maintenanceLogs] }

// Add maintenance log
POST /api/boat-owner/boats/:id/maintenance
Body: { type, description, scheduledDate, cost }
```

### 3. Service Management Endpoints
```javascript
// Get all services for boat owner
GET /api/boat-owner/services
Response: { success: true, data: [services], pagination }

// Create new service
POST /api/boat-owner/services
Body: { name, description, category, pricing, packages, routes }

// Update service details
PUT /api/boat-owner/services/:id
Body: { name, description, pricing, packages }

// Update service routes
PUT /api/boat-owner/services/:id/routes
Body: { routes: [{ departureJettyId, destinationId, duration, price }] }

// Update service schedule
PUT /api/boat-owner/services/:id/schedule
Body: { scheduleRules, operatingDays, timeSlots }

// Assign boats to service
POST /api/boat-owner/services/:id/boats
Body: { boatIds: [string] }
```

### 4. Booking Management Endpoints
```javascript
// Get bookings for boat owner
GET /api/boat-owner/bookings
Query: { status, dateFrom, dateTo, serviceId, page, limit }
Response: { success: true, data: [bookings], pagination }

// Get booking details
GET /api/boat-owner/bookings/:id
Response: { success: true, data: booking }

// Update booking status
PUT /api/boat-owner/bookings/:id/status
Body: { status, notes }

// Get booking analytics
GET /api/boat-owner/analytics/bookings
Query: { period, groupBy }
Response: { success: true, data: { stats, trends, revenue } }
```

## UI/UX Design Specifications

### Design System
- **Color Scheme:** Consistent with GoSea platform (Ocean blues, whites)
- **Typography:** Inter font family for readability
- **Components:** Tailwind CSS with DaisyUI components
- **Icons:** Heroicons and Lucide React for consistency
- **Responsive:** Mobile-first approach with breakpoints at 768px, 1024px

### Key Interface Elements

#### 1. Dashboard Overview
- **Stats Cards:** Total boats, active services, monthly bookings, revenue
- **Quick Actions:** Add boat, create service, view recent bookings
- **Recent Activity:** Latest bookings, maintenance reminders, notifications
- **Performance Metrics:** Booking conversion rates, customer ratings

#### 2. Boat Management Interface
- **Fleet Grid View:** Cards showing boat images, names, status, capacity
- **Boat Detail Form:** Comprehensive form with image upload, specifications
- **Availability Calendar:** Interactive calendar for setting availability
- **Maintenance Tracker:** Timeline view of maintenance history and schedules

#### 3. Service Management Interface
- **Service Cards:** Visual cards showing service details, pricing, status
- **Service Builder:** Step-by-step wizard for creating services
- **Pricing Matrix:** Age-based pricing table with package options
- **Route Configuration:** Map-based interface for setting routes

## Implementation Timeline

### Phase 1: Foundation (Weeks 1-2)
- [ ] Set up boat owner dashboard routing structure
- [ ] Create BoatOwnerLayout component with navigation
- [ ] Implement authentication guards for boat owner routes
- [ ] Set up basic API endpoints for profile management
- [ ] Create profile management interface

### Phase 2: Boat Fleet Management (Weeks 3-4)
- [ ] Implement boat CRUD operations (API + Frontend)
- [ ] Create boat creation and editing forms
- [ ] Implement image upload and gallery management
- [ ] Build availability calendar component
- [ ] Add boat specifications management

### Phase 3: Service Management (Weeks 5-6)
- [ ] Implement service CRUD operations
- [ ] Create service creation wizard
- [ ] Build pricing matrix component
- [ ] Implement package management
- [ ] Add route configuration interface

### Phase 4: Booking Management (Weeks 7-8)
- [ ] Implement booking listing and filtering
- [ ] Create booking detail views
- [ ] Add booking status management
- [ ] Build booking calendar interface
- [ ] Implement customer communication features

### Phase 5: Analytics & Polish (Weeks 9-10)
- [ ] Implement analytics dashboard
- [ ] Add performance metrics and charts
- [ ] Create notification system
- [ ] Implement advanced filtering and search
- [ ] Comprehensive testing and bug fixes

## Testing Strategy

### Unit Testing
- **Components:** Jest + React Testing Library for all components
- **API Endpoints:** Supertest for API route testing
- **Business Logic:** Unit tests for service layer functions
- **Database Operations:** Prisma client testing with test database

### Integration Testing
- **Authentication Flow:** End-to-end boat owner registration and login
- **CRUD Operations:** Complete boat and service management workflows
- **Booking Process:** Full booking creation and management flow
- **File Uploads:** Image and document upload functionality

### User Acceptance Testing
- **Boat Owner Workflows:** Complete user journey testing
- **Admin Integration:** Approval workflow testing
- **Customer Impact:** Ensure customer booking experience remains intact
- **Performance Testing:** Load testing for dashboard operations

## Security Considerations

### Access Control
- **Route Protection:** All boat owner routes require authentication and role verification
- **Data Isolation:** Boat owners can only access their own data
- **API Security:** JWT token validation on all endpoints
- **File Upload Security:** Validation and sanitization of uploaded files

### Data Protection
- **Input Validation:** Comprehensive validation on all forms
- **SQL Injection Prevention:** Parameterized queries via Prisma
- **XSS Protection:** Input sanitization and output encoding
- **CSRF Protection:** Token-based CSRF protection

## Integration Points

### Existing Platform Features
- **Customer Booking Flow:** Ensure seamless integration with existing booking system
- **Admin Dashboard:** Extend admin capabilities for boat owner management
- **Search & Discovery:** Integrate boat owner services with search functionality
- **Payment Processing:** Connect with existing payment gateway integration

### Third-Party Services
- **Email Notifications:** Extend existing email service for boat owner communications
- **File Storage:** Integrate with existing image storage solution
- **Analytics:** Connect with platform analytics for comprehensive reporting
- **Maps Integration:** Use existing map services for route visualization

## Success Metrics

### Key Performance Indicators
- **Adoption Rate:** Percentage of approved boat owners using the dashboard
- **Feature Utilization:** Usage statistics for each module component
- **Time to Market:** Average time for boat owners to list their first service
- **User Satisfaction:** Net Promoter Score from boat owner feedback

### Business Metrics
- **Service Listings:** Increase in active services on the platform
- **Booking Volume:** Growth in bookings through boat owner services
- **Revenue Impact:** Platform commission revenue from boat owner bookings
- **Retention Rate:** Boat owner retention and engagement metrics

## Risk Assessment & Mitigation

### Technical Risks
- **Database Performance:** Monitor query performance with increased data volume
- **File Storage Costs:** Implement image optimization and storage limits
- **API Rate Limits:** Implement proper caching and rate limiting
- **Browser Compatibility:** Ensure cross-browser compatibility for dashboard

### Business Risks
- **User Adoption:** Provide comprehensive onboarding and training materials
- **Feature Complexity:** Implement progressive disclosure for advanced features
- **Support Overhead:** Create self-service help documentation
- **Competitive Response:** Monitor market and adjust feature set accordingly

## Detailed Technical Specifications

### Authentication & Authorization
```javascript
// Middleware for boat owner route protection
const requireBoatOwner = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: { provider: true }
    });

    if (!user || user.role !== 'BOAT_OWNER' || !user.isApproved) {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }

    req.user = user;
    req.provider = user.provider;
    next();
  } catch (error) {
    res.status(401).json({ success: false, message: 'Invalid token' });
  }
};
```

### Data Models & Relationships
```javascript
// Extended Provider model for boat owner dashboard
const ProviderWithDashboard = {
  id: String,
  userId: String,
  companyName: String,
  displayName: String,
  description: String,
  dashboardPreferences: {
    theme: 'light' | 'dark',
    defaultView: 'overview' | 'bookings' | 'boats',
    notifications: {
      newBookings: Boolean,
      cancellations: Boolean,
      maintenance: Boolean,
      reviews: Boolean
    }
  },
  businessHours: {
    monday: { open: String, close: String, closed: Boolean },
    tuesday: { open: String, close: String, closed: Boolean },
    // ... other days
  },
  boats: Boat[],
  services: ProviderService[],
  bookings: Booking[],
  operatingAreas: ProviderOperatingArea[]
};
```

### Component Specifications

#### BoatOwnerLayout Component
```jsx
const BoatOwnerLayout = ({ children, activeTab }) => {
  const { user, provider } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard, href: '/boat-owner/dashboard' },
    { id: 'profile', label: 'Profile', icon: User, href: '/boat-owner/dashboard/profile' },
    { id: 'boats', label: 'Fleet', icon: Ship, href: '/boat-owner/dashboard/boats' },
    { id: 'services', label: 'Services', icon: Cog6ToothIcon, href: '/boat-owner/dashboard/services' },
    { id: 'bookings', label: 'Bookings', icon: Receipt, href: '/boat-owner/bookings' },
    { id: 'analytics', label: 'Analytics', icon: ChartBarIcon, href: '/boat-owner/analytics' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar items={navigationItems} activeTab={activeTab} />
      <main className="lg:pl-64">
        <Header provider={provider} />
        <div className="p-6">{children}</div>
      </main>
    </div>
  );
};
```

#### Boat Management Form
```jsx
const BoatForm = ({ boat, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: boat?.name || '',
    description: boat?.description || '',
    capacity: boat?.capacity || 1,
    registrationNumber: boat?.registrationNumber || '',
    yearBuilt: boat?.yearBuilt || new Date().getFullYear(),
    engineType: boat?.engineType || '',
    length: boat?.length || 0,
    safetyRating: boat?.safetyRating || 'A',
    serviceType: boat?.serviceType || '',
    location: boat?.location || '',
    includedItems: boat?.includedItems || [],
    amenities: boat?.amenities || {},
    specifications: boat?.specifications || {}
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    await onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Form fields implementation */}
    </form>
  );
};
```

## Acceptance Criteria

### Profile Management Module
- [ ] **AC-PM-001:** Boat owner can view and edit company information
- [ ] **AC-PM-002:** Business hours can be set for each day of the week
- [ ] **AC-PM-003:** Contact information updates reflect across all services
- [ ] **AC-PM-004:** Business documents can be uploaded and managed
- [ ] **AC-PM-005:** Profile changes require email verification for critical fields

### Boat Fleet Management Module
- [ ] **AC-BF-001:** Boat owner can create new boat entries with all required fields
- [ ] **AC-BF-002:** Boat images can be uploaded with drag-and-drop interface
- [ ] **AC-BF-003:** Boat specifications are validated for completeness
- [ ] **AC-BF-004:** Availability calendar allows setting recurring schedules
- [ ] **AC-BF-005:** Maintenance logs can be added with cost tracking
- [ ] **AC-BF-006:** Boats can be temporarily disabled without deletion
- [ ] **AC-BF-007:** Boat capacity limits are enforced in booking system

### Service Management Module
- [ ] **AC-SM-001:** Services can be created with step-by-step wizard
- [ ] **AC-SM-002:** Age-based pricing matrix supports multiple age ranges
- [ ] **AC-SM-003:** Service packages can include multiple items and pricing
- [ ] **AC-SM-004:** Routes can be configured with departure and destination points
- [ ] **AC-SM-005:** Service schedules support recurring time slots
- [ ] **AC-SM-006:** Boats can be assigned and unassigned from services
- [ ] **AC-SM-007:** Service status can be toggled (active/inactive)
- [ ] **AC-SM-008:** Pricing changes require confirmation dialog

### Booking Management Module
- [ ] **AC-BM-001:** All bookings for boat owner services are displayed
- [ ] **AC-BM-002:** Bookings can be filtered by date, status, and service
- [ ] **AC-BM-003:** Booking details show complete customer and service information
- [ ] **AC-BM-004:** Booking status can be updated with reason notes
- [ ] **AC-BM-005:** Calendar view shows bookings across all services
- [ ] **AC-BM-006:** Customer contact information is accessible for confirmed bookings
- [ ] **AC-BM-007:** Cancellation policies are enforced automatically

### Analytics Module
- [ ] **AC-AN-001:** Dashboard shows key performance metrics
- [ ] **AC-AN-002:** Revenue charts display monthly and yearly trends
- [ ] **AC-AN-003:** Booking conversion rates are calculated and displayed
- [ ] **AC-AN-004:** Service performance comparison is available
- [ ] **AC-AN-005:** Customer ratings and reviews are aggregated
- [ ] **AC-AN-006:** Export functionality for financial reports

### Security & Performance
- [ ] **AC-SP-001:** All boat owner data is isolated by provider ID
- [ ] **AC-SP-002:** File uploads are validated and size-limited
- [ ] **AC-SP-003:** Dashboard loads within 2 seconds on standard connection
- [ ] **AC-SP-004:** All forms include proper validation and error handling
- [ ] **AC-SP-005:** Session timeout redirects to login page
- [ ] **AC-SP-006:** Sensitive operations require password confirmation

### Mobile Responsiveness
- [ ] **AC-MR-001:** Dashboard is fully functional on mobile devices
- [ ] **AC-MR-002:** Forms adapt to mobile screen sizes
- [ ] **AC-MR-003:** Navigation menu collapses appropriately on mobile
- [ ] **AC-MR-004:** Touch interactions work smoothly on all components
- [ ] **AC-MR-005:** Image uploads work on mobile browsers

## Implementation Checklist

### Development Environment Setup
- [ ] Create boat owner dashboard development branch
- [ ] Set up local database with boat owner test data
- [ ] Configure API endpoints in development environment
- [ ] Set up file upload storage for development
- [ ] Install required dependencies and packages

### Backend Implementation
- [ ] Implement boat owner authentication middleware
- [ ] Create all required API endpoints with proper validation
- [ ] Add database migrations for new fields
- [ ] Implement file upload handling for images and documents
- [ ] Add comprehensive error handling and logging
- [ ] Create API documentation with Swagger

### Frontend Implementation
- [ ] Set up routing structure for boat owner dashboard
- [ ] Create reusable layout components
- [ ] Implement all CRUD forms with validation
- [ ] Build interactive calendar components
- [ ] Create responsive design for all screen sizes
- [ ] Add loading states and error handling

### Testing Implementation
- [ ] Write unit tests for all components
- [ ] Create integration tests for API endpoints
- [ ] Implement end-to-end testing scenarios
- [ ] Add performance testing for dashboard operations
- [ ] Create accessibility testing checklist
- [ ] Set up automated testing pipeline

### Deployment Preparation
- [ ] Configure production environment variables
- [ ] Set up production file storage
- [ ] Create database migration scripts
- [ ] Prepare deployment documentation
- [ ] Set up monitoring and logging
- [ ] Create rollback procedures

## Conclusion

This comprehensive boat owner management module will transform the GoSea platform into a complete marketplace solution, empowering boat owners with professional tools to manage their business operations effectively. The phased implementation approach ensures steady progress while maintaining platform stability and user experience quality.

The module leverages existing infrastructure while adding significant value through specialized boat owner functionality, positioning GoSea as the premier platform for marine tourism operations in Malaysia.

**Next Steps:**
1. Review and approve this development plan
2. Set up development environment and team assignments
3. Begin Phase 1 implementation with foundation components
4. Establish regular review cycles and milestone checkpoints
5. Prepare user training materials and documentation
