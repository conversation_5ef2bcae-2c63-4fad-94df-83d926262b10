# GoSea Payment Ecosystem - Complete Implementation Plan

## 🎯 Overview

This document outlines the comprehensive payment ecosystem for GoSea, including terms and conditions, payment gateway selection (Xendit vs Stripe), refund policies, boat owner payouts, and complete financial flow management.

## 📋 Terms and Conditions Strategy

### When Terms Are Required

**Critical Touchpoints:**
1. **Account Registration** - Basic platform usage terms
2. **Pre-Booking** - Service-specific terms and cancellation policies
3. **Payment Confirmation** - Final acceptance of all terms
4. **Special Circumstances** - Weather, safety, force majeure

### Terms and Conditions Implementation

#### 1. Dynamic Terms Display System

```javascript
// components/TermsAcceptance.js
const TermsAcceptance = ({ 
  bookingDetails, 
  service, 
  onAccept, 
  onDecline 
}) => {
  const [acceptedTerms, setAcceptedTerms] = useState({
    platformTerms: false,
    serviceTerms: false,
    cancellationPolicy: false,
    weatherPolicy: false,
    refundPolicy: false
  });

  const allTermsAccepted = Object.values(acceptedTerms).every(Boolean);

  const termsCategories = [
    {
      id: 'platformTerms',
      title: 'GoSea Platform Terms',
      description: 'General terms for using the GoSea platform',
      mandatory: true,
      content: generatePlatformTerms()
    },
    {
      id: 'serviceTerms',
      title: 'Service-Specific Terms',
      description: `Terms specific to ${service.name}`,
      mandatory: true,
      content: generateServiceTerms(service)
    },
    {
      id: 'cancellationPolicy',
      title: 'Cancellation Policy',
      description: 'Cancellation and modification terms',
      mandatory: true,
      content: generateCancellationPolicy(service)
    },
    {
      id: 'weatherPolicy',
      title: 'Weather & Safety Policy',
      description: 'Weather cancellation and safety procedures',
      mandatory: true,
      content: generateWeatherPolicy(service)
    },
    {
      id: 'refundPolicy',
      title: 'Refund Policy',
      description: 'Refund processing and timelines',
      mandatory: true,
      content: generateRefundPolicy()
    }
  ];

  return (
    <div className="bg-white rounded-lg border p-6">
      <h2 className="text-xl font-semibold mb-6">Terms & Conditions</h2>
      
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {termsCategories.map(term => (
          <TermsSection
            key={term.id}
            term={term}
            accepted={acceptedTerms[term.id]}
            onToggle={(accepted) => 
              setAcceptedTerms(prev => ({ ...prev, [term.id]: accepted }))
            }
          />
        ))}
      </div>

      <div className="mt-6 pt-4 border-t">
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="final-acceptance"
            checked={allTermsAccepted}
            readOnly
            className="mt-1"
          />
          <label htmlFor="final-acceptance" className="text-sm text-gray-700">
            I acknowledge that I have read, understood, and agree to all the 
            terms and conditions listed above. I understand that this booking 
            is subject to these terms.
          </label>
        </div>

        <div className="flex space-x-4 mt-6">
          <button
            onClick={onAccept}
            disabled={!allTermsAccepted}
            className="flex-1 py-3 px-6 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Accept & Proceed to Payment
          </button>
          <button
            onClick={onDecline}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            Decline
          </button>
        </div>
      </div>
    </div>
  );
};
```

#### 2. Dynamic Terms Generation

```javascript
// services/termsService.js
class TermsService {
  static generateServiceTerms(service) {
    const baseTerms = {
      duration: `Service duration is approximately ${service.duration || 'as specified'} hours.`,
      capacity: `Maximum capacity is ${service.maxCapacity} passengers.`,
      ageRestrictions: this.generateAgeRestrictions(service),
      included: `Service includes: ${service.includedItems.join(', ')}.`,
      excluded: `Not included: ${service.excludedItems.join(', ')}.`,
      meetingPoint: this.generateMeetingPointTerms(service),
      safetyRequirements: this.generateSafetyTerms(service)
    };

    // Add route-specific terms for passenger transport
    if (service.serviceType.category.code === 'PASSENGER_TRANSPORT') {
      baseTerms.routes = this.generateRouteTerms(service.routes);
      baseTerms.schedule = 'Departure times are fixed. Late arrivals may result in forfeiture.';
    }

    return baseTerms;
  }

  static generateCancellationPolicy(service) {
    // Dynamic cancellation terms based on service type and provider settings
    return {
      customerCancellation: {
        '24_hours_plus': 'Full refund minus processing fee (RM 10)',
        '12_to_24_hours': '50% refund',
        'less_than_12_hours': 'No refund',
        'no_show': 'No refund'
      },
      providerCancellation: {
        'weather': 'Full refund or reschedule option',
        'mechanical': 'Full refund or alternative service',
        'other': 'Full refund plus RM 50 inconvenience compensation'
      },
      modifications: {
        'passenger_count': 'Allowed up to 12 hours before service',
        'date_time': 'Allowed up to 24 hours before service (subject to availability)',
        'package_upgrade': 'Allowed up to 6 hours before service'
      }
    };
  }

  static generateWeatherPolicy(service) {
    return {
      monitoring: 'Weather conditions are monitored 24 hours before service.',
      criteria: {
        wind: 'Service cancelled if sustained winds exceed 25 knots',
        waves: 'Service cancelled if wave height exceeds 1.5 meters',
        visibility: 'Service cancelled if visibility is less than 500 meters',
        storm: 'Service cancelled during thunderstorm warnings'
      },
      notification: 'Customers notified at least 6 hours before scheduled departure.',
      options: {
        reschedule: 'Free rescheduling within 30 days',
        refund: 'Full refund processed within 5-7 business days',
        credit: '110% credit for future bookings (valid 1 year)'
      }
    };
  }

  static generateRefundPolicy() {
    return {
      processing: {
        timeline: 'Refunds processed within 5-7 business days',
        method: 'Refund to original payment method',
        fees: 'Processing fee of RM 10 for customer-initiated cancellations'
      },
      eligibility: {
        weather_cancellation: 'Full refund',
        provider_cancellation: 'Full refund plus compensation',
        customer_cancellation: 'Based on cancellation timeline',
        no_show: 'No refund eligible',
        partial_service: 'Partial refund based on service provided'
      },
      disputes: {
        timeframe: 'Refund disputes must be raised within 7 days',
        process: 'Review process takes 3-5 business days',
        escalation: 'Unresolved disputes escalated to management'
      }
    };
  }
}
```

---

## 💳 Payment Gateway Analysis: Xendit vs Stripe

### Comprehensive Comparison

| Feature | Xendit | Stripe |
|---------|--------|---------|
| **Market Focus** | Southeast Asia | Global |
| **Malaysian Support** | ✅ Excellent | ⚠️ Limited |
| **Local Payment Methods** | ✅ FPX, Boost, GrabPay, TNG | ❌ Limited local methods |
| **Setup Complexity** | ⭐⭐⭐ Medium | ⭐⭐⭐⭐ Easy |
| **Documentation** | ⭐⭐⭐ Good | ⭐⭐⭐⭐⭐ Excellent |
| **Transaction Fees** | 2.9% + RM 0.50 | 3.4% + RM 1.50 |
| **Settlement Time** | T+1 to T+3 | T+2 to T+7 |
| **Multi-party Payouts** | ✅ Built-in | ✅ Stripe Connect |
| **Dispute Handling** | ⭐⭐⭐ Good | ⭐⭐⭐⭐ Very Good |
| **Compliance** | ✅ Malaysian regulations | ⭐⭐⭐ Global compliance |

### Recommendation: **Xendit Primary, Stripe Secondary**

**Reasoning:**
1. **Local Market Fit**: Xendit designed for SEA market
2. **Payment Methods**: Better coverage of Malaysian preferences
3. **Lower Fees**: Significant cost savings at scale
4. **Regulatory Compliance**: Better understanding of Malaysian requirements
5. **Customer Experience**: Familiar payment flows for locals

### Dual Gateway Implementation

```javascript
// services/paymentGatewayService.js
class PaymentGatewayService {
  static async createPaymentIntent(paymentData) {
    const { customerId, amount, currency, paymentMethod, metadata } = paymentData;
    
    // Route to appropriate gateway based on customer profile and payment method
    const gateway = this.selectGateway(paymentData);
    
    try {
      if (gateway === 'xendit') {
        return await this.createXenditPayment(paymentData);
      } else {
        return await this.createStripePayment(paymentData);
      }
    } catch (error) {
      // Fallback to alternative gateway
      console.log(`${gateway} failed, trying fallback`);
      return await this.createFallbackPayment(paymentData, gateway);
    }
  }

  static selectGateway(paymentData) {
    const { paymentMethod, customerLocation, currency } = paymentData;
    
    // Use Xendit for Malaysian customers and local payment methods
    if (customerLocation === 'MY' || 
        ['fpx', 'boost', 'grabpay', 'tng'].includes(paymentMethod)) {
      return 'xendit';
    }
    
    // Use Stripe for international customers and credit cards
    if (customerLocation !== 'MY' || paymentMethod === 'credit_card') {
      return 'stripe';
    }
    
    return 'xendit'; // Default to Xendit for Malaysian market
  }

  static async createXenditPayment(paymentData) {
    const xendit = require('xendit-node');
    const x = new xendit({
      secretKey: process.env.XENDIT_SECRET_KEY
    });

    const { Invoice } = x;
    
    const invoice = await Invoice.createInvoice({
      externalID: `gosea-${paymentData.bookingId}-${Date.now()}`,
      amount: paymentData.amount,
      currency: paymentData.currency || 'IDR', // Xendit primarily IDR
      payerEmail: paymentData.customerEmail,
      description: `GoSea Booking - ${paymentData.serviceName}`,
      invoiceDuration: 1800, // 30 minutes
      successRedirectURL: `${process.env.FRONTEND_URL}/booking/success/${paymentData.bookingId}`,
      failureRedirectURL: `${process.env.FRONTEND_URL}/booking/failed/${paymentData.bookingId}`,
      paymentMethods: this.getXenditPaymentMethods(paymentData.paymentMethod)
    });

    return {
      success: true,
      paymentIntentId: invoice.id,
      paymentUrl: invoice.invoice_url,
      expiresAt: new Date(Date.now() + 30 * 60 * 1000)
    };
  }

  static async createStripePayment(paymentData) {
    const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: paymentData.currency || 'myr',
          product_data: {
            name: `GoSea Booking - ${paymentData.serviceName}`,
            description: `Booking for ${paymentData.passengerCount} passengers`
          },
          unit_amount: Math.round(paymentData.amount * 100) // Convert to cents
        },
        quantity: 1
      }],
      mode: 'payment',
      success_url: `${process.env.FRONTEND_URL}/booking/success/${paymentData.bookingId}`,
      cancel_url: `${process.env.FRONTEND_URL}/booking/failed/${paymentData.bookingId}`,
      metadata: {
        bookingId: paymentData.bookingId,
        customerId: paymentData.customerId
      }
    });

    return {
      success: true,
      paymentIntentId: session.id,
      paymentUrl: session.url,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    };
  }
}
```

---

## 🔄 Refund Management System

### Refund Policy Framework

```javascript
// services/refundService.js
class RefundService {
  static async processRefund(bookingId, refundReason, requestedBy) {
    try {
      const booking = await prisma.booking.findUnique({
        where: { id: bookingId },
        include: {
          payments: true,
          service: { include: { provider: true } },
          customer: { include: { profile: true } }
        }
      });

      // Calculate refund amount based on policy
      const refundCalculation = await this.calculateRefundAmount(booking, refundReason);
      
      if (refundCalculation.eligibleAmount <= 0) {
        return {
          success: false,
          message: 'No refund eligible based on policy',
          policy: refundCalculation.policy
        };
      }

      // Create refund record
      const refund = await prisma.refund.create({
        data: {
          bookingId,
          requestedBy,
          reason: refundReason,
          eligibleAmount: refundCalculation.eligibleAmount,
          processingFee: refundCalculation.processingFee,
          netRefundAmount: refundCalculation.netAmount,
          status: 'PROCESSING',
          policy: refundCalculation.policy,
          calculationDetails: refundCalculation.breakdown
        }
      });

      // Process refund with payment gateway
      const refundResult = await this.executeRefund(refund, booking);

      // Update booking status
      await prisma.booking.update({
        where: { id: bookingId },
        data: { 
          status: 'CANCELLED',
          cancelledAt: new Date(),
          cancellationReason: refundReason
        }
      });

      // Handle boat owner compensation if applicable
      await this.handleProviderCompensation(booking, refundCalculation);

      // Send notifications
      await this.sendRefundNotifications(refund, booking);

      return {
        success: true,
        refund,
        estimatedProcessingTime: '5-7 business days'
      };

    } catch (error) {
      console.error('Refund processing error:', error);
      return {
        success: false,
        message: 'Refund processing failed'
      };
    }
  }

  static async calculateRefundAmount(booking, reason) {
    const now = new Date();
    const serviceDate = new Date(booking.serviceDate);
    const hoursUntilService = (serviceDate - now) / (1000 * 60 * 60);
    
    const totalPaid = booking.payments
      .filter(p => p.status === 'COMPLETED')
      .reduce((sum, p) => sum + parseFloat(p.amount), 0);

    let refundPercentage = 0;
    let processingFee = 0;
    let policy = '';

    switch (reason) {
      case 'CUSTOMER_CANCELLATION':
        if (hoursUntilService >= 24) {
          refundPercentage = 0.9; // 90% refund
          processingFee = 10; // RM 10 processing fee
          policy = '24+ hours: 90% refund minus RM 10 processing fee';
        } else if (hoursUntilService >= 12) {
          refundPercentage = 0.5; // 50% refund
          policy = '12-24 hours: 50% refund';
        } else {
          refundPercentage = 0; // No refund
          policy = 'Less than 12 hours: No refund';
        }
        break;

      case 'WEATHER_CANCELLATION':
        refundPercentage = 1.0; // Full refund
        policy = 'Weather cancellation: Full refund';
        break;

      case 'PROVIDER_CANCELLATION':
        refundPercentage = 1.0; // Full refund plus compensation
        const compensation = Math.min(totalPaid * 0.1, 50); // 10% or RM 50 max
        policy = 'Provider cancellation: Full refund plus compensation';
        return {
          eligibleAmount: totalPaid + compensation,
          processingFee: 0,
          netAmount: totalPaid + compensation,
          policy,
          breakdown: {
            originalAmount: totalPaid,
            refundPercentage: 1.0,
            compensation,
            processingFee: 0
          }
        };

      case 'SERVICE_NOT_PROVIDED':
        refundPercentage = 1.0; // Full refund
        policy = 'Service not provided: Full refund';
        break;

      case 'NO_SHOW':
        refundPercentage = 0; // No refund
        policy = 'No show: No refund eligible';
        break;

      default:
        refundPercentage = 0;
        policy = 'Reason not recognized: No refund';
    }

    const eligibleAmount = totalPaid * refundPercentage;
    const netAmount = Math.max(0, eligibleAmount - processingFee);

    return {
      eligibleAmount,
      processingFee,
      netAmount,
      policy,
      breakdown: {
        originalAmount: totalPaid,
        refundPercentage,
        hoursUntilService: Math.round(hoursUntilService),
        processingFee
      }
    };
  }

  static async executeRefund(refund, booking) {
    // Determine which gateway to use for refund
    const originalPayment = booking.payments.find(p => p.status === 'COMPLETED');
    
    if (originalPayment.gatewayType === 'XENDIT') {
      return await this.processXenditRefund(refund, originalPayment);
    } else if (originalPayment.gatewayType === 'STRIPE') {
      return await this.processStripeRefund(refund, originalPayment);
    } else {
      // Dummy payment - simulate refund
      return await this.processDummyRefund(refund, originalPayment);
    }
  }
}
```

### Refund Database Schema

```sql
-- Refunds table
CREATE TABLE "refunds" (
    "id" TEXT PRIMARY KEY,
    "bookingId" TEXT NOT NULL,
    "requestedBy" TEXT NOT NULL, -- USER_ID who requested
    "reason" TEXT NOT NULL, -- CUSTOMER_CANCELLATION, WEATHER_CANCELLATION, etc.
    "eligibleAmount" DECIMAL NOT NULL,
    "processingFee" DECIMAL DEFAULT 0,
    "netRefundAmount" DECIMAL NOT NULL,
    "status" TEXT NOT NULL, -- PROCESSING, COMPLETED, FAILED, DISPUTED
    "policy" TEXT NOT NULL,
    "calculationDetails" JSONB,
    "gatewayRefundId" TEXT,
    "processedAt" TIMESTAMP,
    "failureReason" TEXT,
    "disputeDetails" JSONB,
    "createdAt" TIMESTAMP DEFAULT now(),
    "updatedAt" TIMESTAMP NOT NULL,
    FOREIGN KEY ("bookingId") REFERENCES "bookings"("id")
);

-- Add refund tracking to payments
ALTER TABLE "payments" ADD COLUMN "refundId" TEXT;
ALTER TABLE "payments" ADD COLUMN "isRefunded" BOOLEAN DEFAULT FALSE;
ALTER TABLE "payments" ADD COLUMN "refundAmount" DECIMAL DEFAULT 0;
```

---

## 💰 Boat Owner Payout System

### Payout Framework

```javascript
// services/payoutService.js
class PayoutService {
  static async processBoatOwnerPayout(bookingId) {
    try {
      const booking = await prisma.booking.findUnique({
        where: { id: bookingId },
        include: {
          service: { 
            include: { 
              provider: { include: { user: { include: { profile: true } } } }
            }
          },
          payments: { where: { status: 'COMPLETED' } },
          assignedBoat: { include: { owner: { include: { profile: true } } } }
        }
      });

      // Calculate payout amounts
      const payoutCalculation = await this.calculatePayoutAmounts(booking);
      
      // Create payout records
      const payouts = await this.createPayoutRecords(booking, payoutCalculation);
      
      // Process actual payouts
      const payoutResults = await Promise.all([
        this.processProviderPayout(payouts.provider),
        this.processBoatOwnerPayout(payouts.boatOwner),
        this.processPlatformRevenue(payouts.platform)
      ]);

      return {
        success: true,
        payouts: payoutResults,
        totalProcessed: payoutCalculation.totalAmount
      };

    } catch (error) {
      console.error('Payout processing error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  static async calculatePayoutAmounts(booking) {
    const totalRevenue = booking.payments
      .reduce((sum, payment) => sum + parseFloat(payment.amount), 0);

    // Platform commission structure
    const platformCommission = 0.15; // 15% platform fee
    const paymentProcessingFee = totalRevenue * 0.034; // 3.4% payment gateway fee
    
    // Calculate net revenue after platform and processing fees
    const platformFee = totalRevenue * platformCommission;
    const netRevenue = totalRevenue - platformFee - paymentProcessingFee;

    // Split between provider and boat owner (if different)
    const isProviderOwnedBoat = booking.service.providerId === booking.assignedBoat.ownerId;
    
    let providerShare, boatOwnerShare;
    
    if (isProviderOwnedBoat) {
      // Provider owns the boat - gets all net revenue
      providerShare = netRevenue;
      boatOwnerShare = 0;
    } else {
      // Split: 60% to provider (service), 40% to boat owner
      providerShare = netRevenue * 0.6;
      boatOwnerShare = netRevenue * 0.4;
    }

    return {
      totalAmount: totalRevenue,
      platformFee,
      paymentProcessingFee,
      netRevenue,
      providerShare,
      boatOwnerShare,
      breakdown: {
        revenue: totalRevenue,
        platformCommission: `${(platformCommission * 100)}%`,
        processingFee: paymentProcessingFee,
        providerSplit: isProviderOwnedBoat ? '100%' : '60%',
        boatOwnerSplit: isProviderOwnedBoat ? '0%' : '40%'
      }
    };
  }

  static async createPayoutRecords(booking, calculation) {
    const payouts = {};

    // Create provider payout record
    if (calculation.providerShare > 0) {
      payouts.provider = await prisma.payout.create({
        data: {
          recipientId: booking.service.provider.userId,
          recipientType: 'PROVIDER',
          bookingId: booking.id,
          amount: calculation.providerShare,
          currency: 'MYR',
          status: 'PENDING',
          payoutType: 'SERVICE_COMMISSION',
          calculationDetails: calculation.breakdown
        }
      });
    }

    // Create boat owner payout record (if different from provider)
    if (calculation.boatOwnerShare > 0) {
      payouts.boatOwner = await prisma.payout.create({
        data: {
          recipientId: booking.assignedBoat.ownerId,
          recipientType: 'BOAT_OWNER',
          bookingId: booking.id,
          amount: calculation.boatOwnerShare,
          currency: 'MYR',
          status: 'PENDING',
          payoutType: 'BOAT_RENTAL_COMMISSION'
        }
      });
    }

    // Record platform revenue
    payouts.platform = await prisma.platformRevenue.create({
      data: {
        bookingId: booking.id,
        commissionAmount: calculation.platformFee,
        processingFeeAmount: calculation.paymentProcessingFee,
        totalPlatformRevenue: calculation.platformFee + calculation.paymentProcessingFee,
        revenueDate: new Date()
      }
    });

    return payouts;
  }

  static async processProviderPayout(payout) {
    try {
      // Get provider bank details
      const providerBankDetails = await prisma.bankAccount.findFirst({
        where: { 
          userId: payout.recipientId,
          isActive: true,
          isVerified: true
        }
      });

      if (!providerBankDetails) {
        await prisma.payout.update({
          where: { id: payout.id },
          data: { 
            status: 'FAILED',
            failureReason: 'No verified bank account found'
          }
        });
        return { success: false, reason: 'No bank account' };
      }

      // Process payout via Xendit (for Malaysian accounts)
      const disbursement = await this.createXenditDisbursement({
        externalID: `payout-provider-${payout.id}`,
        bankCode: providerBankDetails.bankCode,
        accountHolderName: providerBankDetails.accountName,
        accountNumber: providerBankDetails.accountNumber,
        amount: parseFloat(payout.amount),
        description: `GoSea provider payout - Booking ${payout.bookingId.substring(0, 8)}`
      });

      await prisma.payout.update({
        where: { id: payout.id },
        data: {
          status: 'PROCESSING',
          gatewayPayoutId: disbursement.id,
          processedAt: new Date()
        }
      });

      return { success: true, disbursementId: disbursement.id };

    } catch (error) {
      await prisma.payout.update({
        where: { id: payout.id },
        data: {
          status: 'FAILED',
          failureReason: error.message
        }
      });
      return { success: false, reason: error.message };
    }
  }
}
```

### Payout Database Schema

```sql
-- Bank accounts for payouts
CREATE TABLE "bank_accounts" (
    "id" TEXT PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "bankName" TEXT NOT NULL,
    "bankCode" TEXT NOT NULL,
    "accountName" TEXT NOT NULL,
    "accountNumber" TEXT NOT NULL,
    "accountType" TEXT DEFAULT 'SAVINGS', -- SAVINGS, CURRENT
    "isActive" BOOLEAN DEFAULT TRUE,
    "isVerified" BOOLEAN DEFAULT FALSE,
    "verifiedAt" TIMESTAMP,
    "createdAt" TIMESTAMP DEFAULT now(),
    "updatedAt" TIMESTAMP NOT NULL,
    FOREIGN KEY ("userId") REFERENCES "users"("id")
);

-- Payouts table
CREATE TABLE "payouts" (
    "id" TEXT PRIMARY KEY,
    "recipientId" TEXT NOT NULL,
    "recipientType" TEXT NOT NULL, -- PROVIDER, BOAT_OWNER
    "bookingId" TEXT NOT NULL,
    "amount" DECIMAL NOT NULL,
    "currency" TEXT DEFAULT 'MYR',
    "status" TEXT NOT NULL, -- PENDING, PROCESSING, COMPLETED, FAILED
    "payoutType" TEXT NOT NULL, -- SERVICE_COMMISSION, BOAT_RENTAL_COMMISSION
    "gatewayPayoutId" TEXT,
    "calculationDetails" JSONB,
    "failureReason" TEXT,
    "processedAt" TIMESTAMP,
    "completedAt" TIMESTAMP,
    "createdAt" TIMESTAMP DEFAULT now(),
    "updatedAt" TIMESTAMP NOT NULL,
    FOREIGN KEY ("recipientId") REFERENCES "users"("id"),
    FOREIGN KEY ("bookingId") REFERENCES "bookings"("id")
);

-- Platform revenue tracking
CREATE TABLE "platform_revenue" (
    "id" TEXT PRIMARY KEY,
    "bookingId" TEXT NOT NULL,
    "commissionAmount" DECIMAL NOT NULL,
    "processingFeeAmount" DECIMAL NOT NULL,
    "totalPlatformRevenue" DECIMAL NOT NULL,
    "revenueDate" TIMESTAMP NOT NULL,
    "createdAt" TIMESTAMP DEFAULT now(),
    FOREIGN KEY ("bookingId") REFERENCES "bookings"("id")
);
```

---

## 📊 Complete Financial Flow Dashboard

### Revenue Analytics Implementation

```javascript
// components/admin/FinancialDashboard.js
const FinancialDashboard = () => {
  const [metrics, setMetrics] = useState({
    totalRevenue: 0,
    platformCommission: 0,
    pendingPayouts: 0,
    refundsProcessed: 0,
    transactionVolume: 0
  });

  const [revenueBreakdown, setRevenueBreakdown] = useState([]);
  const [payoutQueue, setPayoutQueue] = useState([]);

  useEffect(() => {
    fetchFinancialMetrics();
    fetchRevenueBreakdown();
    fetchPayoutQueue();
  }, []);

  return (
    <div className="space-y-6">
      {/* Revenue Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <MetricCard
          title="Total Revenue"
          value={`RM ${metrics.totalRevenue.toLocaleString()}`}
          change="+12.5%"
          trend="up"
        />
        <MetricCard
          title="Platform Commission"
          value={`RM ${metrics.platformCommission.toLocaleString()}`}
          change="+8.3%"
          trend="up"
        />
        <MetricCard
          title="Pending Payouts"
          value={`RM ${metrics.pendingPayouts.toLocaleString()}`}
          change="-5.2%"
          trend="down"
        />
        <MetricCard
          title="Refunds Processed"
          value={`RM ${metrics.refundsProcessed.toLocaleString()}`}
          change="+2.1%"
          trend="neutral"
        />
        <MetricCard
          title="Transaction Volume"
          value={metrics.transactionVolume}
          change="+15.7%"
          trend="up"
        />
      </div>

      {/* Revenue Breakdown Chart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Revenue Breakdown</h3>
        <RevenueChart data={revenueBreakdown} />
      </div>

      {/* Payout Queue */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Payout Queue</h3>
          <button className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
            Process All Payouts
          </button>
        </div>
        <PayoutQueueTable payouts={payoutQueue} />
      </div>
    </div>
  );
};
```

---

## 🔒 Compliance & Security

### Financial Compliance Framework

```javascript
// services/complianceService.js
class ComplianceService {
  static async performAMLCheck(userId, transactionAmount) {
    // Anti-Money Laundering checks
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { 
        profile: true,
        payments: { 
          where: { 
            createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
          }
        }
      }
    });

    const monthlyVolume = user.payments.reduce((sum, p) => sum + parseFloat(p.amount), 0);
    const flags = [];

    // High-value transaction flag
    if (transactionAmount > 10000) {
      flags.push('HIGH_VALUE_TRANSACTION');
    }

    // High-frequency flag
    if (user.payments.length > 20) {
      flags.push('HIGH_FREQUENCY_USER');
    }

    // Monthly volume flag
    if (monthlyVolume > 50000) {
      flags.push('HIGH_MONTHLY_VOLUME');
    }

    // Incomplete profile flag
    if (!user.profile?.firstName || !user.profile?.phone) {
      flags.push('INCOMPLETE_PROFILE');
    }

    return {
      riskScore: flags.length * 25, // 0-100 scale
      flags,
      requiresManualReview: flags.length >= 2
    };
  }

  static async logTransaction(transactionData) {
    // Comprehensive transaction logging for audit
    await prisma.transactionLog.create({
      data: {
        ...transactionData,
        ipAddress: transactionData.ipAddress,
        userAgent: transactionData.userAgent,
        timestamp: new Date(),
        complianceChecks: {
          amlCheck: await this.performAMLCheck(transactionData.userId, transactionData.amount),
          fraudCheck: await this.performFraudCheck(transactionData)
        }
      }
    });
  }
}
```

---

## 🚀 Implementation Roadmap

### Phase 1: Terms and Conditions (Week 1-2)

- [ ] **Day 1-2**: Terms service and dynamic generation
- [ ] **Day 3-4**: Terms acceptance UI components
- [ ] **Day 5-7**: Integration with booking flow
- [ ] **Week 2**: Legal review and compliance check

### Phase 2: Payment Gateway Integration (Week 3-5)

- [ ] **Week 3**: Xendit integration and testing
- [ ] **Week 4**: Stripe integration as fallback
- [ ] **Week 5**: Gateway selection logic and failover

### Phase 3: Refund System (Week 6-7)

- [ ] **Week 6**: Refund calculation and processing logic
- [ ] **Week 7**: Refund UI and customer communications

### Phase 4: Payout System (Week 8-10)

- [ ] **Week 8**: Payout calculation and bank account management
- [ ] **Week 9**: Disbursement integration with Xendit
- [ ] **Week 10**: Financial dashboard and reporting

### Phase 5: Compliance & Security (Week 11-12)

- [ ] **Week 11**: AML and fraud detection systems
- [ ] **Week 12**: Security audit and compliance review

---

## 📈 Success Metrics

### Financial KPIs

- **Payment Success Rate**: >97%
- **Refund Processing Time**: <5 business days
- **Payout Processing Time**: <3 business days
- **Transaction Dispute Rate**: <0.5%
- **Platform Commission**: 15% average
- **Payment Gateway Fees**: <3.5% average

### User Experience KPIs

- **Terms Acceptance Rate**: >95%
- **Payment Abandonment Rate**: <10%
- **Customer Satisfaction**: >4.5/5 for payment experience
- **Support Tickets**: <2% of transactions require support

---

## 💡 Key Recommendations

### Immediate Priorities

1. **Implement Terms System**: Critical for legal protection
2. **Choose Xendit as Primary**: Better fit for Malaysian market
3. **Build Comprehensive Refund System**: Essential for customer trust
4. **Automated Payout System**: Critical for partner satisfaction

### Future Enhancements

1. **Multi-currency Support**: For international expansion
2. **Subscription Payments**: For regular charter services
3. **Cryptocurrency Payments**: Future-proofing for digital payments
4. **AI Fraud Detection**: Advanced security measures

This comprehensive payment ecosystem plan ensures GoSea has a robust, compliant, and user-friendly financial infrastructure that supports all stakeholders while maintaining high security standards and excellent user experience.