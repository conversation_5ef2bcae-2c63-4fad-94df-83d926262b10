/**
 * Combined Seed Script
 * Runs both production seed and states/postcodes seed
 */

const { execSync } = require('child_process');

async function runCombinedSeed() {
  try {
    console.log('🌱 Starting combined seeding process...\n');
    
    // Run production seed
    console.log('📦 Step 1: Running production seed...');
    execSync('node prisma/seed_production.js', { stdio: 'inherit' });
    console.log('✅ Production seed completed!\n');
    
    // Run states and postcodes seed
    console.log('🗺️  Step 2: Running states and postcodes seed...');
    execSync('node prisma/seed-states-postcodes.js', { stdio: 'inherit' });
    console.log('✅ States and postcodes seed completed!\n');
    
    console.log('🎉 All seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
    process.exit(1);
  }
}

runCombinedSeed();
