generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                      String                   @id @default(cuid())
  email                   String                   @unique
  role                    UserRole
  isActive                Boolean                  @default(true)
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  password                String?
  googleId                String?                  @unique
  emailVerified           Boolean                  @default(false)
  emailVerifiedAt         DateTime?
  lastLoginAt             DateTime?
  loginAttempts           Int                      @default(0)
  lockedUntil             DateTime?
  approvedAt              DateTime?
  approvedBy              String?
  isApproved              Boolean                  @default(false)
  rejectionReason         String?
  affiliateLinks          AffiliateLink[]          @relation("Affiliate")
  boats                   Boat[]                   @relation("BoatOwner")
  bookings                Booking[]                @relation("Customer")
  emailVerificationTokens EmailVerificationToken[]
  passwordResetTokens     PasswordResetToken[]
  profile                 Profile?
  provider                Provider?                @relation("ProviderOwner")
  userSessions            UserSession[]

  @@map("users")
}

model Profile {
  id                 String    @id @default(cuid())
  userId             String    @unique
  firstName          String
  lastName           String
  phone              String?
  profilePicture     String?
  dateOfBirth        DateTime?
  emergencyContact   String?
  language           String    @default("en")
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt
  profileCompletedAt DateTime?
  personalAddress1   String?
  personalAddress2   String?
  personalCity       String?
  personalPostcode   String?
  personalState      String?
  timezone           String?   @default("Asia/Kuala_Lumpur")
  user               User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("profiles")
}

model Provider {
  id                String                  @id @default(cuid())
  userId            String                  @unique
  companyName       String?
  displayName       String
  description       String?
  brn               String?
  operatingLicense  String?
  logoUrl           String?
  coverImageUrl     String?
  rating            Decimal                 @default(0)
  reviewCount       Int                     @default(0)
  isVerified        Boolean                 @default(false)
  isActive          Boolean                 @default(true)
  isAutoGenerated   Boolean                 @default(false)
  createdAt         DateTime                @default(now())
  updatedAt         DateTime                @updatedAt
  agencyName        String?
  brandColors       Json?
  businessAddress1  String?
  businessAddress2  String?
  businessCity      String?
  businessEmail     String?
  businessPhone     String?
  businessPostcode  String?
  businessState     String?
  certifications    Json?
  licenseExpiryDate DateTime?
  totalBookings     Int                     @default(0)
  verifiedAt        DateTime?
  verifiedBy        String?
  websiteUrl        String?
  boats             Boat[]                  @relation("ProviderBoats")
  bookings          Booking[]
  operatingAreas    ProviderOperatingArea[]
  services          ProviderService[]
  user              User                    @relation("ProviderOwner", fields: [userId], references: [id], onDelete: Cascade)

  @@index([companyName])
  @@index([brn])
  @@index([isVerified, isActive])
  @@map("providers")
}

model Jetty {
  id               String                  @id @default(cuid())
  name             String
  code             String                  @unique
  fullName         String
  coordinates      Json?
  facilities       Json?
  operatingHours   Json?
  parkingAvailable Boolean                 @default(false)
  isActive         Boolean                 @default(true)
  createdAt        DateTime                @default(now())
  updatedAt        DateTime                @updatedAt
  city             String
  postcode         String
  state            String
  boats            Boat[]
  operatingAreas   ProviderOperatingArea[]
  routes           Route[]                 @relation("DepartureJetty")

  @@map("jetties")
}

model Destination {
  id                String   @id @default(cuid())
  name              String
  code              String   @unique
  fullName          String
  description       String?
  coordinates       Json?
  imageUrl          String?
  popularActivities String[]
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  state             String
  routes            Route[]  @relation("DestinationRoutes")

  @@map("destinations")
}

model Route {
  id                   String         @id @default(cuid())
  departureJettyId     String
  destinationId        String
  distance             Decimal?
  estimatedDuration    Int?
  difficulty           String?
  seasonalRestrictions Json?
  isActive             Boolean        @default(true)
  createdAt            DateTime       @default(now())
  updatedAt            DateTime       @updatedAt
  bookings             Booking[]
  departureJetty       Jetty          @relation("DepartureJetty", fields: [departureJettyId], references: [id])
  destination          Destination    @relation("DestinationRoutes", fields: [destinationId], references: [id])
  serviceRoutes        ServiceRoute[]

  @@unique([departureJettyId, destinationId])
  @@map("routes")
}

model ServiceCategory {
  id                  String        @id @default(cuid())
  name                String
  code                String        @unique
  description         String?
  iconUrl             String?
  requiresDestination Boolean       @default(false)
  sortOrder           Int           @default(0)
  isActive            Boolean       @default(true)
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt
  serviceTypes        ServiceType[]

  @@map("service_categories")
}

model ServiceType {
  id                         String            @id @default(cuid())
  categoryId                 String
  name                       String
  code                       String            @unique
  description                String?
  defaultDuration            Int?
  requiresRoute              Boolean           @default(false)
  allowsMultipleDestinations Boolean           @default(false)
  isActive                   Boolean           @default(true)
  createdAt                  DateTime          @default(now())
  updatedAt                  DateTime          @updatedAt
  providerServices           ProviderService[]
  category                   ServiceCategory   @relation(fields: [categoryId], references: [id])

  @@map("service_types")
}

model ProviderService {
  id                  String                @id @default(cuid())
  providerId          String
  serviceTypeId       String
  name                String
  description         String?
  basePrice           Decimal
  agePricing          Json?
  duration            Int?
  maxCapacity         Int
  includedItems       String[]
  itinerary           Json?
  requirements        Json?
  isActive            Boolean               @default(true)
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  images              String[]              @default([])
  excludedItems       String[]              @default([])
  specialInstruction  String?
  pricingModel        PricingModel          @default(basic)
  bookings            Booking[]
  provider            Provider              @relation(fields: [providerId], references: [id])
  serviceType         ServiceType           @relation(fields: [serviceTypeId], references: [id])
  serviceAgePricing   ServiceAgePricing[]
  serviceAgeRanges    ServiceAgeRange[]
  serviceAssignments  ServiceAssignment[]
  serviceAvailability ServiceAvailability[]
  servicePackages     ServicePackage[]
  serviceRoutes       ServiceRoute[]
  serviceSchedules    ServiceSchedule[]

  @@map("provider_services")
}

model ServiceRoute {
  id            String          @id @default(cuid())
  serviceId     String
  routeId       String
  priceModifier Decimal         @default(1.0)
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  route         Route           @relation(fields: [routeId], references: [id])
  service       ProviderService @relation(fields: [serviceId], references: [id])

  @@unique([serviceId, routeId])
  @@map("service_routes")
}

model ServiceAssignment {
  id                  String          @id @default(cuid())
  serviceId           String
  boatId              String
  isPrimary           Boolean         @default(false)
  maxCapacityOverride Int?
  isActive            Boolean         @default(true)
  assignedAt          DateTime        @default(now())
  boat                Boat            @relation(fields: [boatId], references: [id])
  service             ProviderService @relation(fields: [serviceId], references: [id])

  @@unique([serviceId, boatId])
  @@index([boatId])
  @@map("service_assignments")
}

model ProviderOperatingArea {
  id         String   @id @default(cuid())
  providerId String
  jettyId    String
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  jetty      Jetty    @relation(fields: [jettyId], references: [id])
  provider   Provider @relation(fields: [providerId], references: [id])

  @@unique([providerId, jettyId])
  @@map("provider_operating_areas")
}

model ServiceSchedule {
  id                String          @id @default(cuid())
  serviceId         String
  dayOfWeek         Int?
  departureTime     String
  availableCapacity Int
  isActive          Boolean         @default(true)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  service           ProviderService @relation(fields: [serviceId], references: [id])

  @@map("service_schedules")
}

model ServiceAvailability {
  id                String          @id @default(cuid())
  serviceId         String
  date              DateTime        @db.Date
  timeSlot          String
  availableCapacity Int
  totalCapacity     Int
  priceModifier     Decimal         @default(1.0)
  isActive          Boolean         @default(true)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  service           ProviderService @relation(fields: [serviceId], references: [id])

  @@unique([serviceId, date, timeSlot])
  @@map("service_availability")
}

model PackageType {
  id              String           @id @default(cuid())
  name            String
  code            String           @unique
  description     String?
  isDefault       Boolean          @default(false)
  sortOrder       Int              @default(0)
  isActive        Boolean          @default(true)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  servicePackages ServicePackage[]

  @@map("package_types")
}

model ServicePackage {
  id            String          @id @default(cuid())
  serviceId     String
  packageTypeId String
  basePrice     Decimal
  agePricing    Json?
  priceModifier Decimal         @default(1.0)
  includedItems String[]
  excludedItems String[]
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  description   String?
  packageType   PackageType     @relation(fields: [packageTypeId], references: [id])
  service       ProviderService @relation(fields: [serviceId], references: [id])

  @@unique([serviceId, packageTypeId])
  @@map("service_packages")
}

model AgeCategory {
  id                String              @id @default(cuid())
  name              String
  code              String              @unique
  description       String?
  minAge            Int?
  maxAge            Int?
  sortOrder         Int                 @default(0)
  isActive          Boolean             @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  isConfigurable    Boolean             @default(true)
  defaultMinAge     Int?
  defaultMaxAge     Int?
  serviceAgePricing ServiceAgePricing[]
  serviceAgeRanges  ServiceAgeRange[]

  @@map("age_categories")
}

model ServiceAgePricing {
  id               String          @id @default(cuid())
  serviceId        String
  ageCategoryId    String
  price            Decimal
  priceType        String          @default("FIXED")
  percentageOfBase Decimal?
  isActive         Boolean         @default(true)
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  ageCategory      AgeCategory     @relation(fields: [ageCategoryId], references: [id])
  service          ProviderService @relation(fields: [serviceId], references: [id])

  @@unique([serviceId, ageCategoryId])
  @@map("service_age_pricing")
}

model ServiceAgeRange {
  id            String          @id @default(cuid())
  serviceId     String
  ageCategoryId String
  minAge        Int
  maxAge        Int?
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  ageCategory   AgeCategory     @relation(fields: [ageCategoryId], references: [id])
  service       ProviderService @relation(fields: [serviceId], references: [id])

  @@unique([serviceId, ageCategoryId])
  @@map("service_age_ranges")
}

model PasswordResetToken {
  id        String    @id @default(cuid())
  userId    String
  token     String    @unique
  expiresAt DateTime
  usedAt    DateTime?
  createdAt DateTime  @default(now())
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_tokens")
}

model EmailVerificationToken {
  id         String    @id @default(cuid())
  userId     String
  token      String    @unique
  expiresAt  DateTime
  verifiedAt DateTime?
  createdAt  DateTime  @default(now())
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("email_verification_tokens")
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String
  sessionToken String   @unique
  refreshToken String   @unique
  expiresAt    DateTime
  ipAddress    String?
  userAgent    String?
  lastActiveAt DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model Boat {
  id                 String              @id @default(cuid())
  ownerId            String
  name               String
  description        String?
  capacity           Int
  basePrice          Decimal?
  status             BoatStatus          @default(DRAFT)
  isActive           Boolean             @default(true)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  galleryImages      Json?
  amenities          Json?
  engineType         String?
  length             Decimal?
  providerId         String?
  registrationNumber String?
  safetyRating       String?
  yearBuilt          Int?
  serviceType        String?
  location           String?
  approvedAt         DateTime?
  approvedBy         String?
  rejectionReason    String?
  jettyId            String?
  enginePower        String?
  material           String?
  boatAmenities      BoatAmenity[]
  availability       BoatAvailability[]
  packages           BoatPackage[]
  photos             BoatPhoto[]
  jetty              Jetty?              @relation(fields: [jettyId], references: [id])
  owner              User                @relation("BoatOwner", fields: [ownerId], references: [id])
  provider           Provider?           @relation("ProviderBoats", fields: [providerId], references: [id])
  bookings           Booking[]
  oldBookings        Booking[]           @relation("BookingBoat")
  serviceAssignments ServiceAssignment[]

  @@map("boats")
}

model BoatPhoto {
  id        String   @id @default(cuid())
  boatId    String
  url       String
  caption   String?
  order     Int      @default(0)
  createdAt DateTime @default(now())
  boat      Boat     @relation(fields: [boatId], references: [id], onDelete: Cascade)

  @@map("boat_photos")
}

model BoatAmenity {
  id     String  @id @default(cuid())
  boatId String
  name   String
  icon   String?
  boat   Boat    @relation(fields: [boatId], references: [id], onDelete: Cascade)

  @@map("boat_amenities")
}

model BoatPackage {
  id            String   @id @default(cuid())
  boatId        String
  name          String
  description   String?
  includedItems String[]
  itinerary     String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  boat          Boat     @relation(fields: [boatId], references: [id], onDelete: Cascade)

  @@map("boat_packages")
}

model BoatAvailability {
  id             String   @id @default(cuid())
  boatId         String
  date           DateTime
  isAvailable    Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  availableSlots Json?
  boat           Boat     @relation(fields: [boatId], references: [id], onDelete: Cascade)

  @@unique([boatId, date])
  @@map("boat_availability")
}

model Booking {
  id                 String           @id @default(cuid())
  customerId         String
  boatId             String?
  serviceDate        DateTime
  passengerCount     Int
  totalAmount        Decimal
  status             BookingStatus    @default(PENDING)
  contactName        String
  contactPhone       String
  contactEmail       String
  affiliateId        String?
  commission         Decimal?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  discountAmount     Decimal          @default(0)
  discountCode       String?
  passengerBreakdown Json?
  serviceTime        String
  assignedBoatId     String?
  baseAmount         Decimal?
  providerId         String?
  routeAmount        Decimal          @default(0)
  routeId            String?
  serviceId          String?
  specialRequests    String?
  serviceType        String?
  affiliateLink      AffiliateLink?   @relation(fields: [affiliateId], references: [id])
  assignedBoat       Boat?            @relation(fields: [assignedBoatId], references: [id])
  boat               Boat?            @relation("BookingBoat", fields: [boatId], references: [id])
  customer           User             @relation("Customer", fields: [customerId], references: [id])
  discount           DiscountCode?    @relation(fields: [discountCode], references: [code])
  provider           Provider?        @relation(fields: [providerId], references: [id])
  route              Route?           @relation(fields: [routeId], references: [id])
  service            ProviderService? @relation(fields: [serviceId], references: [id])
  notifications      Notification[]
  payments           Payment[]

  @@map("bookings")
}

model Payment {
  id                   String        @id @default(cuid())
  bookingId            String
  amount               Decimal
  type                 PaymentType
  status               PaymentStatus @default(PENDING)
  method               String?
  gatewayTransactionId String?
  gatewayResponse      String?
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  booking              Booking       @relation(fields: [bookingId], references: [id])

  @@map("payments")
}

model AffiliateLink {
  id            String    @id @default(cuid())
  affiliateId   String
  code          String    @unique
  url           String
  clickCount    Int       @default(0)
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  jettyId       String?
  serviceTypeId String?
  affiliate     User      @relation("Affiliate", fields: [affiliateId], references: [id])
  bookings      Booking[]

  @@map("affiliate_links")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  bookingId String?
  isRead    Boolean          @default(false)
  sentAt    DateTime?
  createdAt DateTime         @default(now())
  booking   Booking?         @relation(fields: [bookingId], references: [id])

  @@map("notifications")
}

model DiscountCode {
  id            String    @id @default(cuid())
  code          String    @unique
  discountType  String
  discountValue Decimal
  minAmount     Decimal?
  maxDiscount   Decimal?
  validFrom     DateTime?
  validUntil    DateTime?
  usageLimit    Int?
  usedCount     Int       @default(0)
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  bookings      Booking[]

  @@map("discount_codes")
}

model State {
  id        Int        @id
  name      String     @unique
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  postcodes Postcode[]

  @@map("states")
}

model Postcode {
  id        Int      @id @default(autoincrement())
  postcode  String
  city      String
  stateId   Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  state     State    @relation(fields: [stateId], references: [id])

  @@index([stateId])
  @@index([postcode])
  @@map("postcodes")
}

enum UserRole {
  CUSTOMER
  BOAT_OWNER
  AFFILIATE_AGENT
  ADMIN
}

enum BoatStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  REJECTED
  INACTIVE
  MAINTENANCE
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
  NO_SHOW
}

enum PaymentType {
  FULL
  DEPOSIT
  BALANCE
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum NotificationType {
  BOOKING_CONFIRMATION
  PAYMENT_REMINDER
  BOOKING_REMINDER
  CANCELLATION
  AFFILIATE_COMMISSION
}

enum PricingModel {
  basic
  age_based
  package_only
  age_package_based
}
