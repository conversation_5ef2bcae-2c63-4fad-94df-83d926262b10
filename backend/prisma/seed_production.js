const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedProduction() {
    try {


        console.log('🌱 Starting database seeding...');

        // Check if data already exists
        const existingJetties = await prisma.jetty.count();
        if (existingJetties > 0) {
            console.log('🔄 Database already has data, clearing and reseeding...');

            // Clear existing data in reverse dependency order
            await prisma.serviceAvailability.deleteMany();
            await prisma.serviceSchedule.deleteMany();
            await prisma.serviceAgePricing.deleteMany();
            await prisma.serviceAgeRange.deleteMany();
            await prisma.serviceAssignment.deleteMany();
            await prisma.serviceRoute.deleteMany();
            await prisma.servicePackage.deleteMany();
            await prisma.providerService.deleteMany();
            await prisma.boatAmenity.deleteMany();
            await prisma.boat.deleteMany();
            await prisma.providerOperatingArea.deleteMany();
            await prisma.provider.deleteMany();
            await prisma.profile.deleteMany();
            await prisma.affiliateLink.deleteMany();
            await prisma.serviceType.deleteMany();
            await prisma.packageType.deleteMany();
            await prisma.serviceCategory.deleteMany();
            await prisma.ageCategory.deleteMany();
            await prisma.route.deleteMany();
            await prisma.destination.deleteMany();
            await prisma.jetty.deleteMany();
            // Delete sample users (only those with specific IDs)
            await prisma.user.deleteMany({
                where: {
                    id: {
                        in: [
                            'user_marine_adventures', 'user_coral_divers', 'user_island_explorer',
                            'user_customer_001', 'user_affiliate_001', 'user_ag_holiday'
                        ]
                    }
                }
            });

            console.log('🗑️ Existing data cleared');
        }
        console.log('Seeding production data...');

        // ===== FOUNDATIONAL DATA =====

        // 1. Create Jetties (Departure Points)
        console.log('Creating jetties...');
        const jetties = await Promise.all([
            prisma.jetty.create({
                data: {
                    id: 'jeti_kampung_mangkuk',
                    name: 'Jeti Kampung Mangkuk',
                    code: 'KM',
                    fullName: 'Jeti Kampung Mangkuk, Setiu',
                    city: 'Setiu',
                    postcode: '22100',
                    state: 'Terengganu',
                    coordinates: { lat: 5.6667, lng: 102.7333 },
                    facilities: ['parking', 'restroom', 'cafe'],
                    operatingHours: { open: '06:00', close: '18:00' },
                    parkingAvailable: true,
                    isActive: true
                }
            }),
            prisma.jetty.create({
                data: {
                    id: 'jeti_merang',
                    name: 'Jeti Merang',
                    code: 'MR',
                    fullName: 'Jeti Merang, Setiu',
                    city: 'Setiu',
                    postcode: '22100',
                    state: 'Terengganu',
                    coordinates: { lat: 5.6833, lng: 102.7500 },
                    facilities: ['Parking', 'Restroom', 'Ticket Counter'],
                    operatingHours: { open: '06:00', close: '18:00' },
                    parkingAvailable: true,
                    isActive: true
                }
            }),
            prisma.jetty.create({
                data: {
                    id: 'jeti_penarik',
                    name: 'Jeti Penarik',
                    code: 'PN',
                    fullName: 'Jeti Penarik, Setiu',
                    city: 'Setiu',
                    postcode: '22100',
                    state: 'Terengganu',
                    coordinates: { lat: 5.7000, lng: 102.7667 },
                    facilities: ['Parking', 'Restroom'],
                    operatingHours: { open: '06:00', close: '18:00' },
                    parkingAvailable: true,
                    isActive: true
                }
            })
        ]);

        // 2. Create Destinations
        console.log('Creating destinations...');
        const destinations = await Promise.all([
            prisma.destination.create({
                data: {
                    id: 'dest_redang',
                    name: 'Pulau Redang',
                    code: 'RDG',
                    fullName: 'Redang Island Marine Park',
                    description: 'Crystal clear waters and pristine coral reefs',
                    state: 'Terengganu',
                    coordinates: { lat: 5.7689, lng: 103.0158 },
                    imageUrl: '/images/destinations/redang.jpg',
                    popularActivities: ['snorkeling', 'diving', 'beach', 'photography'],
                    isActive: true
                }
            }),
            prisma.destination.create({
                data: {
                    id: 'dest_perhentian',
                    name: 'Pulau Perhentian',
                    code: 'PHT',
                    fullName: 'Perhentian Islands',
                    description: 'Twin tropical paradise islands',
                    state: 'Terengganu',
                    coordinates: { lat: 5.9045, lng: 102.7606 },
                    imageUrl: '/images/destinations/perhentian.jpg',
                    popularActivities: ['snorkeling', 'diving', 'turtle_watching', 'beach'],
                    isActive: true
                }
            }),
            // prisma.destination.create({
            //     data: {
            //         id: 'dest_tioman',
            //         name: 'Pulau Tioman',
            //         code: 'TMN',
            //         fullName: 'Tioman Island',
            //         description: 'Legendary island with diverse marine life',
            //         state: 'Pahang',
            //         coordinates: { lat: 2.8178, lng: 104.1670 },
            //         imageUrl: '/images/destinations/tioman.jpg',
            //         popularActivities: ['diving', 'snorkeling', 'hiking', 'beach'],
            //         isActive: true
            //     }
            // })
        ]);

        // 3. Create Routes (Jetty to Destination connections)
        // Jetty Kampung Mangkuk to Redang, Jetty Merang to Redang, Jetty Penarik to Redang
        // Jetty Kampung Mangkuk to Perhentian, Jetty Merang to Perhentian, Jetty Penarik to Perhentian
        console.log('Creating routes...');
        const routes = await Promise.all([
            prisma.route.create({
                data: {
                    id: 'route_jetty_mangkuk_redang',
                    departureJettyId: jetties[0].id,
                    destinationId: destinations[0].id,
                    distance: 45.5,
                    estimatedDuration: 60,
                    difficulty: 'Easy',
                    seasonalRestrictions: { monsoon: 'Nov-Feb limited service' },
                    isActive: true
                }
            }),
            prisma.route.create({
                data: {
                    id: 'route_jetty_merang_redang',
                    departureJettyId: jetties[1].id,
                    destinationId: destinations[0].id,
                    distance: 21.0,
                    estimatedDuration: 45,
                    difficulty: 'Easy',
                    seasonalRestrictions: { monsoon: 'Dec-Jan weather dependent' },
                    isActive: true
                }
            }),
            prisma.route.create({
                data: {
                    id: 'route_jetty_penarik_redang',
                    departureJettyId: jetties[2].id,
                    destinationId: destinations[0].id,
                    distance: 56.0,
                    estimatedDuration: 90,
                    difficulty: 'Moderate',
                    seasonalRestrictions: { monsoon: 'Nov-Mar reduced frequency' },
                    isActive: true
                }
            }),
            prisma.route.create({
                data: {
                    id: 'route_jetty_mangkuk_perhentian',
                    departureJettyId: jetties[0].id,
                    destinationId: destinations[1].id,
                    distance: 30.0,
                    estimatedDuration: 50,
                    difficulty: 'Easy',
                    seasonalRestrictions: { monsoon: 'Dec-Jan weather dependent' },
                    isActive: true
                }
            }),
            prisma.route.create({
                data: {
                    id: 'route_jetty_merang_perhentian',
                    departureJettyId: jetties[1].id,
                    destinationId: destinations[1].id,
                    distance: 25.0,
                    estimatedDuration: 45,
                    difficulty: 'Easy',
                    seasonalRestrictions: { monsoon: 'Dec-Jan weather dependent' },
                    isActive: true
                }
            }),
            prisma.route.create({
                data: {
                    id: 'route_jetty_penarik_perhentian',
                    departureJettyId: jetties[2].id,
                    destinationId: destinations[1].id,
                    distance: 40.0,
                    estimatedDuration: 70,
                    difficulty: 'Moderate',
                    seasonalRestrictions: { monsoon: 'Nov-Mar reduced frequency' },
                    isActive: true
                }
            })
        ]);

        // 4. Create Service Categories
        console.log('Creating service categories...');
        const serviceCategories = await Promise.all([
            prisma.serviceCategory.create({
                data: {
                    id: 'sc_snorkeling',
                    name: 'Snorkeling',
                    code: 'SNORKELING',
                    description: 'Explore underwater coral reefs and marine life',
                    iconUrl: '/icons/snorkel.svg',
                    requiresDestination: true,
                    sortOrder: 1,
                    isActive: true
                }
            }),
            prisma.serviceCategory.create({
                data: {
                    id: 'sc_island_hopping',
                    name: 'Island Hopping',
                    code: 'ISLAND_HOPPING',
                    description: 'Visit multiple islands in one trip',
                    iconUrl: '/icons/island.svg',
                    requiresDestination: true,
                    sortOrder: 2,
                    isActive: true
                }
            }),
            prisma.serviceCategory.create({
                data: {
                    id: 'cat_passenger_transport',
                    name: 'Passenger Transport',
                    code: 'PASSENGER_TRANSPORT',
                    description: 'Ferry and boat transport services to islands and destinations',
                    requiresDestination: true,
                    sortOrder: 1,
                    isActive: true
                },
            }),
            prisma.serviceCategory.create({
                data: {
                    id: 'sc_diving',
                    name: 'Scuba Diving',
                    code: 'DIVING',
                    description: 'Deep sea diving adventures',
                    iconUrl: '/icons/diving.svg',
                    requiresDestination: true,
                    sortOrder: 3,
                    isActive: true
                }
            }),
            prisma.serviceCategory.create({
                data: {
                    id: 'sc_fishing',
                    name: 'Fishing',
                    code: 'FISHING',
                    description: 'Deep sea and recreational fishing',
                    iconUrl: '/icons/fishing.svg',
                    requiresDestination: false,
                    sortOrder: 4,
                    isActive: true
                }
            }),
            prisma.serviceCategory.create({
                data: {
                    id: 'sc_sunset_cruise',
                    name: 'Sunset Cruise',
                    code: 'SUNSET_CRUISE',
                    description: 'Romantic sunset viewing cruises',
                    iconUrl: '/icons/sunset.svg',
                    requiresDestination: false,
                    sortOrder: 5,
                    isActive: true
                }
            })
        ]);

        // 5. Create Service Types
        console.log('Creating service types...');
        const serviceTypes = await Promise.all([
            prisma.serviceType.create({
                data: {
                    id: 'st_half_day_snorkeling',
                    categoryId: serviceCategories[0].id,
                    name: 'Half Day Snorkeling',
                    code: 'HALF_DAY_SNORKELING',
                    description: 'Half day snorkeling adventure',
                    defaultDuration: 240, // 4 hours
                    requiresRoute: true,
                    allowsMultipleDestinations: false,
                    isActive: true
                }
            }),
            prisma.serviceType.create({
                data: {
                    id: 'st_daytrip_snorkeling',
                    categoryId: serviceCategories[0].id,
                    name: 'Day Trip Snorkeling',
                    code: 'DAYTRIP_SNORKELING',
                    description: 'Day trip snorkeling adventure',
                    defaultDuration: 480, // 8 hours
                    requiresRoute: true,
                    allowsMultipleDestinations: false,
                    isActive: true
                }
            }),
            prisma.serviceType.create({
                data: {
                    id: 'st_premium_diving',
                    categoryId: serviceCategories[2].id,
                    name: 'Premium Diving Experience',
                    code: 'PREMIUM_DIVING',
                    description: 'Professional diving with certified instructors',
                    defaultDuration: 360, // 6 hours
                    requiresRoute: true,
                    allowsMultipleDestinations: false,
                    isActive: true
                }
            }),
            prisma.serviceType.create({
                data: {
                    id: 'st_deep_sea_fishing',
                    categoryId: serviceCategories[3].id,
                    name: 'Deep Sea Fishing',
                    code: 'DEEP_SEA_FISHING',
                    description: 'Deep sea fishing adventure',
                    defaultDuration: 360, // 6 hours
                    requiresRoute: false,
                    allowsMultipleDestinations: false,
                    isActive: true
                }
            }),
            prisma.serviceType.create({
                data: {
                    id: 'st_sunset_cruise',
                    categoryId: serviceCategories[4].id,
                    name: 'Romantic Sunset Cruise',
                    code: 'SUNSET_CRUISE',
                    description: 'Romantic sunset viewing cruise',
                    defaultDuration: 180, // 3 hours
                    requiresRoute: false,
                    allowsMultipleDestinations: false,
                    isActive: true
                }
            }),
            prisma.serviceType.create({
                data: {
                    id: 'st_full_day_island_hopping',
                    categoryId: serviceCategories[1].id,
                    name: 'Full Day Island Hopping',
                    code: 'FULL_DAY_ISLAND_HOPPING',
                    description: 'Full day island hopping experience',
                    defaultDuration: 480, // 8 hours
                    requiresRoute: true,
                    allowsMultipleDestinations: true,
                    isActive: true
                }
            }),
        ]);

        // 6. Create Package Types
        console.log('Creating package types...');
        const packageTypes = await Promise.all([
            prisma.packageType.create({
                data: {
                    id: 'pt_basic',
                    name: 'Basic',
                    code: 'BASIC',
                    description: 'Essential package with basic amenities',
                    isDefault: true,
                    sortOrder: 1,
                    isActive: true
                }
            }),
            prisma.packageType.create({
                data: {
                    id: 'pt_standard',
                    name: 'Standard',
                    code: 'STANDARD',
                    description: 'Standard package with additional amenities',
                    isDefault: false,
                    sortOrder: 2,
                    isActive: true
                }
            }),
            prisma.packageType.create({
                data: {
                    id: 'pt_premium',
                    name: 'Premium',
                    code: 'PREMIUM',
                    description: 'Premium package with luxury amenities',
                    isDefault: false,
                    sortOrder: 3,
                    isActive: true
                }
            }),
            prisma.packageType.create({
                data: {
                    id: 'pt_deluxe',
                    name: 'Deluxe',
                    code: 'DELUXE',
                    description: 'Deluxe package with all premium amenities',
                    isDefault: false,
                    sortOrder: 4,
                    isActive: true
                }
            }),
            prisma.packageType.create({
                data: {
                    id: 'pt_regular',
                    name: 'Regular',
                    code: 'REGULAR',
                    description: 'Regular package with all basic amenities',
                    isDefault: false,
                    sortOrder: 4,
                    isActive: true
                }
            })
        ]);

        // 7. Create Age Categories
        console.log('Creating age categories...');
        const ageCategories = await Promise.all([
            prisma.ageCategory.create({
                data: {
                    id: 'ac_adults',
                    name: 'Adults',
                    code: 'ADULTS',
                    description: 'Adult passengers',
                    minAge: 13,
                    maxAge: 60,
                    isConfigurable: true,
                    defaultMinAge: 13,
                    defaultMaxAge: 60,
                    sortOrder: 1,
                    isActive: true
                }
            }),
            prisma.ageCategory.create({
                data: {
                    id: 'ac_children',
                    name: 'Children',
                    code: 'CHILDREN',
                    description: 'Child passengers',
                    minAge: 4,
                    maxAge: 12,
                    isConfigurable: true,
                    defaultMinAge: 4,
                    defaultMaxAge: 12,
                    sortOrder: 2,
                    isActive: true
                }
            }),
            prisma.ageCategory.create({
                data: {
                    id: 'ac_toddlers',
                    name: 'Toddlers',
                    code: 'TODDLERS',
                    description: 'Toddler passengers',
                    minAge: 0,
                    maxAge: 3,
                    isConfigurable: true,
                    defaultMinAge: 0,
                    defaultMaxAge: 3,
                    sortOrder: 3,
                    isActive: true
                }
            }),
            prisma.ageCategory.create({
                data: {
                    id: 'ac_seniors',
                    name: 'Seniors',
                    code: 'SENIORS',
                    description: 'Senior passengers',
                    minAge: 61,
                    maxAge: null,
                    isConfigurable: true,
                    defaultMinAge: 61,
                    defaultMaxAge: null,
                    sortOrder: 4,
                    isActive: true
                }
            }),
            prisma.ageCategory.create({
                data: {
                    id: 'ac_pwd',
                    name: 'PWD',
                    code: 'PWD',
                    description: 'Persons with disabilities',
                    minAge: null,
                    maxAge: null,
                    isConfigurable: false,
                    defaultMinAge: null,
                    defaultMaxAge: null,
                    sortOrder: 5,
                    isActive: true
                }
            })
        ]);

        // ===== USER AND PROVIDER DATA =====

        // 8. Create Users with Different Roles
        console.log('Creating users...');
        const users = await Promise.all([
            // Boat Owner 1 - Marine Adventures
            prisma.user.create({
                data: {
                    id: 'user_marine_adventures',
                    email: '<EMAIL>',
                    role: 'BOAT_OWNER',
                    emailVerified: true,
                    isActive: true
                }
            }),
            // Boat Owner 2 - Coral Divers
            prisma.user.create({
                data: {
                    id: 'user_coral_divers',
                    email: '<EMAIL>',
                    role: 'BOAT_OWNER',
                    emailVerified: true,
                    isActive: true
                }
            }),
            // Boat Owner 3 - Island Explorer
            prisma.user.create({
                data: {
                    id: 'user_island_explorer',
                    email: '<EMAIL>',
                    role: 'BOAT_OWNER',
                    emailVerified: true,
                    isActive: true
                }
            }),
            // Customer
            prisma.user.create({
                data: {
                    id: 'user_customer_001',
                    email: '<EMAIL>',
                    role: 'CUSTOMER',
                    emailVerified: true,
                    isActive: true
                }
            }),
            // Affiliate Agent
            prisma.user.create({
                data: {
                    id: 'user_affiliate_001',
                    email: '<EMAIL>',
                    role: 'AFFILIATE_AGENT',
                    emailVerified: true,
                    isActive: true
                }
            }),
            // Boat Owner 4 - AG Holiday
            prisma.user.create({
                data: {
                    id: 'user_ag_holiday',
                    email: '<EMAIL>',
                    role: 'BOAT_OWNER',
                    emailVerified: true,
                    isActive: true
                }
            }),
        ]);

        // 9. Create User Profiles (Personal information only - no business data)
        console.log('Creating user profiles...');
        const profiles = await Promise.all([
            // Marine Adventures Profile (Personal info only)
            prisma.profile.create({
                data: {
                    id: 'profile_marine_adventures',
                    userId: users[0].id,
                    firstName: 'Ahmad',
                    lastName: 'Rahman',
                    phone: '+***********',
                    language: 'en',
                    // Personal address (separate from business address)
                    personalAddress1: '45 Taman Seri',
                    personalCity: 'Kuala Terengganu',
                    personalPostcode: '20100',
                    personalState: 'Terengganu'
                }
            }),
            // Coral Divers Profile (Personal info only)
            prisma.profile.create({
                data: {
                    id: 'profile_coral_divers',
                    userId: users[1].id,
                    firstName: 'Siti',
                    lastName: 'Abdullah',
                    phone: '+***********',
                    language: 'ms',
                    // Personal address
                    personalAddress1: '12 Jalan Pantai',
                    personalCity: 'Kuala Besut',
                    personalPostcode: '22200',
                    personalState: 'Terengganu'
                }
            }),
            // Island Explorer Profile (Personal info only)
            prisma.profile.create({
                data: {
                    id: 'profile_island_explorer',
                    userId: users[2].id,
                    firstName: 'Lim',
                    lastName: 'Wei Ming',
                    phone: '+***********',
                    language: 'en',
                    // Personal address
                    personalAddress1: '88 Taman Indah',
                    personalCity: 'Mersing',
                    personalPostcode: '86700',
                    personalState: 'Johor'
                }
            }),
            // Customer Profile (Personal info only)
            prisma.profile.create({
                data: {
                    id: 'profile_customer_001',
                    userId: users[3].id,
                    firstName: 'John',
                    lastName: 'Doe',
                    phone: '+***********',
                    language: 'en'
                }
            }),
            // Affiliate Profile (Personal info only - agencyName moved to Provider)
            prisma.profile.create({
                data: {
                    id: 'profile_affiliate_001',
                    userId: users[4].id,
                    firstName: 'Sarah',
                    lastName: 'Tan',
                    phone: '+***********',
                    language: 'en'
                }
            }),
            // AG Holiday Profile (Personal info only)
            prisma.profile.create({
                data: {
                    id: 'profile_ag_holiday',
                    userId: users[5].id,
                    firstName: 'Koh',
                    lastName: 'Siew Hua',
                    phone: '+***********',
                    language: 'en',
                    // Personal address
                    personalAddress1: '67 Lorong Damai',
                    personalCity: 'Kota Bharu',
                    personalPostcode: '15200',
                    personalState: 'Kelantan'
                }
            })
        ]);

        // 10. Create Providers (Business information consolidated here)
        console.log('Creating providers...');
        const providers = await Promise.all([
            // Marine Adventures Provider
            prisma.provider.create({
                data: {
                    id: 'provider_marine_adventures',
                    userId: users[0].id,
                    companyName: 'Marine Adventures Sdn Bhd',
                    displayName: 'Marine Adventures',
                    description: 'Premium marine experiences in Terengganu waters with professional guides and top-quality equipment.',
                    brn: 'MA-2023-001',
                    operatingLicense: 'ML-2023-001',
                    // Updated field names for new schema
                    businessPhone: '+***********',
                    businessEmail: '<EMAIL>',
                    websiteUrl: 'https://marineadventures.com',
                    // Business address (separate from personal address)
                    businessAddress1: '123 Marina Bay',
                    businessCity: 'Kuala Terengganu',
                    businessPostcode: '20000',
                    businessState: 'Terengganu',
                    // Branding
                    logoUrl: '/images/providers/marine-adventures-logo.jpg',
                    coverImageUrl: '/images/providers/marine-adventures-cover.jpg',
                    // Business metrics
                    rating: 4.8,
                    reviewCount: 156,
                    totalBookings: 450,
                    // Status
                    isVerified: true,
                    isActive: true,
                    verifiedAt: new Date('2023-01-15'),
                    verifiedBy: 'admin'
                }
            }),
            // Coral Divers Provider
            prisma.provider.create({
                data: {
                    id: 'provider_coral_divers',
                    userId: users[1].id,
                    companyName: 'Coral Divers Enterprise',
                    displayName: 'Coral Divers',
                    description: 'Specialized diving and snorkeling tours to pristine coral reefs around Perhentian Islands.',
                    brn: 'CD-2023-002',
                    operatingLicense: 'ML-2023-002',
                    // Updated field names
                    businessPhone: '+***********',
                    businessEmail: '<EMAIL>',
                    websiteUrl: 'https://coraldivers.com',
                    // Business address
                    businessAddress1: '456 Coastal Road',
                    businessCity: 'Kuala Besut',
                    businessPostcode: '22300',
                    businessState: 'Terengganu',
                    // Branding
                    logoUrl: '/images/providers/coral-divers-logo.jpg',
                    coverImageUrl: '/images/providers/coral-divers-cover.jpg',
                    // Business metrics
                    rating: 4.6,
                    reviewCount: 89,
                    totalBookings: 320,
                    // Status
                    isVerified: true,
                    isActive: true,
                    verifiedAt: new Date('2023-02-10'),
                    verifiedBy: 'admin'
                }
            }),
            // Island Explorer Provider
            prisma.provider.create({
                data: {
                    id: 'provider_island_explorer',
                    userId: users[2].id,
                    companyName: 'Island Explorer Tours',
                    displayName: 'Island Explorer',
                    description: 'Comprehensive island hopping and adventure tours to Tioman and surrounding islands.',
                    brn: 'IE-2023-003',
                    operatingLicense: 'ML-2023-003',
                    // Updated field names
                    businessPhone: '+***********',
                    businessEmail: '<EMAIL>',
                    websiteUrl: 'https://islandexplorer.com',
                    // Business address
                    businessAddress1: '789 Harbor Street',
                    businessCity: 'Mersing',
                    businessPostcode: '86800',
                    businessState: 'Johor',
                    // Branding
                    logoUrl: '/images/providers/island-explorer-logo.jpg',
                    coverImageUrl: '/images/providers/island-explorer-cover.jpg',
                    // Business metrics
                    rating: 4.7,
                    reviewCount: 203,
                    totalBookings: 680,
                    // Status
                    isVerified: true,
                    isActive: true,
                    verifiedAt: new Date('2023-01-20'),
                    verifiedBy: 'admin'
                }
            }),
            // AG Holiday Provider
            prisma.provider.create({
                data: {
                    id: 'provider_ag_holiday',
                    userId: users[5].id,
                    companyName: 'AG Holiday',
                    displayName: 'AG Holiday',
                    description: 'Family-friendly holiday tours and activities in Terengganu and surrounding areas.',
                    brn: 'AG-2023-004',
                    operatingLicense: 'ML-2023-004',
                    // Updated field names
                    businessPhone: '+***********',
                    businessEmail: '<EMAIL>',
                    websiteUrl: 'https://agholiday.com',
                    // Business address
                    businessAddress1: '321 Beachfront Drive',
                    businessCity: 'Kota Bharu',
                    businessPostcode: '16000',
                    businessState: 'Kelantan',
                    // Branding
                    logoUrl: '/images/providers/ag-holiday-logo.jpg',
                    coverImageUrl: '/images/providers/ag-holiday-cover.jpg',
                    // Business metrics
                    rating: 4.5,
                    reviewCount: 123,
                    totalBookings: 290,
                    // Status
                    isVerified: true,
                    isActive: true,
                    verifiedAt: new Date('2023-03-05'),
                    verifiedBy: 'admin'
                }
            })
        ]);

        // 11. Create Provider Operating Areas
        console.log('Creating provider operating areas...');
        await Promise.all([
            // Marine Adventures operates from Kuala Terengganu
            prisma.providerOperatingArea.create({
                data: {
                    id: 'poa_marine_kt',
                    providerId: providers[0].id,
                    jettyId: jetties[0].id,
                    isActive: true
                }
            }),
            // Coral Divers operates from Kuala Besut
            prisma.providerOperatingArea.create({
                data: {
                    id: 'poa_coral_kb',
                    providerId: providers[1].id,
                    jettyId: jetties[1].id,
                    isActive: true
                }
            }),
            // Island Explorer operates from Mersing
            prisma.providerOperatingArea.create({
                data: {
                    id: 'poa_island_mg',
                    providerId: providers[2].id,
                    jettyId: jetties[2].id,
                    isActive: true
                }
            }),
            // AG Holiday operates from Setiu
            prisma.providerOperatingArea.create({
                data: {
                    id: 'poa_ag_kt',
                    providerId: providers[3].id,
                    jettyId: jetties[0].id,
                    isActive: true
                }
            })
        ]);

        // 12. Create Boats
        console.log('Creating boats...');
        const boats = await Promise.all([
            // Marine Adventures - Ocean Explorer (Large capacity)
            prisma.boat.create({
                data: {
                    id: 'boat_ocean_explorer',
                    ownerId: users[0].id,
                    providerId: providers[0].id,
                    name: 'Ocean Explorer',
                    description: 'Large capacity speedboat perfect for group adventures',
                    capacity: 20,
                    basePrice: 100.00,
                    status: 'APPROVED',
                    registrationNumber: 'MY-TR-2024-001',
                    yearBuilt: 2023,
                    engineType: 'Twin Outboard 300HP',
                    length: 12.5,
                    safetyRating: 'A+',
                    serviceType: 'Multi-purpose',
                    location: 'Kuala Terengganu',
                    isActive: true,
                    amenities: {
                        seating: 'Cushioned bench seating',
                        shade: 'Retractable canopy',
                        storage: 'Waterproof compartments',
                        sound: 'Bluetooth sound system'
                    }
                }
            }),
            // Marine Adventures - Sea Breeze (Medium capacity)
            prisma.boat.create({
                data: {
                    id: 'boat_sea_breeze',
                    ownerId: users[0].id,
                    providerId: providers[0].id,
                    name: 'Sea Breeze',
                    description: 'Comfortable mid-size boat for intimate groups',
                    capacity: 12,
                    basePrice: 80.00,
                    status: 'APPROVED',
                    registrationNumber: 'MY-TR-2024-002',
                    yearBuilt: 2022,
                    engineType: 'Single Outboard 200HP',
                    length: 9.5,
                    safetyRating: 'A',
                    serviceType: 'Snorkeling & Diving',
                    location: 'Kuala Terengganu',
                    isActive: true,
                    amenities: {
                        seating: 'Padded seating',
                        shade: 'Fixed canopy',
                        storage: 'Dry storage',
                        extras: 'Ladder for easy water access'
                    }
                }
            }),
            // Coral Divers - Reef Master (Diving specialist)
            prisma.boat.create({
                data: {
                    id: 'boat_reef_master',
                    ownerId: users[1].id,
                    providerId: providers[1].id,
                    name: 'Reef Master',
                    description: 'Specialized diving boat with professional equipment',
                    capacity: 8,
                    basePrice: 120.00,
                    status: 'APPROVED',
                    registrationNumber: 'MY-TR-2024-003',
                    yearBuilt: 2024,
                    engineType: 'Twin Outboard 250HP',
                    length: 10.0,
                    safetyRating: 'A+',
                    serviceType: 'Diving Specialist',
                    location: 'Kuala Besut',
                    isActive: true,
                    amenities: {
                        seating: 'Dive platform seating',
                        equipment: 'Professional dive gear storage',
                        safety: 'Emergency oxygen kit',
                        extras: 'Dive ladder and platform'
                    }
                }
            }),
            // Island Explorer - Island Hopper (Multi-destination)
            prisma.boat.create({
                data: {
                    id: 'boat_island_hopper',
                    ownerId: users[2].id,
                    providerId: providers[2].id,
                    name: 'Island Hopper',
                    description: 'Fast and comfortable boat for island hopping adventures',
                    capacity: 15,
                    basePrice: 90.00,
                    status: 'APPROVED',
                    registrationNumber: 'MY-JH-2024-001',
                    yearBuilt: 2023,
                    engineType: 'Twin Outboard 275HP',
                    length: 11.0,
                    safetyRating: 'A',
                    serviceType: 'Island Hopping',
                    location: 'Mersing',
                    isActive: true,
                    amenities: {
                        seating: 'Comfortable bench seating',
                        shade: 'Large sun canopy',
                        storage: 'Multiple storage compartments',
                        comfort: 'Cushioned seating and backrests'
                    }
                }
            }),
            // AG Holiday - Family Fun (Family-friendly)
            prisma.boat.create({
                data: {
                    id: 'boat_1',
                    ownerId: users[5].id,
                    providerId: providers[3].id,
                    name: 'Boat 1',
                    description: 'Family-friendly boat for leisure activities',
                    capacity: 10,
                    basePrice: 70.00,
                    status: 'APPROVED',
                    registrationNumber: 'MY-AG-2024-001',
                    yearBuilt: 2023,
                    engineType: 'Single Outboard 150HP',
                    length: 8.0,
                    safetyRating: 'A',
                    serviceType: 'Snorkeling & Passenger Boat',
                    location: 'Setiu',
                    isActive: true,
                    amenities: {
                        seating: 'Comfortable bench seating',
                        shade: 'Large sun canopy',
                        storage: 'Multiple storage compartments',
                        comfort: 'Cushioned seating and backrests'
                    }
                }
            }),
            prisma.boat.create({
                data: {
                    id: 'boat_2',
                    ownerId: users[5].id,
                    providerId: providers[3].id,
                    name: 'Boat 2',
                    description: 'Family-friendly boat for leisure activities',
                    capacity: 10,
                    basePrice: 70.00,
                    status: 'APPROVED',
                    registrationNumber: 'MY-AG-2024-002',
                    yearBuilt: 2023,
                    engineType: 'Single Outboard 150HP',
                    length: 8.0,
                    safetyRating: 'A',
                    serviceType: 'Snorkeling & Passenger Boat',
                    location: 'Setiu',
                    isActive: true,
                    amenities: {
                        seating: 'Comfortable bench seating',
                        shade: 'Large sun canopy',
                        storage: 'Multiple storage compartments',
                        comfort: 'Cushioned seating and backrests'
                    }
                }
            })
        ]);

        // 13. Create Boat Amenities
        console.log('Creating boat amenities...');
        await Promise.all([
            // Ocean Explorer amenities
            prisma.boatAmenity.create({
                data: { id: 'ba_oe_001', boatId: boats[0].id, name: 'Life Jackets', icon: 'life-jacket' }
            }),
            prisma.boatAmenity.create({
                data: { id: 'ba_oe_002', boatId: boats[0].id, name: 'First Aid Kit', icon: 'medical' }
            }),
            prisma.boatAmenity.create({
                data: { id: 'ba_oe_003', boatId: boats[0].id, name: 'Sound System', icon: 'speaker' }
            }),
            prisma.boatAmenity.create({
                data: { id: 'ba_oe_004', boatId: boats[0].id, name: 'Cooler Box', icon: 'cooler' }
            }),

            // Sea Breeze amenities
            prisma.boatAmenity.create({
                data: { id: 'ba_sb_001', boatId: boats[1].id, name: 'Snorkeling Gear', icon: 'mask' }
            }),
            prisma.boatAmenity.create({
                data: { id: 'ba_sb_002', boatId: boats[1].id, name: 'Life Jackets', icon: 'life-jacket' }
            }),
            prisma.boatAmenity.create({
                data: { id: 'ba_sb_003', boatId: boats[1].id, name: 'Swim Ladder', icon: 'ladder' }
            }),

            // Reef Master amenities
            prisma.boatAmenity.create({
                data: { id: 'ba_rm_001', boatId: boats[2].id, name: 'Diving Equipment', icon: 'diving-tank' }
            }),
            prisma.boatAmenity.create({
                data: { id: 'ba_rm_002', boatId: boats[2].id, name: 'Emergency Oxygen', icon: 'oxygen' }
            }),
            prisma.boatAmenity.create({
                data: { id: 'ba_rm_003', boatId: boats[2].id, name: 'Dive Platform', icon: 'platform' }
            }),

            // Island Hopper amenities
            prisma.boatAmenity.create({
                data: { id: 'ba_ih_001', boatId: boats[3].id, name: 'Beach Equipment', icon: 'beach-umbrella' }
            }),
            prisma.boatAmenity.create({
                data: { id: 'ba_ih_002', boatId: boats[3].id, name: 'Snorkeling Gear', icon: 'mask' }
            }),
            prisma.boatAmenity.create({
                data: { id: 'ba_ih_003', boatId: boats[3].id, name: 'Sun Canopy', icon: 'umbrella' }
            })
        ]);

        // ===== SERVICE CREATION SCENARIOS =====

        // 14. Create Services with Different Pricing Scenarios
        console.log('Creating services with different pricing scenarios...');

        // SCENARIO 1: Basic Pricing (Single passenger field only)
        const basicService = await prisma.providerService.create({
            data: {
                id: 'ps_basic_fishing',
                providerId: providers[0].id,
                serviceTypeId: serviceTypes[3].id, // Deep Sea Fishing
                name: 'Basic Deep Sea Fishing',
                description: 'Simple deep sea fishing experience with basic equipment and guidance.',
                basePrice: 150.00,
                pricingModel: 'basic',
                duration: 360, // 6 hours
                maxCapacity: 20,
                includedItems: ['Fishing equipment', 'Bait', 'Life jackets', 'Basic refreshments'],
                excludedItems: ['Lunch', 'Professional guide', 'Fish cleaning service'],
                specialInstruction: 'Bring your own food and drinks. Basic fishing experience provided.',
                isActive: true,
                images: ['/images/services/basic-fishing-1.jpg', '/images/services/basic-fishing-2.jpg']
            }
        });

        // SCENARIO 2: Package-Only Pricing (Packages + single passenger field)
        const packageOnlyService = await prisma.providerService.create({
            data: {
                id: 'ps_package_sunset_cruise',
                providerId: providers[2].id,
                serviceTypeId: serviceTypes[4].id, // Sunset Cruise
                name: 'Romantic Sunset Cruise Packages',
                description: 'Choose from different sunset cruise packages for the perfect romantic evening.',
                basePrice: 80.00, // Base price for basic package
                pricingModel: 'package_only',
                duration: 180, // 3 hours
                maxCapacity: 15,
                includedItems: ['Welcome drinks', 'Light snacks', 'Romantic ambiance'],
                excludedItems: ['Dinner', 'Photography service', 'Transportation'],
                specialInstruction: 'Perfect for couples and special occasions. Package selection required.',
                isActive: true,
                images: ['/images/services/sunset-cruise-1.jpg', '/images/services/sunset-cruise-2.jpg']
            }
        });

        // SCENARIO 3: Age-Based Pricing (Age breakdown, no packages)
        const ageBasedService = await prisma.providerService.create({
            data: {
                id: 'ps_age_based_snorkeling',
                providerId: providers[1].id,
                serviceTypeId: serviceTypes[0].id, // Half Day Snorkeling
                name: 'Family Snorkeling Adventure',
                description: 'Family-friendly snorkeling with age-appropriate pricing and equipment.',
                basePrice: 100.00, // Base adult price
                pricingModel: 'age_based',
                duration: 240, // 4 hours
                maxCapacity: 8,
                includedItems: ['Professional snorkeling gear', 'Life jackets', 'Professional guide', 'Underwater photos'],
                excludedItems: ['Lunch', 'Transportation', 'Wetsuit rental'],
                specialInstruction: 'Age-based pricing applies. Children equipment provided. Minimum age 3 years.',
                isActive: true,
                images: ['/images/services/family-snorkeling-1.jpg', '/images/services/family-snorkeling-2.jpg']
            }
        });

        // SCENARIO 4: Full Variation (Packages + age breakdown)
        const fullVariationService = await prisma.providerService.create({
            data: {
                id: 'ps_full_variation_island_hopping',
                providerId: providers[2].id,
                serviceTypeId: serviceTypes[1].id, // Full Day Island Hopping
                name: 'Premium Island Hopping Experience',
                description: 'Complete island hopping experience with multiple package options and age-based pricing.',
                basePrice: 120.00, // Base adult price for basic package
                pricingModel: 'age_package_based',
                duration: 480, // 8 hours
                maxCapacity: 15,
                includedItems: ['Island transfers', 'Snorkeling equipment', 'Professional guide', 'Safety equipment'],
                excludedItems: ['Meals (package dependent)', 'Underwater photography', 'Wetsuit rental'],
                isActive: true,
                images: ['/images/services/island-hopping-1.jpg', '/images/services/island-hopping-2.jpg', '/images/services/island-hopping-3.jpg']
            }
        });

        const fullVariationService2 = await prisma.providerService.create({
            data: {
                id: 'ps_daytrip_snorkeling',
                providerId: providers[3].id,
                serviceTypeId: serviceTypes[1].id, // Full Day Island Hopping
                name: 'Day Trip Snorkeling',
                description: 'Day trip snorkeling Pulau Redang. Experience enhanced comfort with more inclusive features of premium package.',
                basePrice: 80.00, // Base adult price for basic package
                pricingModel: 'age_package_based',
                duration: 480, // 8 hours
                maxCapacity: 15,
                includedItems: ['7 checkpoints', '2 ways boat transfer', 'Snorkeling equipment', 'Experienced guide', 'Safety jackets', 'Lunch box'],
                excludedItems: ['Jetty parking fee', 'Jetty access fee by Majlis Daerah (children under 5 years free)'],
                isActive: true,
                images: ['/images/services/day-trip-snorkeling-1.jpg', '/images/services/day-trip-snorkeling-2.jpg', '/images/services/day-trip-snorkeling-3.jpg'],
                itinerary: [
                    { time: '08:15 AM', activity: 'Standby for departure', location: 'Jeti Kampung Mangkuk' },
                    { time: '08:30 AM', activity: 'Breakfast (PREMIUM PACKAGE)', location: 'Jeti Kampung Mangkuk' },
                    { time: '08:45 AM', activity: 'Depart for Redang Island', location: '' },
                    { time: '09:30 AM', activity: 'OOTD at Little Maldives', location: 'Little Maldives' },
                    { time: '09:50 AM', activity: 'Water Confidence', location: '' },
                    { time: '10:30 AM', activity: 'Snorkeling at Nemo Point', location: 'Nemo Point' },
                    { time: '11:30 AM', activity: 'Snorkeling at Turtle Bay', location: 'Turtle Bay' },
                    { time: '01:00 PM', activity: 'Rest at Teluk Dalam Beach', location: 'Teluk Dalam Beach' },
                    { time: '01:00 PM', activity: 'Tour Redang Village (PREMIUM PACKAGE)', location: 'Pulau Redang' },
                    { time: '01:00 PM', activity: 'Lunch Buffet (PREMIUM PACKAGE)', location: '' },
                    { time: '02:30 PM', activity: 'Snorkeling at Coral Point', location: 'Coral Point' },
                    { time: '03:30 PM', activity: 'Snorkeling at Gua Kawah', location: 'Gua Kawah' },
                    { time: '04:30 PM', activity: 'Return to Jeti Kampung Mangkuk', location: 'Jeti Kampung Mangkuk' },
                    { time: '05:15 PM', activity: 'Hi-Tea (PREMIUM PACKAGE)', location: 'Jeti Kampung Mangkuk' },
                ]
            }
        });

        // SCENARIO 5: Premium Diving (Age-based with restrictions)
        const premiumDivingService = await prisma.providerService.create({
            data: {
                id: 'ps_premium_diving',
                providerId: providers[1].id,
                serviceTypeId: serviceTypes[2].id, // Premium Diving
                name: 'Professional Diving Experience',
                description: 'Professional diving experience with certified instructors and premium equipment.',
                basePrice: 200.00,
                pricingModel: 'age_based',
                duration: 360, // 6 hours
                maxCapacity: 8,
                includedItems: ['Professional diving equipment', 'Certified instructor', 'Underwater photography', 'Certification support'],
                excludedItems: ['Diving certification course', 'Equipment purchase', 'Lunch'],
                specialInstruction: 'Minimum age 12 years. Diving certification or supervised diving available.',
                isActive: true,
                images: ['/images/services/premium-diving-1.jpg', '/images/services/premium-diving-2.jpg']
            }
        });



        // 15. Create Service Assignments (Link boats to services)
        console.log('Creating service assignments...');
        await Promise.all([
            // Basic Fishing (Marine Adventures) - Ocean Explorer (20 seats, no override - use full capacity)
            prisma.serviceAssignment.create({
                data: {
                    id: 'sa_basic_fishing_primary',
                    serviceId: basicService.id,
                    boatId: boats[0].id, // Ocean Explorer (20 capacity)
                    isPrimary: true,
                    maxCapacityOverride: null, // Use full boat capacity (20)
                    isActive: true
                }
            }),
            // Basic Fishing (Marine Adventures) - Sea Breeze as secondary boat (multiple boats example)
            prisma.serviceAssignment.create({
                data: {
                    id: 'sa_basic_fishing_secondary',
                    serviceId: basicService.id,
                    boatId: boats[1].id, // Sea Breeze (12 capacity) - same provider
                    isPrimary: false,
                    maxCapacityOverride: 10, // Reduced capacity for smaller secondary boat
                    isActive: true
                }
            }),
            // Package Sunset Cruise (Island Explorer) - Island Hopper (15 seats, reduced to 12 for premium experience)
            prisma.serviceAssignment.create({
                data: {
                    id: 'sa_sunset_cruise',
                    serviceId: packageOnlyService.id,
                    boatId: boats[3].id, // Island Hopper (15 capacity)
                    isPrimary: true,
                    maxCapacityOverride: 12, // Reduced capacity for premium sunset experience
                    isActive: true
                }
            }),
            // Age-based Snorkeling (Coral Divers) - Reef Master (12 seats, reduced to 10 for family safety)
            prisma.serviceAssignment.create({
                data: {
                    id: 'sa_family_snorkeling',
                    serviceId: ageBasedService.id,
                    boatId: boats[2].id, // Reef Master (12 capacity)
                    isPrimary: true,
                    maxCapacityOverride: 10, // Reduced for family-friendly snorkeling with children
                    isActive: true
                }
            }),
            // Full Variation Island Hopping (Island Explorer) - Island Hopper only (since we need same provider)
            prisma.serviceAssignment.create({
                data: {
                    id: 'sa_island_hopping_primary',
                    serviceId: fullVariationService.id,
                    boatId: boats[3].id, // Island Hopper (15 capacity) - belongs to Island Explorer
                    isPrimary: true,
                    maxCapacityOverride: null, // Use full capacity (15)
                    isActive: true
                }
            }),
            // Premium Diving (Coral Divers) - Reef Master (12 seats, reduced to 8 for diving equipment space)
            prisma.serviceAssignment.create({
                data: {
                    id: 'sa_premium_diving',
                    serviceId: premiumDivingService.id,
                    boatId: boats[2].id, // Reef Master (12 capacity)
                    isPrimary: true,
                    maxCapacityOverride: 8, // Reduced for diving equipment and safety requirements
                    isActive: true
                }
            }),
            // Day Trip Snorkeling (Aqua Adventures) - Boat 1 (10 seats, reduced to 8 for premium gear storage)
            prisma.serviceAssignment.create({
                data: {
                    id: 'sa_daytrip_snorkeling',
                    serviceId: fullVariationService2.id,
                    boatId: boats[4].id, // Boat 1 (10 capacity)
                    isPrimary: true,
                    maxCapacityOverride: 8, // Reduced for premium snorkeling equipment storage
                    isActive: true
                }
            }),
            // Additional assignment for Day Trip Snorkeling - Boat 2 as backup
            prisma.serviceAssignment.create({
                data: {
                    id: 'sa_daytrip_snorkeling_backup',
                    serviceId: fullVariationService2.id,
                    boatId: boats[5].id, // Boat 2 (10 capacity)
                    isPrimary: false,
                    maxCapacityOverride: null, // Use full capacity (10) for backup boat
                    isActive: true
                }
            })
        ]);

        // 16. Create Service Packages (for package-based services)
        console.log('Creating service packages...');
        await Promise.all([
            // Sunset Cruise Packages
            prisma.servicePackage.create({
                data: {
                    id: 'sp_sunset_basic',
                    serviceId: packageOnlyService.id,
                    packageTypeId: packageTypes[0].id, // Basic
                    basePrice: 80.00,
                    priceModifier: 1.0,
                    includedItems: ['Welcome drink', 'Light snacks', 'Sunset viewing'],
                    excludedItems: ['Dinner', 'Photography', 'Premium drinks'],
                    isActive: true
                }
            }),
            prisma.servicePackage.create({
                data: {
                    id: 'sp_sunset_premium',
                    serviceId: packageOnlyService.id,
                    packageTypeId: packageTypes[2].id, // Premium
                    basePrice: 120.00,
                    priceModifier: 1.5,
                    includedItems: ['Premium cocktails', 'Gourmet canapés', 'Professional photography', 'Sunset viewing'],
                    excludedItems: ['Full dinner', 'Transportation'],
                    isActive: true
                }
            }),

            // Island Hopping Packages (Full Variation)
            prisma.servicePackage.create({
                data: {
                    id: 'sp_island_basic',
                    serviceId: fullVariationService.id,
                    packageTypeId: packageTypes[0].id, // Basic
                    basePrice: 120.00,
                    priceModifier: 1.0,
                    includedItems: ['Island transfers', 'Snorkeling gear', 'Basic lunch'],
                    excludedItems: ['Premium lunch', 'Photography', 'Beach equipment'],
                    isActive: true
                }
            }),
            prisma.servicePackage.create({
                data: {
                    id: 'sp_island_standard',
                    serviceId: fullVariationService.id,
                    packageTypeId: packageTypes[1].id, // Standard
                    basePrice: 150.00,
                    priceModifier: 1.25,
                    includedItems: ['Island transfers', 'Premium snorkeling gear', 'Buffet lunch', 'Beach equipment'],
                    excludedItems: ['Photography', 'Premium drinks'],
                    isActive: true
                }
            }),
            prisma.servicePackage.create({
                data: {
                    id: 'sp_island_deluxe',
                    serviceId: fullVariationService.id,
                    packageTypeId: packageTypes[3].id, // Deluxe
                    basePrice: 200.00,
                    priceModifier: 1.67,
                    includedItems: ['Island transfers', 'Premium gear', 'Gourmet lunch', 'Photography', 'Beach equipment', 'Premium drinks'],
                    excludedItems: ['Private guide'],
                    isActive: true
                }
            }),
            prisma.servicePackage.create({
                data: {
                    id: 'sp_daytrip_regular',
                    serviceId: fullVariationService2.id,
                    packageTypeId: packageTypes[4].id, // Basic
                    basePrice: 100.00,
                    priceModifier: 1.0,
                    includedItems: ['7 checkpoints', '2 ways boat transfer', 'Snorkeling equipment', 'Experienced guide', 'Safety jackets', 'Lunch box'],
                    excludedItems: ['Jetty parking fee', 'Jetty access fee by Majlis Daerah (children under 5 years free)'],
                    isActive: true,
                    agePricing: {
                        adult: 100.00,
                        child: 80.00,
                        toddler: 0.00,
                        senior: 80.00,
                        pwd: 80.00
                    }
                }
            }),
            prisma.servicePackage.create({
                data: {
                    id: 'sp_daytrip_premium',
                    serviceId: fullVariationService2.id,
                    packageTypeId: packageTypes[2].id, // Premium
                    basePrice: 110.00,
                    priceModifier: 1.5,
                    includedItems: ['7 checkpoints', '2 ways boat transfer', 'Snorkeling equipment & Insurance', 'Experienced guide', 'Safety jackets', 'Breakfast at jetty', 'Lunch buffet, unlimited drinks and hi-teas'],
                    excludedItems: ['Jetty parking fee', 'Jetty access fee by Majlis Daerah (children under 5 years free)'],
                    isActive: true,
                    agePricing: {
                        adult: 130.00,
                        child: 110.00,
                        toddler: 0.00,
                        senior: 110.00,
                        pwd: 110.00
                    },
                    description: 'Premium package with breakfast, buffet lunch, and unlimited drinks.'
                }
            })
        ]);

        // 17. Create Service Age Ranges and Pricing
        console.log('Creating service age ranges and pricing...');

        // Age-based Snorkeling Service (Family-friendly)
        await Promise.all([
            // Age ranges (family-friendly: adults, children, toddlers)
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_snorkel_adults',
                    serviceId: ageBasedService.id,
                    ageCategoryId: ageCategories[0].id, // Adults
                    minAge: 13,
                    maxAge: null,
                    isActive: true
                }
            }),
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_snorkel_children',
                    serviceId: ageBasedService.id,
                    ageCategoryId: ageCategories[1].id, // Children
                    minAge: 3,
                    maxAge: 12,
                    isActive: true
                }
            }),
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_snorkel_toddlers',
                    serviceId: ageBasedService.id,
                    ageCategoryId: ageCategories[2].id, // Toddlers
                    minAge: 0,
                    maxAge: 2,
                    isActive: true
                }
            }),
            // NOTE: No seniors or PWD for snorkeling due to safety considerations

            // Age pricing
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_snorkel_adults',
                    serviceId: ageBasedService.id,
                    ageCategoryId: ageCategories[0].id, // Adults
                    price: 100.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            }),
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_snorkel_children',
                    serviceId: ageBasedService.id,
                    ageCategoryId: ageCategories[1].id, // Children
                    price: 70.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            }),
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_snorkel_toddlers',
                    serviceId: ageBasedService.id,
                    ageCategoryId: ageCategories[2].id, // Toddlers
                    price: 0.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            })
        ]);

        // Full Variation Island Hopping Service (Packages + Age pricing)
        await Promise.all([
            // Age ranges
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_island_adults',
                    serviceId: fullVariationService.id,
                    ageCategoryId: ageCategories[0].id, // Adults
                    minAge: 13,
                    maxAge: null,
                    isActive: true
                }
            }),
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_island_children',
                    serviceId: fullVariationService.id,
                    ageCategoryId: ageCategories[1].id, // Children
                    minAge: 3,
                    maxAge: 12,
                    isActive: true
                }
            }),
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_island_toddlers',
                    serviceId: fullVariationService.id,
                    ageCategoryId: ageCategories[2].id, // Toddlers
                    minAge: 0,
                    maxAge: 2,
                    isActive: true
                }
            }),

            // Age pricing (base prices, will be modified by package multipliers)
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_island_adults',
                    serviceId: fullVariationService.id,
                    ageCategoryId: ageCategories[0].id, // Adults
                    price: 120.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            }),
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_island_children',
                    serviceId: fullVariationService.id,
                    ageCategoryId: ageCategories[1].id, // Children
                    price: 90.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            }),
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_island_toddlers',
                    serviceId: fullVariationService.id,
                    ageCategoryId: ageCategories[2].id, // Toddlers
                    price: 0.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            })
        ]);

        // Premium Diving Service (Age restrictions - only 12+ allowed)
        await Promise.all([
            // Age ranges (restricted to 12+ for diving)
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_diving_adults',
                    serviceId: premiumDivingService.id,
                    ageCategoryId: ageCategories[0].id, // Adults
                    minAge: 13,
                    maxAge: null,
                    isActive: true
                }
            }),
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_diving_youth',
                    serviceId: premiumDivingService.id,
                    ageCategoryId: ageCategories[1].id, // Children (but only 12 years for diving)
                    minAge: 12,
                    maxAge: 12,
                    isActive: true
                }
            }),
            // NOTE: No toddlers, seniors, or PWD for diving due to safety restrictions

            // Age pricing
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_diving_adults',
                    serviceId: premiumDivingService.id,
                    ageCategoryId: ageCategories[0].id, // Adults
                    price: 200.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            }),
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_diving_youth',
                    serviceId: premiumDivingService.id,
                    ageCategoryId: ageCategories[1].id, // Youth (12 years)
                    price: 150.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            })
        ]);

        // Day Trip Snorkeling Service (Age-based pricing)
        await Promise.all([
            // Age ranges (family-friendly: adults, children, toddlers, seniors, PWD)
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_daytrip_adults',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[0].id, // Adults
                    minAge: 13,
                    maxAge: 59,
                    isActive: true
                }
            }),
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_daytrip_children',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[1].id, // Children
                    minAge: 4,
                    maxAge: 12,
                    isActive: true
                }
            }),
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_daytrip_toddlers',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[2].id, // Toddlers
                    minAge: 0,
                    maxAge: 3,
                    isActive: true
                }
            }),
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_daytrip_seniors',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[3].id, // Seniors
                    minAge: 60,
                    maxAge: null,
                    isActive: true
                }
            }),
            prisma.serviceAgeRange.create({
                data: {
                    id: 'sar_daytrip_pwd',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[4].id, // PWD
                    minAge: 0,
                    maxAge: null,
                    isActive: true
                }
            }),
            // Age pricing (base prices, will be modified by package multipliers)
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_daytrip_adults',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[0].id, // Adults
                    price: 100.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            }),
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_daytrip_children',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[1].id, // Children
                    price: 80.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            }),
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_daytrip_toddlers',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[2].id, // Toddlers
                    price: 0.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            }),
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_daytrip_seniors',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[3].id, // Seniors
                    price: 80.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            }),
            prisma.serviceAgePricing.create({
                data: {
                    id: 'sap_daytrip_pwd',
                    serviceId: fullVariationService2.id,
                    ageCategoryId: ageCategories[4].id, // PWD
                    price: 80.00,
                    priceType: 'FIXED',
                    isActive: true
                }
            })
        ]);

        // 18. Create Service Routes (Link services to routes)
        console.log('Creating service routes...');
        await Promise.all([
            // Age-based Snorkeling - Kuala Besut to Perhentian
            prisma.serviceRoute.create({
                data: {
                    id: 'sr_snorkel_perhentian',
                    serviceId: ageBasedService.id,
                    routeId: routes[1].id, // Besut to Perhentian
                    priceModifier: 1.0,
                    isActive: true
                }
            }),

            // Full Variation Island Hopping - Mersing to Tioman
            prisma.serviceRoute.create({
                data: {
                    id: 'sr_island_tioman',
                    serviceId: fullVariationService.id,
                    routeId: routes[2].id, // Mersing to Tioman
                    priceModifier: 1.0,
                    isActive: true
                }
            }),

            // Premium Diving - Kuala Besut to Perhentian
            prisma.serviceRoute.create({
                data: {
                    id: 'sr_diving_perhentian',
                    serviceId: premiumDivingService.id,
                    routeId: routes[1].id, // Besut to Perhentian
                    priceModifier: 1.2, // Premium pricing for diving
                    isActive: true
                }
            }),
            // Day Trip Snorkeling - Kampung Mangkuk to Redang
            prisma.serviceRoute.create({
                data: {
                    id: 'sr_daytrip_redang',
                    serviceId: fullVariationService2.id,
                    routeId: routes[0].id, // Kampung Mangkuk to Redang
                    priceModifier: 1.0,
                    isActive: true
                }
            })
        ]);

        // 19. Create Service Schedules
        console.log('Creating service schedules...');
        await Promise.all([
            // Basic Fishing - Daily morning and afternoon (Ocean Explorer 20 + Sea Breeze 10 = 30 capacity)
            prisma.serviceSchedule.create({
                data: {
                    id: 'ss_fishing_morning',
                    serviceId: basicService.id,
                    dayOfWeek: null, // Every day
                    departureTime: '07:00',
                    availableCapacity: 30,
                    isActive: true
                }
            }),
            prisma.serviceSchedule.create({
                data: {
                    id: 'ss_fishing_afternoon',
                    serviceId: basicService.id,
                    dayOfWeek: null, // Every day
                    departureTime: '13:00',
                    availableCapacity: 30,
                    isActive: true
                }
            }),

            // Sunset Cruise - Daily evening (Island Hopper override: 12 capacity)
            prisma.serviceSchedule.create({
                data: {
                    id: 'ss_sunset_evening',
                    serviceId: packageOnlyService.id,
                    dayOfWeek: null, // Every day
                    departureTime: '17:00',
                    availableCapacity: 12,
                    isActive: true
                }
            }),

            // Family Snorkeling - Morning only (Reef Master override: 10 capacity)
            prisma.serviceSchedule.create({
                data: {
                    id: 'ss_snorkel_morning',
                    serviceId: ageBasedService.id,
                    dayOfWeek: null, // Every day
                    departureTime: '09:00',
                    availableCapacity: 10,
                    isActive: true
                }
            }),

            // Island Hopping - Early morning (Island Hopper: 15 capacity)
            prisma.serviceSchedule.create({
                data: {
                    id: 'ss_island_morning',
                    serviceId: fullVariationService.id,
                    dayOfWeek: null, // Every day
                    departureTime: '08:00',
                    availableCapacity: 15,
                    isActive: true
                }
            }),

            // Premium Diving - Morning and afternoon
            prisma.serviceSchedule.create({
                data: {
                    id: 'ss_diving_morning',
                    serviceId: premiumDivingService.id,
                    dayOfWeek: null, // Every day
                    departureTime: '08:30',
                    availableCapacity: 8,
                    isActive: true
                }
            }),
            prisma.serviceSchedule.create({
                data: {
                    id: 'ss_diving_afternoon',
                    serviceId: premiumDivingService.id,
                    dayOfWeek: null, // Every day
                    departureTime: '14:00',
                    availableCapacity: 8,
                    isActive: true
                }
            }),
            // Day Trip Snorkeling - Daily morning (Boat 1 override 8 + Boat 2: 10 = 18 capacity)
            prisma.serviceSchedule.create({
                data: {
                    id: 'ss_daytrip_morning',
                    serviceId: fullVariationService2.id,
                    dayOfWeek: null, // Every day
                    departureTime: '08:00',
                    availableCapacity: 18,
                    isActive: true
                }
            })
        ]);

        // 20. Create Sample Service Availability (blocking examples only)
        console.log('Creating sample service availability blocks...');
        const today = new Date();
        const availabilityPromises = [];

        // Example 1: Block Christmas Day 2024 for all services
        const christmasDate = new Date('2024-12-25');
        const services = [basicService, packageOnlyService, ageBasedService, fullVariationService, premiumDivingService, fullVariationService2];
        
        for (const service of services) {
            // Block morning and afternoon slots on Christmas
            availabilityPromises.push(
                prisma.serviceAvailability.create({
                    data: {
                        id: `sa_block_christmas_morning_${service.id}`,
                        serviceId: service.id,
                        date: christmasDate,
                        timeSlot: '08:00',
                        availableCapacity: 0,
                        totalCapacity: 0,
                        isActive: false // FALSE = BLOCKED
                    }
                }),
                prisma.serviceAvailability.create({
                    data: {
                        id: `sa_block_christmas_afternoon_${service.id}`,
                        serviceId: service.id,
                        date: christmasDate,
                        timeSlot: '14:00',
                        availableCapacity: 0,
                        totalCapacity: 0,
                        isActive: false // FALSE = BLOCKED
                    }
                })
            );
        }

        // Example 2: Block New Year's Day 2025 for premium services only
        const newYearDate = new Date('2025-01-01');
        const premiumServices = [premiumDivingService, packageOnlyService]; // Premium services
        
        for (const service of premiumServices) {
            availabilityPromises.push(
                prisma.serviceAvailability.create({
                    data: {
                        id: `sa_block_newyear_${service.id}`,
                        serviceId: service.id,
                        date: newYearDate,
                        timeSlot: '08:30',
                        availableCapacity: 0,
                        totalCapacity: 0,
                        isActive: false // FALSE = BLOCKED
                    }
                })
            );
        }

        // Example 3: Block a maintenance day for basic fishing service
        const maintenanceDate = new Date(today);
        maintenanceDate.setDate(today.getDate() + 15); // 15 days from now
        
        availabilityPromises.push(
            prisma.serviceAvailability.create({
                data: {
                    id: `sa_block_maintenance_fishing`,
                    serviceId: basicService.id,
                    date: maintenanceDate,
                    timeSlot: '07:00',
                    availableCapacity: 0,
                    totalCapacity: 0,
                    isActive: false // FALSE = BLOCKED
                }
            }),
            prisma.serviceAvailability.create({
                data: {
                    id: `sa_block_maintenance_fishing_afternoon`,
                    serviceId: basicService.id,
                    date: maintenanceDate,
                    timeSlot: '13:00',
                    availableCapacity: 0,
                    totalCapacity: 0,
                    isActive: false // FALSE = BLOCKED
                }
            })
        );

        // Example 4: Block some random dates for demonstration
        for (let i = 5; i < 30; i += 10) { // Every 10 days starting from day 5
            const blockDate = new Date(today);
            blockDate.setDate(today.getDate() + i);
            
            // Block diving service on some random dates
            availabilityPromises.push(
                prisma.serviceAvailability.create({
                    data: {
                        id: `sa_block_random_diving_${i}`,
                        serviceId: premiumDivingService.id,
                        date: blockDate,
                        timeSlot: '08:30',
                        availableCapacity: 0,
                        totalCapacity: 0,
                        isActive: false // FALSE = BLOCKED
                    }
                })
            );
        }

        await Promise.all(availabilityPromises);
        console.log(`✅ Created ${availabilityPromises.length} availability blocking records`);
        console.log('   - Christmas Day blocked for all services');
        console.log('   - New Year blocked for premium services');
        console.log('   - Maintenance day blocked for fishing service');
        console.log('   - Random dates blocked for diving service demo');

        // 21. Create Sample Affiliate Links
        console.log('Creating sample affiliate links...');
        await Promise.all([
            prisma.affiliateLink.create({
                data: {
                    id: 'al_travel_paradise_001',
                    affiliateId: users[4].id, // Affiliate agent
                    code: 'TRAVEL-PARADISE-SNORKEL',
                    url: 'https://gosea.com/booking?serviceId=ps_age_based_snorkeling&affiliate=TRAVEL-PARADISE-SNORKEL',
                    clickCount: 0,
                    isActive: true,
                    jettyId: jetties[1].id, // Kuala Besut
                    serviceTypeId: serviceTypes[0].id // Snorkeling
                }
            }),
            prisma.affiliateLink.create({
                data: {
                    id: 'al_travel_paradise_002',
                    affiliateId: users[4].id, // Affiliate agent
                    code: 'TRAVEL-PARADISE-ISLAND',
                    url: 'https://gosea.com/booking?serviceId=ps_full_variation_island_hopping&affiliate=TRAVEL-PARADISE-ISLAND',
                    clickCount: 0,
                    isActive: true,
                    jettyId: jetties[2].id, // Mersing
                    serviceTypeId: serviceTypes[1].id // Island Hopping
                }
            })
        ]);

        console.log('✅ Production seed data creation completed successfully!');
        console.log('');
        console.log('📊 Summary of created data:');
        console.log('- 3 Jetties (Kuala Terengganu, Kuala Besut, Mersing)');
        console.log('- 3 Destinations (Redang, Perhentian, Tioman)');
        console.log('- 3 Routes connecting jetties to destinations');
        console.log('- 5 Service Categories (Snorkeling, Island Hopping, Diving, Fishing, Sunset Cruise)');
        console.log('- 5 Service Types with different requirements');
        console.log('- 4 Package Types (Basic, Standard, Premium, Deluxe)');
        console.log('- 5 Age Categories (Adults, Children, Toddlers, Seniors, PWD)');
        console.log('- 5 Users with different roles (3 Boat Owners, 1 Customer, 1 Affiliate)');
        console.log('- 3 Providers with complete profiles');
        console.log('- 4 Boats with different capacities and amenities');
        console.log('- 5 Services demonstrating all pricing scenarios:');
        console.log('  * Basic Pricing: Deep Sea Fishing');
        console.log('  * Package-Only: Sunset Cruise (2 packages)');
        console.log('  * Age-Based: Family Snorkeling');
        console.log('  * Full Variation: Island Hopping (3 packages + age pricing)');
        console.log('  * Premium Diving: Age restrictions + premium pricing');
        console.log('- Service schedules with recurring daily availability');
        console.log('- Availability blocking examples (holidays, maintenance, etc.)');
        console.log('');
        console.log('🔄 New Availability System:');
        console.log('- ServiceSchedule: Defines recurring patterns (takes priority)');
        console.log('- ServiceAvailability: Only used for blocking specific dates');
        console.log('- Capacity: Calculated from ServiceAssignments + actual bookings');
        console.log('');
        console.log('🎯 Ready for testing with realistic data and blocked dates!');
        console.log('- Test booking flow with real capacity calculations');
        console.log('- Test blocked dates functionality');
        console.log('- Test capacity management across multiple boats');
        console.log('- Use admin panel (when ready) to manage date blocking');
        console.log('- 2 Affiliate links for marketing');
        console.log('');
        console.log('🎯 All service creation scenarios from guidelines are now represented!');
        console.log('🚀 Ready for comprehensive testing of the GoSea booking system.');

        console.log('Production data seeding completed!');
    } catch (error) {
        console.error('Seeding failed:', error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

seedProduction();
