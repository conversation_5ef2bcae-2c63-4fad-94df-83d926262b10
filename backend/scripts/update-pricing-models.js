#!/usr/bin/env node

/**
 * Migration Script: Update Pricing Models for Existing Services
 * 
 * This script analyzes existing ProviderService records and updates their
 * pricingModel field based on their current pricing structure:
 * 
 * - basic: Services with only basePrice (no packages, no age pricing)
 * - age_based: Services with ServiceAgePricing entries (no packages)
 * - package_only: Services with ServicePackage entries (no age pricing in packages)
 * - age_package_based: Services with both packages and age pricing
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updatePricingModels() {
  console.log('🚀 Starting pricing model migration...\n');

  try {
    // Get all services with their related pricing data
    const services = await prisma.providerService.findMany({
      include: {
        servicePackages: true,
        serviceAgePricing: true,
        serviceAgeRanges: true
      }
    });

    console.log(`📊 Found ${services.length} services to analyze\n`);

    let basicCount = 0;
    let ageBasedCount = 0;
    let packageOnlyCount = 0;
    let agePackageBasedCount = 0;

    for (const service of services) {
      let pricingModel = 'basic'; // default
      let reasoning = '';

      const hasPackages = service.servicePackages.length > 0;
      const hasAgePricing = service.serviceAgePricing.length > 0;
      const hasAgeRanges = service.serviceAgeRanges.length > 0;

      // Check if packages have age pricing in JSON
      const hasPackageAgePricing = service.servicePackages.some(pkg => 
        pkg.agePricing && typeof pkg.agePricing === 'object' && Object.keys(pkg.agePricing).length > 0
      );

      // Determine pricing model based on relationships
      if (hasPackages && (hasAgePricing || hasAgeRanges || hasPackageAgePricing)) {
        pricingModel = 'age_package_based';
        reasoning = `Has ${service.servicePackages.length} packages, ${service.serviceAgePricing.length} age pricing entries, ${service.serviceAgeRanges.length} age ranges`;
        agePackageBasedCount++;
      } else if (hasPackages && !hasAgePricing && !hasAgeRanges && !hasPackageAgePricing) {
        pricingModel = 'package_only';
        reasoning = `Has ${service.servicePackages.length} packages, no age pricing`;
        packageOnlyCount++;
      } else if (!hasPackages && (hasAgePricing || hasAgeRanges)) {
        pricingModel = 'age_based';
        reasoning = `No packages, has ${service.serviceAgePricing.length} age pricing entries, ${service.serviceAgeRanges.length} age ranges`;
        ageBasedCount++;
      } else {
        pricingModel = 'basic';
        reasoning = `No packages, no age pricing - using basePrice only (${service.basePrice})`;
        basicCount++;
      }

      // Update the service
      await prisma.providerService.update({
        where: { id: service.id },
        data: { pricingModel }
      });

      console.log(`✅ ${service.name}`);
      console.log(`   ID: ${service.id}`);
      console.log(`   Pricing Model: ${pricingModel}`);
      console.log(`   Reasoning: ${reasoning}`);
      console.log(`   Base Price: RM ${service.basePrice}\n`);
    }

    console.log('📈 Migration Summary:');
    console.log(`   Basic Pricing: ${basicCount} services`);
    console.log(`   Age-Based Pricing: ${ageBasedCount} services`);
    console.log(`   Package-Only Pricing: ${packageOnlyCount} services`);
    console.log(`   Age + Package Pricing: ${agePackageBasedCount} services`);
    console.log(`   Total: ${services.length} services\n`);

    console.log('🎉 Pricing model migration completed successfully!');

  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
if (require.main === module) {
  updatePricingModels()
    .then(() => {
      console.log('\n✨ Migration script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { updatePricingModels };
