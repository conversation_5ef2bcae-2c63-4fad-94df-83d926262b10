const express = require('express');
const { PrismaClient } = require('@prisma/client');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const emailService = require('../services/emailService');

const router = express.Router();
const prisma = new PrismaClient();

// Middleware for boat owner authentication
const requireBoatOwner = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      include: {
        profile: true,
        provider: {
          include: {
            boats: {
              select: {
                id: true,
                name: true,
                description: true,
                capacity: true,
                basePrice: true,
                status: true,
                isActive: true,
                registrationNumber: true,
                yearBuilt: true,
                engineType: true,
                enginePower: true,
                material: true,
                length: true,
                safetyRating: true,
                serviceType: true,
                location: true,
                amenities: true,
                galleryImages: true,
                ownerId: true,
                providerId: true,
                createdAt: true,
                updatedAt: true
              }
            },
            services: true,
            operatingAreas: {
              include: {
                jetty: true
              }
            }
          }
        }
      }
    });

    if (!user || user.role !== 'BOAT_OWNER' || !user.isApproved) {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }

    // Ensure provider exists - create if missing
    let provider = user.provider;
    if (!provider) {
      console.log(`Creating missing provider for boat owner: ${user.id}`);
      try {
        provider = await prisma.provider.create({
          data: {
            userId: user.id,
            companyName: user.profile
              ? `${user.profile.firstName} ${user.profile.lastName} Marine Services`
              : 'Marine Services',
            displayName: user.profile
              ? `${user.profile.firstName} ${user.profile.lastName}`
              : 'Boat Owner',
            businessPhone: user.profile?.phone || user.email,
            businessEmail: user.email,
            isAutoGenerated: true,
          },
          include: {
            boats: {
              select: {
                id: true,
                name: true,
                description: true,
                capacity: true,
                basePrice: true,
                status: true,
                isActive: true,
                registrationNumber: true,
                yearBuilt: true,
                engineType: true,
                enginePower: true,
                material: true,
                length: true,
                safetyRating: true,
                serviceType: true,
                location: true,
                amenities: true,
                galleryImages: true,
                ownerId: true,
                providerId: true,
                createdAt: true,
                updatedAt: true
              }
            },
            services: true,
            operatingAreas: {
              include: {
                jetty: true
              }
            }
          }
        });
        console.log(`Provider created successfully for user: ${user.id}`);
      } catch (providerError) {
        console.error('Failed to create provider:', providerError);
        return res.status(500).json({ success: false, message: 'Failed to initialize provider account' });
      }
    }

    req.user = user;
    req.provider = provider;
    next();
  } catch (error) {
    console.error('Auth error:', error);
    res.status(401).json({ success: false, message: 'Invalid token' });
  }
};

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../../uploads/boat-owner');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// =============================================================================
// PROFILE MANAGEMENT ENDPOINTS
// =============================================================================

// Get boat owner profile
router.get('/profile', requireBoatOwner, async (req, res) => {
  try {
    const { user, provider } = req;
    
    // Get additional statistics
    const stats = await prisma.provider.findUnique({
      where: { id: provider.id },
      include: {
        boats: {
          select: {
            id: true,
            name: true,
            status: true,
            capacity: true
          }
        },
        services: {
          select: {
            id: true,
            name: true,
            isActive: true,
            basePrice: true
          }
        },
        _count: {
          select: {
            boats: true,
            services: true
          }
        }
      }
    });

    // Get recent bookings count
    const recentBookings = await prisma.booking.count({
      where: {
        service: {
          providerId: provider.id
        },
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      }
    });

    // Decrypt sensitive profile data before sending to frontend
    const dataProtection = require('../utils/dataProtection');
    const decryptedProfile = user.profile ? dataProtection.decryptProfileData(user.profile) : null;

    // Decrypt sensitive provider data
    const decryptedProvider = { ...provider };
    if (decryptedProvider.businessPhone) {
      try {
        decryptedProvider.businessPhone = dataProtection.decrypt(decryptedProvider.businessPhone);
      } catch (error) {
        console.error('Error decrypting businessPhone:', error);
        decryptedProvider.businessPhone = null;
      }
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          isApproved: user.isApproved,
          createdAt: user.createdAt,
          emailVerified: user.emailVerified,
          emailVerifiedAt: user.emailVerifiedAt,
          approvedAt: user.approvedAt,
          lastLoginAt: user.lastLoginAt,
          profile: decryptedProfile
        },
        provider: {
          ...decryptedProvider,
          stats: {
            totalBoats: stats._count.boats,
            totalServices: stats._count.services,
            recentBookings
          }
        }
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch profile' });
  }
});

// Update boat owner profile
router.put('/profile', requireBoatOwner, async (req, res) => {
  try {
    const { user, provider } = req;
    const {
      // Personal Information
      firstName,
      lastName,
      phone,
      dateOfBirth,
      emergencyContact,
      language,
      timezone,
      personalAddress1,
      personalAddress2,
      personalCity,
      personalPostcode,
      personalState,

      // Business Information
      companyName,
      displayName,
      description,
      brn,
      operatingLicense,
      businessPhone,
      businessEmail,
      websiteUrl,
      agencyName,
      licenseExpiryDate,
      businessAddress1,
      businessAddress2,
      businessCity,
      businessPostcode,
      businessState
    } = req.body;

    // Prepare profile update data
    const profileUpdateData = {};
    if (firstName) profileUpdateData.firstName = firstName;
    if (lastName) profileUpdateData.lastName = lastName;
    if (phone) profileUpdateData.phone = phone;
    if (dateOfBirth) profileUpdateData.dateOfBirth = new Date(dateOfBirth);
    if (emergencyContact) profileUpdateData.emergencyContact = emergencyContact;
    if (language) profileUpdateData.language = language;
    if (timezone) profileUpdateData.timezone = timezone;
    if (personalAddress1) profileUpdateData.personalAddress1 = personalAddress1;
    if (personalAddress2) profileUpdateData.personalAddress2 = personalAddress2;
    if (personalCity) profileUpdateData.personalCity = personalCity;
    if (personalPostcode) profileUpdateData.personalPostcode = personalPostcode;
    if (personalState) profileUpdateData.personalState = personalState;

    // Update user profile if there are changes
    if (Object.keys(profileUpdateData).length > 0) {
      await prisma.profile.update({
        where: { userId: user.id },
        data: profileUpdateData
      });
    }

    // Prepare provider update data
    const providerUpdateData = {};
    if (companyName) providerUpdateData.companyName = companyName;
    if (displayName) providerUpdateData.displayName = displayName;
    if (description) providerUpdateData.description = description;
    if (brn) providerUpdateData.brn = brn;
    if (operatingLicense) providerUpdateData.operatingLicense = operatingLicense;
    if (businessPhone) providerUpdateData.businessPhone = businessPhone;
    if (businessEmail) providerUpdateData.businessEmail = businessEmail;
    if (websiteUrl) providerUpdateData.websiteUrl = websiteUrl;
    if (agencyName) providerUpdateData.agencyName = agencyName;
    if (licenseExpiryDate) providerUpdateData.licenseExpiryDate = new Date(licenseExpiryDate);
    if (businessAddress1) providerUpdateData.businessAddress1 = businessAddress1;
    if (businessAddress2) providerUpdateData.businessAddress2 = businessAddress2;
    if (businessCity) providerUpdateData.businessCity = businessCity;
    if (businessPostcode) providerUpdateData.businessPostcode = businessPostcode;
    if (businessState) providerUpdateData.businessState = businessState;

    // Update provider information
    const updatedProvider = await prisma.provider.update({
      where: { id: provider.id },
      data: providerUpdateData,
      include: {
        boats: true,
        services: true,
        operatingAreas: {
          include: {
            jetty: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: updatedProvider
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ success: false, message: 'Failed to update profile' });
  }
});

// Upload business documents
router.post('/documents/upload', requireBoatOwner, upload.array('documents', 5), async (req, res) => {
  try {
    const { provider } = req;
    const files = req.files;

    if (!files || files.length === 0) {
      return res.status(400).json({ success: false, message: 'No files uploaded' });
    }

    const uploadedFiles = files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      path: `/uploads/boat-owner/${file.filename}`,
      size: file.size,
      mimetype: file.mimetype,
      uploadedAt: new Date()
    }));

    // Store file information in provider's documents field
    const currentDocuments = provider.documents || [];
    const updatedDocuments = [...currentDocuments, ...uploadedFiles];

    await prisma.provider.update({
      where: { id: provider.id },
      data: {
        documents: updatedDocuments
      }
    });

    res.json({
      success: true,
      message: 'Documents uploaded successfully',
      data: uploadedFiles
    });
  } catch (error) {
    console.error('Upload documents error:', error);
    res.status(500).json({ success: false, message: 'Failed to upload documents' });
  }
});

// Get business documents
router.get('/documents', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    
    res.json({
      success: true,
      data: provider.documents || []
    });
  } catch (error) {
    console.error('Get documents error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch documents' });
  }
});

// Upload logo image
router.post('/profile/logo', requireBoatOwner, upload.single('logo'), async (req, res) => {
  try {
    const { provider } = req;
    const file = req.file;

    if (!file) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    // Update provider with logo URL
    const updatedProvider = await prisma.provider.update({
      where: { id: provider.id },
      data: {
        logoUrl: `/uploads/boat-owner/${file.filename}`
      }
    });

    res.json({
      success: true,
      message: 'Logo uploaded successfully',
      data: {
        logoUrl: `/uploads/boat-owner/${file.filename}`
      }
    });
  } catch (error) {
    console.error('Logo upload error:', error);
    res.status(500).json({ success: false, message: 'Failed to upload logo' });
  }
});

// Upload cover image
router.post('/profile/cover', requireBoatOwner, upload.single('cover'), async (req, res) => {
  try {
    const { provider } = req;
    const file = req.file;

    if (!file) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    // Update provider with cover image URL
    const updatedProvider = await prisma.provider.update({
      where: { id: provider.id },
      data: {
        coverImageUrl: `/uploads/boat-owner/${file.filename}`
      }
    });

    res.json({
      success: true,
      message: 'Cover image uploaded successfully',
      data: {
        coverImageUrl: `/uploads/boat-owner/${file.filename}`
      }
    });
  } catch (error) {
    console.error('Cover image upload error:', error);
    res.status(500).json({ success: false, message: 'Failed to upload cover image' });
  }
});

// =============================================================================
// BOAT FLEET MANAGEMENT ENDPOINTS
// =============================================================================

// Get all boats for boat owner
router.get('/boats', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const { page = 1, limit = 10, status, search } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {
      providerId: provider.id,
      ...(status && { status }),
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      })
    };

    const [boats, total] = await Promise.all([
      prisma.boat.findMany({
        where,
        skip,
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' },
        include: {
          photos: {
            orderBy: { order: 'asc' },
            take: 1
          },
          jetty: true  // Use direct jetty relationship instead of provider operating areas
        }
      }),
      prisma.boat.count({ where })
    ]);

    // Transform boats to include jetty info instead of simple location
    const boatsWithJettyInfo = boats.map(boat => {
      return {
        ...boat,
        // Provide default images array if photos don't exist
        images: [...(boat.galleryImages || []), ...(boat.photos || []).map(photo => photo.url).filter(url => !(boat.galleryImages || []).includes(url))],
        // Use direct jetty relationship
        location: boat.jetty
      };
    });

    res.json({
      success: true,
      data: boatsWithJettyInfo,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Get boats error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch boats' });
  }
});

// Get single boat details
router.get('/boats/:id', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const { id } = req.params;

    const boat = await prisma.boat.findFirst({
      where: {
        id,
        providerId: provider.id
      },
      include: {
        photos: {
          orderBy: { order: 'asc' }
        },
        jetty: true  // Use direct jetty relationship instead of provider operating areas
      }
    });

    if (!boat) {
      return res.status(404).json({ success: false, message: 'Boat not found' });
    }

    // If boat is approved, fetch the admin user's name
    let approvedByUser = null;
    if (boat.approvedBy) {
      try {
        const adminUser = await prisma.user.findUnique({
          where: { id: boat.approvedBy },
          select: {
            profile: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        });
        
        if (adminUser && adminUser.profile) {
          // Capitalize first letter of each word in the name
          const capitalizeWords = (str) => {
            return str.split(' ').map(word => 
              word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            ).join(' ');
          };
          
          const fullName = `${adminUser.profile.firstName} ${adminUser.profile.lastName}`;
          approvedByUser = capitalizeWords(fullName);
        } else {
          approvedByUser = 'Admin User';
        }
      } catch (error) {
        console.error('Error fetching approvedBy user:', error);
        approvedByUser = 'Admin User';
      }
    }

    // Remove provider from the response and replace location with jetty info
    const { provider: boatProvider, ...boatWithoutProvider } = boat;
    
    const boatWithJettyInfo = {
      ...boatWithoutProvider,
      // Provide default images array if photos don't exist
      images: [...(boat.galleryImages || []), ...(boat.photos || []).map(photo => photo.url).filter(url => !(boat.galleryImages || []).includes(url))],
      // Use direct jetty relationship
      location: boat.jetty,
      // Replace approvedBy ID with admin name
      approvedBy: approvedByUser
    };

    res.json({
      success: true,
      data: boatWithJettyInfo
    });
  } catch (error) {
    console.error('Get boat error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch boat' });
  }
});

// Create new boat
router.post('/boats', requireBoatOwner, upload.array('images', 10), async (req, res) => {
  try {
    const { provider } = req;
    const {
      name,
      description,
      capacity,
      registrationNumber,
      yearBuilt,
      engineType,
      enginePower,
      material,
      length,
      safetyRating,
      serviceType,
      jettyId,  // Change from location to jettyId
      amenities,
      specifications
    } = req.body;

    console.log('Boat creation request:', {
      name,
      capacity,
      registrationNumber,
      serviceType,
      jettyId,
      providerId: provider.id,
      ownerId: req.user.id
    });

    // Validate required fields
    if (!name || !capacity || !registrationNumber) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: name, capacity, and registration number are required'
      });
    }

    // Process uploaded images
    const images = req.files ? req.files.map(file => `/uploads/boat-owner/${file.filename}`) : [];

    // Parse JSON fields safely
    let parsedAmenities = {};

    try {
      parsedAmenities = amenities ? JSON.parse(amenities) : {};
    } catch (e) {
      console.error('Error parsing amenities:', e);
      parsedAmenities = {};
    }

    const boat = await prisma.boat.create({
      data: {
        name,
        description,
        capacity: parseInt(capacity),
        registrationNumber,
        yearBuilt: yearBuilt ? parseInt(yearBuilt) : null,
        engineType,
        enginePower,
        material,
        length: length ? parseFloat(length) : null,
        safetyRating,
        serviceType,
        jettyId,  // Use jettyId instead of location
        amenities: parsedAmenities,
        galleryImages: images,
        status: 'PENDING_APPROVAL',
        ownerId: req.user.id,
        providerId: provider.id
      }
    });

    // Send email notifications to all admin users
    try {
      const adminUsers = await prisma.user.findMany({
        where: { role: 'ADMIN' },
        include: { profile: true }
      });

      const approvalLink = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/admin/boats/${boat.id}`;

      for (const admin of adminUsers) {
        await emailService.sendBoatApprovalNotification({
          adminEmail: admin.email,
          boatOwner: {
            email: req.user.email,
            profile: req.user.profile,
            provider: provider
          },
          boat: {
            name: boat.name,
            registrationNumber: boat.registrationNumber,
            capacity: boat.capacity,
            location: boat.location,
            description: boat.description
          },
          approvalLink
        });
      }

      console.log(`Boat approval notifications sent to ${adminUsers.length} admin(s)`);
    } catch (emailError) {
      console.error('Failed to send boat approval notifications:', emailError);
      // Don't fail the boat creation if email fails
    }

    res.status(201).json({
      success: true,
      message: 'Boat created successfully',
      data: boat
    });
  } catch (error) {
    console.error('Create boat error:', error);

    // Provide more specific error messages
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: 'A boat with this registration number already exists'
      });
    }

    if (error.code === 'P2003') {
      return res.status(400).json({
        success: false,
        message: 'Invalid reference data provided'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create boat',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Update boat details
router.put('/boats/:id', requireBoatOwner, upload.array('images', 10), async (req, res) => {
  try {
    const { provider } = req;
    const { id } = req.params;
    const {
      name,
      description,
      capacity,
      registrationNumber,
      yearBuilt,
      engineType,
      enginePower,
      material,
      length,
      safetyRating,
      serviceType,
      jettyId,  // Change from location to jettyId
      amenities,
      specifications,
      status,
      existingImages,
      // Individual specification fields
      fuelCapacity
    } = req.body;

    console.log('Update boat request:', {
      id,
      providerId: provider.id,
      name,
      capacity,
      registrationNumber,
      yearBuilt,
      engineType,
      length,
      safetyRating,
      serviceType,
      jettyId,
      amenities,
      specifications,
      status,
      existingImages,
      fuelCapacity,
      enginePower,
      material,
      files: req.files ? req.files.length : 0
    });

    // Check if boat belongs to this provider
    const existingBoat = await prisma.boat.findFirst({
      where: {
        id,
        providerId: provider.id
      }
    });

    if (!existingBoat) {
      return res.status(404).json({ success: false, message: 'Boat not found' });
    }

    // Process uploaded images
    const newImages = req.files ? req.files.map(file => `/uploads/boat-owner/${file.filename}`) : [];
    const keepExistingImages = existingImages ? JSON.parse(existingImages) : [];
    const allImages = [...keepExistingImages, ...newImages];

    // Parse JSON fields
    const parsedAmenities = amenities ? JSON.parse(amenities) : existingBoat.amenities;

    const updatedBoat = await prisma.boat.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(description && { description }),
        ...(capacity && { capacity: parseInt(capacity) }),
        ...(registrationNumber && { registrationNumber }),
        ...(yearBuilt && { yearBuilt: parseInt(yearBuilt) }),
        ...(engineType && { engineType }),
        ...(enginePower && { enginePower }),
        ...(material && { material }),
        ...(length && { length: parseFloat(length) }),
        ...(safetyRating && { safetyRating }),
        ...(serviceType && { serviceType }),
        ...(jettyId && { jettyId }),  // Use jettyId instead of location
        ...(status && { status }),
        amenities: parsedAmenities,
        galleryImages: allImages
      }
    });

    res.json({
      success: true,
      message: 'Boat updated successfully',
      data: updatedBoat
    });
  } catch (error) {
    console.error('Update boat error:', error);
    console.error('Update boat error stack:', error.stack);
    res.status(500).json({ success: false, message: 'Failed to update boat', error: error.message });
  }
});

// Delete boat
router.delete('/boats/:id', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const { id } = req.params;

    // Check if boat belongs to this provider
    const boat = await prisma.boat.findFirst({
      where: {
        id,
        providerId: provider.id
      }
    });

    if (!boat) {
      return res.status(404).json({ success: false, message: 'Boat not found' });
    }

    // Check if boat is assigned to any services
    const serviceAssignments = await prisma.serviceAssignment.count({
      where: { boatId: id }
    });

    if (serviceAssignments > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete boat that is assigned to services'
      });
    }

    await prisma.boat.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Boat deleted successfully'
    });
  } catch (error) {
    console.error('Delete boat error:', error);
    res.status(500).json({ success: false, message: 'Failed to delete boat' });
  }
});

// =============================================================================
// SERVICE MANAGEMENT ENDPOINTS
// =============================================================================

// Get all services for boat owner
router.get('/services', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const { page = 1, limit = 10, status, category, search } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {
      providerId: provider.id,
      ...(status && { status }),
      ...(category && { category }),
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      })
    };

    console.log('Fetching services with filters:', { providerId: provider.id, page, limit, status, category, search });
    console.log('Where clause:', where);

    const [services, total] = await Promise.all([
      prisma.providerService.findMany({
        where,
        skip,
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' },
        include: {
          serviceType: true,
          serviceRoutes: {
            include: {
              route: {
                include: {
                  departureJetty: true,
                  destination: true
                }
              }
            }
          },
          serviceAssignments: {
            include: {
              boat: {
                select: {
                  id: true,
                  name: true,
                  capacity: true,
                  status: true
                }
              }
            }
          },
          servicePackages: {
            include: {
              packageType: true
            }
          },
          serviceAgeRanges: {
            include: {
              ageCategory: true
            }
          },
          serviceAgePricing: {
            include: {
              ageCategory: true
            }
          },
          serviceSchedules: true
        }
      }),
      prisma.providerService.count({ where })
    ]);

    console.log('Services fetched successfully:', { count: services.length, total });

    res.json({
      success: true,
      data: services,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Get services error:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ success: false, message: 'Failed to fetch services', error: error.message });
  }
});

// Get single service details
router.get('/services/:id', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const { id } = req.params;

    const service = await prisma.providerService.findFirst({
      where: {
        id,
        providerId: provider.id
      },
      include: {
        serviceType: true,
        serviceRoutes: {
          include: {
            route: {
              include: {
                departureJetty: true,
                destination: true
              }
            }
          }
        },
        serviceAssignments: {
          include: {
            boat: true
          }
        },
        servicePackages: true,
        serviceAgeRanges: true,
        serviceAgePricing: true,
        serviceSchedules: true
      }
    });

    if (!service) {
      return res.status(404).json({ success: false, message: 'Service not found' });
    }

    res.json({
      success: true,
      data: service
    });
  } catch (error) {
    console.error('Get service error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch service' });
  }
});

// Create new service
router.post('/services', requireBoatOwner, upload.array('images', 10), async (req, res) => {
  try {
    const { provider } = req;
    const {
      name,
      description,
      category,
      pricing,
      packages,
      duration,
      maxCapacity,
      minCapacity,
      includedItems,
      excludedItems,
      specialInstructions,
      cancellationPolicy,
      termsConditions,
      routes,
      boatIds
    } = req.body;

    // Process uploaded images
    const images = req.files ? req.files.map(file => `/uploads/boat-owner/${file.filename}`) : [];

    // Parse JSON fields
    const parsedPricing = pricing ? JSON.parse(pricing) : {};
    const parsedPackages = packages ? JSON.parse(packages) : [];
    const parsedIncludedItems = includedItems ? JSON.parse(includedItems) : [];
    const parsedExcludedItems = excludedItems ? JSON.parse(excludedItems) : [];
    const parsedRoutes = routes ? JSON.parse(routes) : [];
    const parsedBoatIds = boatIds ? JSON.parse(boatIds) : [];

    // Create service
    const service = await prisma.providerService.create({
      data: {
        name,
        description,
        category,
        pricing: parsedPricing,
        packages: parsedPackages,
        duration: duration ? parseInt(duration) : null,
        maxCapacity: maxCapacity ? parseInt(maxCapacity) : null,
        minCapacity: minCapacity ? parseInt(minCapacity) : 1,
        includedItems: parsedIncludedItems,
        excludedItems: parsedExcludedItems,
        specialInstructions,
        cancellationPolicy,
        termsConditions,
        images,
        status: 'ACTIVE',
        providerId: provider.id
      }
    });

    // Create routes if provided
    if (parsedRoutes.length > 0) {
      await prisma.serviceRoute.createMany({
        data: parsedRoutes.map(route => ({
          serviceId: service.id,
          departureJettyId: route.departureJettyId,
          destinationId: route.destinationId,
          duration: route.duration || duration,
          price: route.price || 0
        }))
      });
    }

    // Assign boats if provided
    if (parsedBoatIds.length > 0) {
      await prisma.serviceAssignment.createMany({
        data: parsedBoatIds.map(boatId => ({
          serviceId: service.id,
          boatId
        }))
      });
    }

    // Fetch the complete service with relations
    const completeService = await prisma.providerService.findUnique({
      where: { id: service.id },
      include: {
        routes: {
          include: {
            departureJetty: true,
            destination: true
          }
        },
        serviceAssignments: {
          include: {
            boat: {
              select: {
                id: true,
                name: true,
                capacity: true,
                status: true
              }
            }
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Service created successfully',
      data: completeService
    });
  } catch (error) {
    console.error('Create service error:', error);
    res.status(500).json({ success: false, message: 'Failed to create service' });
  }
});

// Create new service with wizard form (complete relationship creation)
router.post('/services/wizard', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const {
      name,
      description,
      serviceTypeId,
      duration,
      maxCapacity,
      basePrice,
      pricingModel,
      includedItems,
      excludedItems,
      specialInstruction,
      routes,
      serviceAssignments,
      servicePackages,
      serviceAgeRanges,
      serviceAgePricing,
      serviceSchedules,
      providerId
    } = req.body;

    // Validate that the service belongs to the authenticated provider
    if (providerId !== provider.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only create services for your own provider'
      });
    }

    // Additional validation: Ensure provider exists and is active
    const providerExists = await prisma.provider.findUnique({
      where: { id: provider.id },
      select: { id: true, isActive: true }
    });

    if (!providerExists || !providerExists.isActive) {
      return res.status(400).json({
        success: false,
        message: 'Provider account is not active or does not exist'
      });
    }

    // Validate service type exists
    if (serviceTypeId) {
      const serviceTypeExists = await prisma.serviceType.findUnique({
        where: { id: serviceTypeId },
        select: { id: true, isActive: true }
      });

      if (!serviceTypeExists || !serviceTypeExists.isActive) {
        return res.status(400).json({
          success: false,
          message: 'Invalid or inactive service type provided'
        });
      }
    }

    // Validate boats exist and are available for assignment
    if (serviceAssignments && Array.isArray(serviceAssignments) && serviceAssignments.length > 0) {
      for (const assignment of serviceAssignments) {
        const boat = await prisma.boat.findUnique({
          where: { id: assignment.boatId },
          select: {
            id: true,
            status: true,
            providerId: true,
            serviceAssignments: {
              where: { isPrimary: true, isActive: true },
              select: { id: true }
            }
          }
        });

        if (!boat) {
          return res.status(400).json({
            success: false,
            message: `Boat with ID ${assignment.boatId} not found`
          });
        }

        if (boat.status !== 'APPROVED') {
          return res.status(400).json({
            success: false,
            message: `Boat with ID ${assignment.boatId} is not approved`
          });
        }

        if (boat.providerId !== provider.id) {
          return res.status(403).json({
            success: false,
            message: `Boat with ID ${assignment.boatId} does not belong to your provider`
          });
        }

        if (assignment.isPrimary && boat.serviceAssignments.length > 0) {
          return res.status(400).json({
            success: false,
            message: `Boat with ID ${assignment.boatId} is already assigned as primary to another service`
          });
        }
      }
    }

    // Calculate the optimal basePrice based on pricing model
    let calculatedBasePrice = basePrice ? parseFloat(basePrice) : 0;

    if (pricingModel === 'age_based' && serviceAgePricing && Array.isArray(serviceAgePricing) && serviceAgePricing.length > 0) {
      // Age-based: Find the lowest price from all age categories
      const agePrices = serviceAgePricing.map(pricing => parseFloat(pricing.price));
      calculatedBasePrice = Math.min(...agePrices);
    } else if (pricingModel === 'package_only' && servicePackages && Array.isArray(servicePackages) && servicePackages.length > 0) {
      // Package-only: Find the lowest price from all packages
      const packagePrices = servicePackages.map(pkg => parseFloat(pkg.basePrice));
      calculatedBasePrice = Math.min(...packagePrices);
    } else if (pricingModel === 'age_package_based' && servicePackages && Array.isArray(servicePackages) && servicePackages.length > 0) {
      // Age + Package: Find the lowest price across all package/age combinations
      let allPrices = [];

      servicePackages.forEach(pkg => {
        if (pkg.agePricing && typeof pkg.agePricing === 'object') {
          // Package has age-specific pricing
          const ageSpecificPrices = Object.values(pkg.agePricing).map(price => parseFloat(price));
          allPrices.push(...ageSpecificPrices);
        } else {
          // Package has uniform pricing
          allPrices.push(parseFloat(pkg.basePrice));
        }
      });

      if (allPrices.length > 0) {
        calculatedBasePrice = Math.min(...allPrices);
      }
    }

    // Start a transaction to ensure data consistency
    const service = await prisma.$transaction(async (prisma) => {
      // Create the main service
      const newService = await prisma.providerService.create({
        data: {
          name,
          description,
          serviceTypeId,
          duration: duration ? parseInt(duration) : null,
          maxCapacity: maxCapacity ? parseInt(maxCapacity) : null,
          basePrice: calculatedBasePrice,
          pricingModel: pricingModel || 'basic',
          includedItems: includedItems || [],
          excludedItems: excludedItems || [],
          specialInstruction: specialInstruction || null,
          providerId: provider.id,
          isActive: true
        }
      });

      // Create service routes
      if (routes && Array.isArray(routes) && routes.length > 0) {
        await prisma.serviceRoute.createMany({
          data: routes.map(route => ({
            serviceId: newService.id,
            routeId: route.routeId,
            priceModifier: route.priceModifier ? parseFloat(route.priceModifier) : 1.0,
            isActive: true
          }))
        });
      }

      // Create service assignments
      if (serviceAssignments && Array.isArray(serviceAssignments) && serviceAssignments.length > 0) {
        await prisma.serviceAssignment.createMany({
          data: serviceAssignments.map((assignment, index) => ({
            serviceId: newService.id,
            boatId: assignment.boatId,
            isPrimary: assignment.isPrimary || index === 0, // First assignment is primary by default
            maxCapacityOverride: assignment.maxCapacityOverride ? parseInt(assignment.maxCapacityOverride) : null,
            isActive: true
          }))
        });
      }

      // Create service packages
      if (servicePackages && Array.isArray(servicePackages) && servicePackages.length > 0) {
        await prisma.servicePackage.createMany({
          data: servicePackages.map(pkg => ({
            serviceId: newService.id,
            packageTypeId: pkg.packageTypeId,
            basePrice: pkg.basePrice ? parseFloat(pkg.basePrice) : 0,
            priceModifier: pkg.priceModifier ? parseFloat(pkg.priceModifier) : 1.0,
            includedItems: pkg.includedItems || [],
            excludedItems: pkg.excludedItems || [],
            isActive: true,
            agePricing: pkg.agePricing || null
          }))
        });
      }

      // Create service age ranges
      if (serviceAgeRanges && Array.isArray(serviceAgeRanges) && serviceAgeRanges.length > 0) {
        await prisma.serviceAgeRange.createMany({
          data: serviceAgeRanges.map(range => ({
            serviceId: newService.id,
            ageCategoryId: range.ageCategoryId,
            minAge: range.minAge ? parseInt(range.minAge) : 0,
            maxAge: range.maxAge ? parseInt(range.maxAge) : null,
            isActive: true
          }))
        });
      }

      // Create service age pricing
      if (serviceAgePricing && Array.isArray(serviceAgePricing) && serviceAgePricing.length > 0) {
        await prisma.serviceAgePricing.createMany({
          data: serviceAgePricing.map(pricing => ({
            serviceId: newService.id,
            ageCategoryId: pricing.ageCategoryId,
            price: pricing.price ? parseFloat(pricing.price) : 0,
            priceType: pricing.priceType || 'FIXED',
            isActive: true
          }))
        });
      }

      // Create service schedules
      if (serviceSchedules && Array.isArray(serviceSchedules) && serviceSchedules.length > 0) {
        await prisma.serviceSchedule.createMany({
          data: serviceSchedules.map(schedule => ({
            serviceId: newService.id,
            dayOfWeek: schedule.dayOfWeek !== null ? parseInt(schedule.dayOfWeek) : null,
            departureTime: schedule.departureTime,
            availableCapacity: schedule.availableCapacity ? parseInt(schedule.availableCapacity) : 0,
            isActive: true
          }))
        });
      }

      return newService;
    });

    // Fetch the complete service with all relations
    const completeService = await prisma.providerService.findUnique({
      where: { id: service.id },
      include: {
        serviceType: true,
        serviceRoutes: {
          include: {
            route: {
              include: {
                departureJetty: true,
                destination: true
              }
            }
          }
        },
        serviceAssignments: {
          include: {
            boat: true
          }
        },
        servicePackages: {
          include: {
            packageType: true
          }
        },
        serviceAgeRanges: {
          include: {
            ageCategory: true
          }
        },
        serviceAgePricing: {
          include: {
            ageCategory: true
          }
        },
        serviceSchedules: true
      }
    });

    res.status(201).json({
      success: true,
      message: 'Service created successfully with all relationships',
      data: completeService
    });
  } catch (error) {
    console.error('Create service with wizard error:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      meta: error.meta,
      stack: error.stack
    });

    // Provide more specific error messages based on error type
    let errorMessage = 'Failed to create service with all relationships';
    let statusCode = 500;

    if (error.code === 'P2002') {
      errorMessage = 'A service with this name already exists or there is a duplicate constraint violation';
      statusCode = 400;
    } else if (error.code === 'P2003') {
      errorMessage = 'Invalid reference to related data (boat, service type, route, etc.)';
      statusCode = 400;
    } else if (error.code === 'P2025') {
      errorMessage = 'Referenced record not found (boat, service type, route, etc.)';
      statusCode = 404;
    } else if (error.message && error.message.includes('Boat is already assigned')) {
      errorMessage = 'The selected boat is already assigned as primary to another service';
      statusCode = 400;
    } else if (error.message && error.message.includes('Foreign key constraint')) {
      errorMessage = 'Invalid service type, route, or other reference data provided';
      statusCode = 400;
    }

    res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: error.message,
      details: error.code ? `Database error code: ${error.code}` : null
    });
  }
});

// Update existing service
router.put('/services/:id', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const { id: serviceId } = req.params;
    const {
      name,
      description,
      serviceTypeId,
      duration,
      maxCapacity,
      basePrice,
      pricingModel,
      includedItems,
      excludedItems,
      specialInstruction,
      routes,
      serviceAssignments,
      servicePackages,
      serviceAgeRanges,
      serviceAgePricing,
      serviceSchedules,
      itinerary
    } = req.body;

    // Verify service exists and belongs to the provider
    const existingService = await prisma.providerService.findFirst({
      where: {
        id: serviceId,
        providerId: provider.id
      },
      select: { id: true, name: true }
    });

    if (!existingService) {
      return res.status(404).json({
        success: false,
        message: 'Service not found or you do not have permission to edit it'
      });
    }

    // Calculate the optimal basePrice based on pricing model
    let calculatedBasePrice = basePrice ? parseFloat(basePrice) : 0;

    if (pricingModel === 'age_based' && serviceAgePricing && Array.isArray(serviceAgePricing) && serviceAgePricing.length > 0) {
      const agePrices = serviceAgePricing.map(pricing => parseFloat(pricing.price));
      calculatedBasePrice = Math.min(...agePrices);
    } else if (pricingModel === 'package_only' && servicePackages && Array.isArray(servicePackages) && servicePackages.length > 0) {
      const packagePrices = servicePackages.map(pkg => parseFloat(pkg.basePrice));
      calculatedBasePrice = Math.min(...packagePrices);
    } else if (pricingModel === 'age_package_based' && servicePackages && Array.isArray(servicePackages) && servicePackages.length > 0) {
      let allPrices = [];
      servicePackages.forEach(pkg => {
        if (pkg.agePricing && typeof pkg.agePricing === 'object') {
          const ageSpecificPrices = Object.values(pkg.agePricing).map(price => parseFloat(price));
          allPrices.push(...ageSpecificPrices);
        } else {
          allPrices.push(parseFloat(pkg.basePrice));
        }
      });
      if (allPrices.length > 0) {
        calculatedBasePrice = Math.min(...allPrices);
      }
    }

    // Update service in a transaction
    const updatedService = await prisma.$transaction(async (prisma) => {
      // Update main service record
      const service = await prisma.providerService.update({
        where: { id: serviceId },
        data: {
          name: name || existingService.name,
          description,
          serviceTypeId,
          duration: duration ? parseInt(duration) : undefined,
          maxCapacity: maxCapacity ? parseInt(maxCapacity) : undefined,
          basePrice: calculatedBasePrice,
          pricingModel: pricingModel || 'basic',
          includedItems: includedItems || [],
          excludedItems: excludedItems || [],
          specialInstruction,
          itinerary: itinerary ? JSON.stringify(itinerary) : null,
          updatedAt: new Date()
        }
      });

      // Update service routes
      if (routes && Array.isArray(routes)) {
        // Delete existing routes
        await prisma.serviceRoute.deleteMany({
          where: { serviceId: service.id }
        });

        // Create new routes
        if (routes.length > 0) {
          await prisma.serviceRoute.createMany({
            data: routes.map(route => ({
              serviceId: service.id,
              routeId: route.routeId,
              priceModifier: route.priceModifier || 1.0,
              isActive: true
            }))
          });
        }
      }

      // Update service assignments
      if (serviceAssignments && Array.isArray(serviceAssignments)) {
        // Delete existing assignments
        await prisma.serviceAssignment.deleteMany({
          where: { serviceId: service.id }
        });

        // Create new assignments
        if (serviceAssignments.length > 0) {
          await prisma.serviceAssignment.createMany({
            data: serviceAssignments.map(assignment => ({
              serviceId: service.id,
              boatId: assignment.boatId,
              isPrimary: assignment.isPrimary || false,
              maxCapacityOverride: assignment.maxCapacityOverride ? parseInt(assignment.maxCapacityOverride) : null,
              isActive: true
            }))
          });
        }
      }

      // Update service packages
      if (servicePackages && Array.isArray(servicePackages)) {
        // Delete existing packages
        await prisma.servicePackage.deleteMany({
          where: { serviceId: service.id }
        });

        // Create new packages
        if (servicePackages.length > 0) {
          await prisma.servicePackage.createMany({
            data: servicePackages.map(pkg => ({
              serviceId: service.id,
              packageTypeId: pkg.packageTypeId,
              basePrice: parseFloat(pkg.basePrice) || 0,
              priceModifier: parseFloat(pkg.priceModifier) || 1.0,
              includedItems: pkg.includedItems || [],
              excludedItems: pkg.excludedItems || [],
              agePricing: pkg.agePricing ? JSON.stringify(pkg.agePricing) : null,
              isActive: true
            }))
          });
        }
      }

      // Update age pricing
      if (serviceAgePricing && Array.isArray(serviceAgePricing)) {
        // Delete existing age pricing
        await prisma.serviceAgePricing.deleteMany({
          where: { serviceId: service.id }
        });

        // Create new age pricing
        if (serviceAgePricing.length > 0) {
          await prisma.serviceAgePricing.createMany({
            data: serviceAgePricing.map(pricing => ({
              serviceId: service.id,
              ageCategoryId: pricing.ageCategoryId,
              price: parseFloat(pricing.price) || 0,
              isActive: true
            }))
          });
        }
      }

      // Update age ranges
      if (serviceAgeRanges && Array.isArray(serviceAgeRanges)) {
        // Delete existing age ranges
        await prisma.serviceAgeRange.deleteMany({
          where: { serviceId: service.id }
        });

        // Create new age ranges
        if (serviceAgeRanges.length > 0) {
          await prisma.serviceAgeRange.createMany({
            data: serviceAgeRanges.map(range => ({
              serviceId: service.id,
              ageCategoryId: range.ageCategoryId,
              minAge: range.minAge ? parseInt(range.minAge) : 0,
              maxAge: range.maxAge ? parseInt(range.maxAge) : null,
              isActive: true
            }))
          });
        }
      }

      // Update service schedules
      if (serviceSchedules && Array.isArray(serviceSchedules)) {
        // Delete existing schedules
        await prisma.serviceSchedule.deleteMany({
          where: { serviceId: service.id }
        });

        // Create new schedules
        if (serviceSchedules.length > 0) {
          await prisma.serviceSchedule.createMany({
            data: serviceSchedules.map(schedule => ({
              serviceId: service.id,
              dayOfWeek: schedule.dayOfWeek !== null ? parseInt(schedule.dayOfWeek) : null,
              departureTime: schedule.departureTime,
              availableCapacity: schedule.availableCapacity ? parseInt(schedule.availableCapacity) : 0,
              isActive: true
            }))
          });
        }
      }

      return service;
    });

    // Fetch the complete updated service with all relations
    const completeService = await prisma.providerService.findUnique({
      where: { id: updatedService.id },
      include: {
        serviceType: true,
        serviceRoutes: {
          include: {
            route: {
              include: {
                departureJetty: true,
                destination: true
              }
            }
          }
        },
        serviceAssignments: {
          include: {
            boat: true
          }
        },
        servicePackages: {
          include: {
            packageType: true
          }
        },
        serviceAgeRanges: {
          include: {
            ageCategory: true
          }
        },
        serviceAgePricing: {
          include: {
            ageCategory: true
          }
        },
        serviceSchedules: true
      }
    });

    res.json({
      success: true,
      message: 'Service updated successfully',
      data: completeService
    });
  } catch (error) {
    console.error('Update service error:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      meta: error.meta
    });

    // Provide specific error messages
    let errorMessage = 'Failed to update service';
    let statusCode = 500;

    if (error.code === 'P2002') {
      errorMessage = 'A service with this name already exists or there is a duplicate constraint violation';
      statusCode = 400;
    } else if (error.code === 'P2003') {
      errorMessage = 'Invalid reference to related data (boat, service type, route, etc.)';
      statusCode = 400;
    } else if (error.code === 'P2025') {
      errorMessage = 'Referenced record not found (boat, service type, route, etc.)';
      statusCode = 404;
    }

    res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: error.message,
      details: error.code ? `Database error code: ${error.code}` : null
    });
  }
});

// =============================================================================
// BOOKING MANAGEMENT ENDPOINTS
// =============================================================================

// Get bookings for boat owner
router.get('/bookings', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const {
      page = 1,
      limit = 10,
      status,
      dateFrom,
      dateTo,
      serviceId
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {
      service: {
        providerId: provider.id
      },
      ...(status && { status }),
      ...(serviceId && { serviceId }),
      ...(dateFrom || dateTo) && {
        bookingDate: {
          ...(dateFrom && { gte: new Date(dateFrom) }),
          ...(dateTo && { lte: new Date(dateTo) })
        }
      }
    };

    const [bookings, total] = await Promise.all([
      prisma.booking.findMany({
        where,
        skip,
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            include: {
              profile: true
            }
          },
          service: {
            select: {
              id: true,
              name: true,
              category: true,
              images: true
            }
          },
          route: {
            include: {
              departureJetty: true,
              destination: true
            }
          }
        }
      }),
      prisma.booking.count({ where })
    ]);

    res.json({
      success: true,
      data: bookings,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Get bookings error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch bookings' });
  }
});

// Get single booking details
router.get('/bookings/:id', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const { id } = req.params;

    const booking = await prisma.booking.findFirst({
      where: {
        id,
        service: {
          providerId: provider.id
        }
      },
      include: {
        user: {
          include: {
            profile: true
          }
        },
        service: {
          include: {
            routes: {
              include: {
                departureJetty: true,
                destination: true
              }
            }
          }
        },
        route: {
          include: {
            departureJetty: true,
            destination: true
          }
        }
      }
    });

    if (!booking) {
      return res.status(404).json({ success: false, message: 'Booking not found' });
    }

    res.json({
      success: true,
      data: booking
    });
  } catch (error) {
    console.error('Get booking error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch booking' });
  }
});

// Update booking status
router.put('/bookings/:id/status', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const { id } = req.params;
    const { status, notes } = req.body;

    // Validate status
    const validStatuses = ['PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ success: false, message: 'Invalid status' });
    }

    // Check if booking belongs to this provider
    const booking = await prisma.booking.findFirst({
      where: {
        id,
        service: {
          providerId: provider.id
        }
      }
    });

    if (!booking) {
      return res.status(404).json({ success: false, message: 'Booking not found' });
    }

    const updatedBooking = await prisma.booking.update({
      where: { id },
      data: {
        status,
        ...(notes && { notes })
      },
      include: {
        user: {
          include: {
            profile: true
          }
        },
        service: {
          select: {
            id: true,
            name: true,
            category: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Booking status updated successfully',
      data: updatedBooking
    });
  } catch (error) {
    console.error('Update booking status error:', error);
    res.status(500).json({ success: false, message: 'Failed to update booking status' });
  }
});

// =============================================================================
// ANALYTICS ENDPOINTS
// =============================================================================

// Get booking analytics
router.get('/analytics/bookings', requireBoatOwner, async (req, res) => {
  try {
    const { provider } = req;
    const { period = '30d', groupBy = 'day' } = req.query;

    // Calculate date range
    const now = new Date();
    let startDate;

    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get booking statistics
    const [totalBookings, confirmedBookings, cancelledBookings, totalRevenue] = await Promise.all([
      prisma.booking.count({
        where: {
          service: { providerId: provider.id },
          createdAt: { gte: startDate }
        }
      }),
      prisma.booking.count({
        where: {
          service: { providerId: provider.id },
          status: 'CONFIRMED',
          createdAt: { gte: startDate }
        }
      }),
      prisma.booking.count({
        where: {
          service: { providerId: provider.id },
          status: 'CANCELLED',
          createdAt: { gte: startDate }
        }
      }),
      prisma.booking.aggregate({
        where: {
          service: { providerId: provider.id },
          status: 'CONFIRMED',
          createdAt: { gte: startDate }
        },
        _sum: {
          totalPrice: true
        }
      })
    ]);

    // Get booking trends (simplified for now)
    const bookingTrends = await prisma.booking.groupBy({
      by: ['status'],
      where: {
        service: { providerId: provider.id },
        createdAt: { gte: startDate }
      },
      _count: {
        id: true
      }
    });

    res.json({
      success: true,
      data: {
        stats: {
          totalBookings,
          confirmedBookings,
          cancelledBookings,
          totalRevenue: totalRevenue._sum.totalPrice || 0,
          conversionRate: totalBookings > 0 ? (confirmedBookings / totalBookings * 100).toFixed(2) : 0
        },
        trends: bookingTrends,
        period,
        dateRange: {
          start: startDate,
          end: now
        }
      }
    });
  } catch (error) {
    console.error('Get analytics error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch analytics' });
  }
});

module.exports = router;
