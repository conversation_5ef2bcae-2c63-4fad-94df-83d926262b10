const express = require('express');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const passport = require('passport');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { PrismaClient } = require('@prisma/client');
const authService = require('../services/authService');
const emailService = require('../services/emailService');
const { authenticateToken, validateSession } = require('../middleware/auth');
const { generateSessionData } = require('../utils/auth');
const dataProtection = require('../utils/dataProtection');

const prisma = new PrismaClient();

const router = express.Router();

// Configure multer for profile image uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/profiles');
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'profile-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    // Check file type
    const allowedTypes = /jpeg|jpg|png|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files (JPEG, PNG, WebP) are allowed'));
    }
  }
});

/**
 * Authentication routes for GoSea platform
 */

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 2 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 5 requests per windowMs
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 password reset requests per hour
  message: {
    success: false,
    message: 'Too many password reset attempts, please try again later.',
    code: 'RATE_LIMIT_EXCEEDED'
  }
});

// Validation rules
const registerValidation = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .customSanitizer(value => value.toLowerCase().trim()),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long'),
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must be less than 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must be less than 50 characters'),
  body('role')
    .optional()
    .isIn(['CUSTOMER', 'BOAT_OWNER', 'AFFILIATE_AGENT'])
    .withMessage('Invalid role specified'),
  body('phone')
    .notEmpty()
    .withMessage('Phone number is required')
    .custom((value) => {
      if (!value || !value.trim()) {
        throw new Error('Phone number is required');
      }

      // Malaysia phone number format validation
      // Supports: +***********, ***********, 0123456789, 123456789
      const malaysiaPhoneRegex = /^(\+?6?01[0-46-9][-\s]?\d{7,8}|(\+?6?0)?(1[0-46-9][-\s]?\d{7,8}))$/;

      if (!malaysiaPhoneRegex.test(value.replace(/[-\s]/g, ''))) {
        throw new Error('Please provide a valid Malaysia phone number (e.g., +***********, 0123456789)');
      }
      return true;
    })
];

// Boat owner registration validation (business fields now optional)
const boatOwnerRegisterValidation = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .customSanitizer(value => value.toLowerCase().trim()),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long'),
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must be less than 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must be less than 50 characters'),
  body('phone')
    .notEmpty()
    .withMessage('Phone number is required')
    .custom((value) => {
      if (!value || !value.trim()) {
        throw new Error('Phone number is required');
      }

      // Malaysia phone number format validation
      const malaysiaPhoneRegex = /^(\+?6?01[0-46-9][-\s]?\d{7,8}|(\+?6?0)?(1[0-46-9][-\s]?\d{7,8}))$/;

      if (!malaysiaPhoneRegex.test(value.replace(/[-\s]/g, ''))) {
        throw new Error('Please provide a valid Malaysia phone number (e.g., +***********, 0123456789)');
      }
      return true;
    }),
  // Business fields are now optional for individual operators
  body('companyName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Company name must be less than 100 characters if provided'),
  body('brn')
    .optional()
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Business Registration Number (BRN) must be less than 20 characters if provided'),
  // Add conditional validation: if companyName is provided, brn should be provided too
  body('companyName')
    .custom((value, { req }) => {
      if (value && value.trim() && (!req.body.brn || !req.body.brn.trim())) {
        throw new Error('Business Registration Number (BRN) is required when company name is provided');
      }
      return true;
    }),
  body('brn')
    .custom((value, { req }) => {
      if (value && value.trim() && (!req.body.companyName || !req.body.companyName.trim())) {
        throw new Error('Company name is required when Business Registration Number (BRN) is provided');
      }
      return true;
    })
];

// Boat owner login validation
const boatOwnerLoginValidation = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .customSanitizer(value => value.toLowerCase().trim()),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

const loginValidation = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .customSanitizer(value => value.toLowerCase().trim()),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

const passwordResetRequestValidation = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .customSanitizer(value => value.toLowerCase().trim())
];

const passwordResetValidation = [
  body('token')
    .notEmpty()
    .withMessage('Reset token is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
];

const refreshTokenValidation = [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required')
];

// Helper function to handle validation errors
function handleValidationErrors(req, res, next) {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array(),
      code: 'VALIDATION_ERROR'
    });
  }
  next();
}

// Helper function to get client info
function getClientInfo(req) {
  return {
    ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent') || 'unknown'
  };
}

/**
 * @swagger
 * /api/auth:
 *   get:
 *     summary: Get authentication endpoints info
 *     description: Returns information about available authentication endpoints
 *     tags: [Authentication]
 *     responses:
 *       200:
 *         description: Authentication endpoints information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "GoSea Authentication API"
 *                 endpoints:
 *                   type: object
 *                   properties:
 *                     register:
 *                       type: string
 *                       example: "POST /api/auth/register"
 *                     login:
 *                       type: string
 *                       example: "POST /api/auth/login"
 *                     logout:
 *                       type: string
 *                       example: "POST /api/auth/logout"
 *                     refresh:
 *                       type: string
 *                       example: "POST /api/auth/refresh"
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'GoSea Authentication API',
    endpoints: {
      register: 'POST /api/auth/register',
      login: 'POST /api/auth/login',
      logout: 'POST /api/auth/logout',
      refresh: 'POST /api/auth/refresh',
      passwordReset: 'POST /api/auth/password-reset/request',
      passwordResetConfirm: 'POST /api/auth/password-reset/confirm',
      verifyEmail: 'POST /api/auth/verify-email',
      me: 'GET /api/auth/me',
      sessions: 'GET /api/auth/sessions'
    }
  });
});

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     description: Create a new user account with email and password
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *               - role
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 minLength: 8
 *                 example: "securePassword123"
 *               firstName:
 *                 type: string
 *                 example: "John"
 *               lastName:
 *                 type: string
 *                 example: "Doe"
 *               phoneNumber:
 *                 type: string
 *                 example: "+***********"
 *               role:
 *                 type: string
 *                 enum: [CUSTOMER, BOAT_OWNER, AFFILIATE_AGENT]
 *                 example: "CUSTOMER"
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Registration successful"
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: Registration failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/register',
  registerValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const result = await authService.register(req.body);
      
      res.status(201).json(result);
    } catch (error) {
      console.error('Registration error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Registration failed',
        code: 'REGISTRATION_ERROR'
      });
    }
  }
);

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login user
 *     description: Authenticate user with email and password
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 example: "securePassword123"
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Login successful"
 *                 accessToken:
 *                   type: string
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                 refreshToken:
 *                   type: string
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       429:
 *         description: Too many login attempts
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/login',
  authLimiter,
  loginValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { email, password } = req.body;
      const { ipAddress, userAgent } = getClientInfo(req);
      
      const result = await authService.login(email, password, ipAddress, userAgent);
      
      // TODO: Set HTTP-only cookie for web clients
      // res.cookie('accessToken', result.accessToken, {
      //   httpOnly: true,
      //   secure: process.env.NODE_ENV === 'production',
      //   sameSite: 'strict',
      //   maxAge: 24 * 60 * 60 * 1000 // 24 hours
      // });

      res.json(result);
    } catch (error) {
      console.error('Login error:', error);
      res.status(401).json({
        success: false,
        message: error.message || 'Login failed',
        code: 'LOGIN_ERROR'
      });
    }
  }
);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post('/logout',
  authenticateToken,
  async (req, res) => {
    try {
      const sessionToken = req.headers['x-session-token'];
      
      await authService.logout(sessionToken);
      
      // TODO: Clear cookie
      // res.clearCookie('accessToken');
      
      res.json({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Logout failed',
        code: 'LOGOUT_ERROR'
      });
    }
  }
);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh',
  refreshTokenValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { refreshToken } = req.body;
      
      const result = await authService.refreshToken(refreshToken);
      
      // TODO: Update HTTP-only cookie
      // res.cookie('accessToken', result.accessToken, {
      //   httpOnly: true,
      //   secure: process.env.NODE_ENV === 'production',
      //   sameSite: 'strict',
      //   maxAge: 24 * 60 * 60 * 1000 // 24 hours
      // });

      res.json(result);
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(401).json({
        success: false,
        message: error.message || 'Token refresh failed',
        code: 'TOKEN_REFRESH_ERROR'
      });
    }
  }
);

/**
 * @route   POST /api/auth/password-reset/request
 * @desc    Request password reset
 * @access  Public
 */
router.post('/password-reset/request',
  passwordResetLimiter,
  passwordResetRequestValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { email } = req.body;
      
      const result = await authService.requestPasswordReset(email);
      
      res.json(result);
    } catch (error) {
      console.error('Password reset request error:', error);
      res.status(500).json({
        success: false,
        message: 'Password reset request failed',
        code: 'PASSWORD_RESET_REQUEST_ERROR'
      });
    }
  }
);

/**
 * @route   POST /api/auth/password-reset/confirm
 * @desc    Reset password using token
 * @access  Public
 */
router.post('/password-reset/confirm',
  passwordResetValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { token, newPassword } = req.body;
      
      const result = await authService.resetPassword(token, newPassword);
      
      res.json(result);
    } catch (error) {
      console.error('Password reset error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Password reset failed',
        code: 'PASSWORD_RESET_ERROR'
      });
    }
  }
);



/**
 * @route   GET /api/auth/me
 * @desc    Get current user info
 * @access  Private
 */
router.get('/me',
  authenticateToken,
  async (req, res) => {
    try {
      // Decrypt profile data before sending response
      let decryptedProfile = null;
      if (req.user.profile) {
        decryptedProfile = dataProtection.decryptProfileData(req.user.profile);
      }

      res.json({
        success: true,
        user: {
          id: req.user.id,
          email: req.user.email,
          role: req.user.role,
          emailVerified: req.user.emailVerified,
          emailVerifiedAt: req.user.emailVerifiedAt,
          lastLoginAt: req.user.lastLoginAt,
          createdAt: req.user.createdAt,
          isApproved: req.user.isApproved,
          profile: decryptedProfile,
          provider: req.user.provider
        }
      });
    } catch (error) {
      console.error('Get user info error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get user information',
        code: 'GET_USER_ERROR'
      });
    }
  }
);

/**
 * @route   PUT /api/auth/profile
 * @desc    Update user profile with optional image upload
 * @access  Private
 */
router.put('/profile',
  authenticateToken,
  upload.single('profileImage'),
  async (req, res) => {
    try {
      const {
        firstName,
        lastName,
        phone,
        dateOfBirth,
        address1,
        address2,
        postcode,
        city,
        state,
        emergencyContact,
        language
      } = req.body;
      const userId = req.user.id;

      // Prepare update data with encryption for sensitive fields
      const updateData = {};

      // Handle basic profile fields
      if (firstName !== undefined) {
        updateData.firstName = firstName ? firstName.trim() : null;
      }
      if (lastName !== undefined) {
        updateData.lastName = lastName ? lastName.trim() : null;
      }

      // Handle profile image upload
      if (req.file) {
        // Generate URL for the uploaded file
        const profileImageUrl = `/uploads/profiles/${req.file.filename}`;
        updateData.profilePicture = profileImageUrl;

        // Delete old profile image if it exists and is not a Google profile picture
        try {
          const currentProfile = await prisma.profile.findUnique({
            where: { userId },
            select: { profilePicture: true }
          });

          if (currentProfile?.profilePicture &&
              !currentProfile.profilePicture.includes('googleusercontent.com') &&
              currentProfile.profilePicture.startsWith('/uploads/')) {
            const oldImagePath = path.join(__dirname, '../../', currentProfile.profilePicture);
            if (fs.existsSync(oldImagePath)) {
              fs.unlinkSync(oldImagePath);
            }
          }
        } catch (deleteError) {
          console.warn('Failed to delete old profile image:', deleteError);
        }
      }

      // Handle date of birth validation separately (not encrypted)
      if (dateOfBirth !== undefined) {
        if (dateOfBirth && typeof dateOfBirth === 'string' && dateOfBirth.trim()) {
          const dateStr = dateOfBirth.trim();
          console.log('Processing dateOfBirth:', dateStr);

          // Parse date string in YYYY-MM-DD format to avoid timezone issues
          let parsedDate;
          if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
            // For YYYY-MM-DD format, parse manually to avoid timezone conversion
            const [year, month, day] = dateStr.split('-').map(num => parseInt(num, 10));
            parsedDate = new Date(year, month - 1, day); // month is 0-indexed
          } else {
            // Fallback for other formats
            parsedDate = new Date(dateStr);
          }

          // Additional validation for common date formats
          const isValidDate = !isNaN(parsedDate.getTime()) &&
                             parsedDate.getFullYear() > 1900 &&
                             parsedDate.getFullYear() < new Date().getFullYear() + 1;

          if (!isValidDate) {
            console.log('Invalid date detected:', dateStr, 'Parsed as:', parsedDate);
            return res.status(400).json({
              success: false,
              message: 'Invalid date format for dateOfBirth. Please use a valid date.',
              error: 'INVALID_DATE_FORMAT'
            });
          }
          updateData.dateOfBirth = parsedDate;
        } else {
          updateData.dateOfBirth = null;
        }
      }

      // Properly handle null/empty values for sensitive fields with encryption
      try {
        if (phone !== undefined) {
          updateData.phone = phone && phone.trim() ? dataProtection.encrypt(phone.trim()) : null;
        }
        if (emergencyContact !== undefined) {
          updateData.emergencyContact = emergencyContact && emergencyContact.trim() ? dataProtection.encrypt(emergencyContact.trim()) : null;
        }

        // Handle new address fields with encryption
        if (address1 !== undefined) {
          updateData.personalAddress1 = address1 && address1.trim() ? dataProtection.encrypt(address1.trim()) : null;
        }
        if (address2 !== undefined) {
          updateData.personalAddress2 = address2 && address2.trim() ? dataProtection.encrypt(address2.trim()) : null;
        }
        if (postcode !== undefined) {
          updateData.personalPostcode = postcode && postcode.trim() ? dataProtection.encrypt(postcode.trim()) : null;
        }
        if (city !== undefined) {
          updateData.personalCity = city && city.trim() ? dataProtection.encrypt(city.trim()) : null;
        }
        if (state !== undefined) {
          updateData.personalState = state && state.trim() ? dataProtection.encrypt(state.trim()) : null;
        }
      } catch (encryptionError) {
        console.error('Encryption error during profile update:', encryptionError);
        return res.status(500).json({
          success: false,
          message: 'Failed to encrypt sensitive data. Please try again.',
          error: 'ENCRYPTION_FAILED'
        });
      }

      // Handle language preference (non-encrypted field)
      if (language !== undefined) {
        updateData.language = language && language.trim() ? language.trim() : 'en';
      }

      // Validate profile existence before update
      const existingProfile = await prisma.profile.findUnique({
        where: { userId }
      });

      if (!existingProfile) {
        throw new Error('Profile not found');
      }

      // Debug: Log the updateData before database update
      console.log('updateData before database update:', JSON.stringify(updateData, null, 2));
      console.log('updateData.dateOfBirth type:', typeof updateData.dateOfBirth);
      console.log('updateData.dateOfBirth value:', updateData.dateOfBirth);

      // Update profile
      const updatedProfile = await prisma.profile.update({
        where: { userId },
        data: updateData
      });

      console.log('Profile updated successfully in database. Updated fields:', Object.keys(updateData));
      console.log('Updated profile dateOfBirth:', updatedProfile.dateOfBirth);

      // Decrypt sensitive data before sending response
      let responseProfile;
      try {
        responseProfile = dataProtection.decryptProfileData(updatedProfile);
      } catch (decryptionError) {
        console.error('Decryption error during profile response:', decryptionError);
        // Return profile without decryption if decryption fails
        responseProfile = {
          ...updatedProfile,
          phone: updatedProfile.phone ? '[Encrypted]' : null,
          emergencyContact: updatedProfile.emergencyContact ? '[Encrypted]' : null,
          personalAddress1: updatedProfile.personalAddress1 ? '[Encrypted]' : null,
          personalAddress2: updatedProfile.personalAddress2 ? '[Encrypted]' : null,
          personalPostcode: updatedProfile.personalPostcode ? '[Encrypted]' : null,
          personalCity: updatedProfile.personalCity ? '[Encrypted]' : null,
          personalState: updatedProfile.personalState ? '[Encrypted]' : null
        };
      }

      res.json({
        success: true,
        message: 'Profile updated successfully',
        profile: responseProfile
      });
    } catch (error) {
      console.error('Profile update error:', error);

      // Clean up uploaded file if there was an error
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (cleanupError) {
          console.warn('Failed to cleanup uploaded file:', cleanupError);
        }
      }

      // Ensure JSON response for all errors
      return res.status(500).json({
        success: false,
        message: 'Failed to update profile. Please try again.',
        error: error.message || 'Unknown error occurred',
        code: 'PROFILE_UPDATE_ERROR'
      });
    }
  }
);

/**
 * @route   GET /api/auth/sessions
 * @desc    Get user sessions
 * @access  Private
 */
router.get('/sessions',
  authenticateToken,
  async (req, res) => {
    try {
      const sessions = await authService.getUserSessions(req.user.id);
      
      res.json({
        success: true,
        sessions
      });
    } catch (error) {
      console.error('Get sessions error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get user sessions',
        code: 'GET_SESSIONS_ERROR'
      });
    }
  }
);

/**
 * @route   DELETE /api/auth/sessions/:sessionToken
 * @desc    Revoke a specific session
 * @access  Private
 */
router.delete('/sessions/:sessionToken',
  authenticateToken,
  async (req, res) => {
    try {
      const { sessionToken } = req.params;
      
      const result = await authService.revokeSession(req.user.id, sessionToken);
      
      res.json(result);
    } catch (error) {
      console.error('Revoke session error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to revoke session',
        code: 'REVOKE_SESSION_ERROR'
      });
    }
  }
);

/**
 * @route   DELETE /api/auth/sessions
 * @desc    Revoke all sessions except current
 * @access  Private
 */
router.delete('/sessions',
  authenticateToken,
  async (req, res) => {
    try {
      const currentSessionToken = req.headers['x-session-token'];
      
      const result = await authService.revokeAllSessions(req.user.id, currentSessionToken);
      
      res.json(result);
    } catch (error) {
      console.error('Revoke all sessions error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to revoke sessions',
        code: 'REVOKE_ALL_SESSIONS_ERROR'
      });
    }
  }
);

/**
 * @route   POST /api/auth/resend-verification
 * @desc    Resend email verification
 * @access  Public
 */
router.post('/resend-verification',
  authLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address')
  ],
  async (req, res) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      const { email } = req.body;

      // Check if user exists and is not already verified
      const user = await prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        include: {
          profile: true,
          emailVerificationTokens: {
            where: { verifiedAt: null },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      });

      if (!user) {
        // Don't reveal if user exists or not for security
        return res.json({
          success: true,
          message: 'If an account with this email exists and is not verified, a verification email has been sent.',
          code: 'VERIFICATION_EMAIL_SENT'
        });
      }

      if (user.emailVerified) {
        return res.json({
          success: true,
          message: 'This email address is already verified.',
          code: 'EMAIL_ALREADY_VERIFIED'
        });
      }

      // Generate new verification token
      const { token: emailToken, expiresAt: emailExpiresAt } = require('../utils/auth').generateEmailVerificationToken();

      // Create new verification token (invalidate old ones)
      await prisma.$transaction(async (tx) => {
        // Mark old tokens as used
        await tx.emailVerificationToken.updateMany({
          where: {
            userId: user.id,
            verifiedAt: null
          },
          data: { verifiedAt: new Date() }
        });

        // Create new token
        await tx.emailVerificationToken.create({
          data: {
            userId: user.id,
            token: emailToken,
            expiresAt: emailExpiresAt
          }
        });
      });

      // Send verification email
      try {
        await emailService.sendVerificationEmail(
          user.email,
          user.profile.firstName,
          emailToken
        );
        console.log(`Verification email resent to ${user.email}`);
      } catch (emailError) {
        console.error('Failed to resend verification email:', emailError);
        return res.status(500).json({
          success: false,
          message: 'Failed to send verification email. Please try again later.',
          code: 'EMAIL_SEND_ERROR'
        });
      }

      res.json({
        success: true,
        message: 'Verification email has been sent. Please check your inbox.',
        code: 'VERIFICATION_EMAIL_SENT'
      });

    } catch (error) {
      console.error('Resend verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to resend verification email',
        code: 'RESEND_VERIFICATION_ERROR'
      });
    }
  }
);

/**
 * @route   POST /api/auth/verify-email
 * @desc    Verify email with token
 * @access  Public
 */
router.post('/verify-email',
  [
    body('token')
      .notEmpty()
      .withMessage('Verification token is required')
      .isLength({ min: 32, max: 128 })
      .withMessage('Invalid token format')
  ],
  async (req, res) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Invalid request data',
          errors: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      const { token } = req.body;

      // Find the verification token
      const verificationToken = await prisma.emailVerificationToken.findUnique({
        where: { token },
        include: {
          user: {
            include: { profile: true }
          }
        }
      });

      if (!verificationToken) {
        return res.status(400).json({
          success: false,
          message: 'Invalid verification token.',
          code: 'TOKEN_NOT_FOUND'
        });
      }

      // Check if token is already used
      if (verificationToken.verifiedAt) {
        return res.status(400).json({
          success: false,
          message: 'This verification token has already been used.',
          code: 'TOKEN_ALREADY_USED'
        });
      }

      // Check if token is expired
      if (new Date() > verificationToken.expiresAt) {
        return res.status(400).json({
          success: false,
          message: 'Verification token has expired. Please request a new one.',
          code: 'TOKEN_EXPIRED',
          email: verificationToken.user.email
        });
      }

      // Check if user is already verified
      if (verificationToken.user.emailVerified) {
        return res.json({
          success: true,
          message: 'Your email is already verified.',
          code: 'EMAIL_ALREADY_VERIFIED',
          user: {
            email: verificationToken.user.email,
            firstName: verificationToken.user.profile?.firstName
          }
        });
      }

      // Verify the email
      await prisma.$transaction(async (tx) => {
        // Update user as verified
        await tx.user.update({
          where: { id: verificationToken.userId },
          data: {
            emailVerified: true,
            emailVerifiedAt: new Date()
          }
        });

        // Mark token as used
        await tx.emailVerificationToken.update({
          where: { id: verificationToken.id },
          data: { verifiedAt: new Date() }
        });
      });

      // Send welcome email
      try {
        await emailService.sendWelcomeEmail(
          verificationToken.user.email,
          verificationToken.user.profile?.firstName || 'User'
        );
        console.log(`Welcome email sent to ${verificationToken.user.email}`);
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
        // Don't fail verification if welcome email fails
      }

      res.json({
        success: true,
        message: 'Email verified successfully! Welcome to GoSea Platform.',
        code: 'EMAIL_VERIFIED',
        user: {
          email: verificationToken.user.email,
          firstName: verificationToken.user.profile?.firstName
        }
      });
    } catch (error) {
      console.error('Email verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

/**
 * @route   GET /auth/google
 * @desc    Initiate Google OAuth authentication
 * @access  Public
 */
router.get('/google',
  (req, res, next) => {
    // Store role in session if provided
    if (req.query.role && ['CUSTOMER', 'BOAT_OWNER', 'AFFILIATE_AGENT'].includes(req.query.role)) {
      req.session.pendingRole = req.query.role;
    }

    // Store state parameter (return URL) in session if provided
    if (req.query.state) {
      try {
        const stateData = JSON.parse(decodeURIComponent(req.query.state));
        if (stateData.returnUrl) {
          req.session.returnUrl = stateData.returnUrl;
          console.log('Stored return URL in session:', stateData.returnUrl);
        }
      } catch (error) {
        console.error('Failed to parse state parameter:', error);
      }
    }

    next();
  },
  passport.authenticate('google', {
    scope: ['profile', 'email'],
    prompt: 'select_account', // Force account selection
    state: req => req.query.state // Pass state to Google OAuth
  })
);

/**
 * @route   GET /auth/google/callback
 * @desc    Google OAuth callback
 * @access  Public
 */
router.get('/google/callback',
  (req, res, next) => {
    passport.authenticate('google', {
      session: false
    }, (err, user, info) => {
      if (err) {
        // Check if this is a registration required error
        if (err.message === 'USER_NOT_REGISTERED' && err.googleProfile) {
          console.log('Google user not registered, redirecting to registration with profile data');

          // Encode the Google profile data for the frontend
          const encodedProfile = encodeURIComponent(JSON.stringify(err.googleProfile));
          const redirectUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/register?google_profile=${encodedProfile}&error=registration_required`;

          return res.redirect(redirectUrl);
        }

        // Other OAuth errors
        console.error('Google OAuth error:', err);
        return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/?error=oauth_failed`);
      }

      if (!user) {
        return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/?error=oauth_failed`);
      }

      // Continue with successful authentication
      req.user = user;
      next();
    })(req, res, next);
  },
  async (req, res) => {
    try {
      const user = req.user;

      // Update role if it was specified during OAuth initiation
      // BUT ONLY for first-time users - don't overwrite existing user roles
      if (req.session.pendingRole && req.session.pendingRole !== user.role && user.isFirstTimeGoogleUser) {
        await prisma.user.update({
          where: { id: user.id },
          data: { role: req.session.pendingRole }
        });
        user.role = req.session.pendingRole;
        console.log(`Set role ${req.session.pendingRole} for first-time Google user:`, user.email);
      } else if (req.session.pendingRole && !user.isFirstTimeGoogleUser) {
        console.log(`Skipping role update for existing user ${user.email}. Current role: ${user.role}, Pending role: ${req.session.pendingRole}`);
      }

      // Clear pending role from session
      delete req.session.pendingRole;

      // Decrypt profile data before generating session
      const decryptedUser = { ...user };
      if (decryptedUser.profile) {
        decryptedUser.profile = dataProtection.decryptProfileData(decryptedUser.profile);
      }

      // Generate JWT tokens
      const sessionData = generateSessionData(decryptedUser);

      console.log('Google OAuth successful for user:', user.email);

      // Check if this is a first-time Google user
      const isFirstTimeGoogleUser = user.isFirstTimeGoogleUser || false;

      // Determine redirect URL based on user status and stored return URL
      let callbackPath = '/auth/google/callback';

      // Check if we have a stored return URL from the OAuth initiation
      const storedReturnUrl = req.session.returnUrl;
      if (storedReturnUrl && storedReturnUrl !== '/') {
        console.log('Using stored return URL from session:', storedReturnUrl);
        callbackPath = '/auth/google/callback';
      } else if (isFirstTimeGoogleUser) {
        callbackPath = '/auth/google/callback?first_time=true';
        console.log('First-time Google user, will redirect to profile completion');
      }

      // Redirect to frontend with tokens
      const redirectUrl = new URL(`${process.env.FRONTEND_URL || 'http://localhost:3000'}${callbackPath}`);
      redirectUrl.searchParams.set('token', sessionData.accessToken);
      redirectUrl.searchParams.set('refreshToken', sessionData.refreshToken);
      redirectUrl.searchParams.set('sessionToken', sessionData.sessionToken);
      redirectUrl.searchParams.set('user', JSON.stringify({
        id: decryptedUser.id,
        email: decryptedUser.email,
        role: decryptedUser.role,
        emailVerified: decryptedUser.emailVerified,
        profile: decryptedUser.profile,
        isFirstTimeGoogleUser: isFirstTimeGoogleUser
      }));

      // Add the stored return URL as a query parameter if available
      if (storedReturnUrl && storedReturnUrl !== '/') {
        redirectUrl.searchParams.set('returnUrl', storedReturnUrl);
        console.log('Added return URL to callback:', storedReturnUrl);

        // Clear the return URL from session after using it
        delete req.session.returnUrl;
      }

      res.redirect(redirectUrl.toString());
    } catch (error) {
      console.error('Google OAuth callback error:', error);
      res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/?error=oauth_callback_failed`);
    }
  }
);

/**
 * @route   POST /auth/google/link
 * @desc    Link Google account to existing user
 * @access  Private
 */
router.post('/google/link',
  authenticateToken,
  async (req, res) => {
    try {
      const { googleId } = req.body;
      const userId = req.user.id;

      if (!googleId) {
        return res.status(400).json({
          success: false,
          message: 'Google ID is required',
          code: 'MISSING_GOOGLE_ID'
        });
      }

      // Check if Google ID is already linked to another account
      const existingUser = await prisma.user.findFirst({
        where: {
          googleId,
          id: { not: userId }
        }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'This Google account is already linked to another user',
          code: 'GOOGLE_ACCOUNT_ALREADY_LINKED'
        });
      }

      // Link Google account
      await prisma.user.update({
        where: { id: userId },
        data: { googleId }
      });

      res.json({
        success: true,
        message: 'Google account linked successfully'
      });
    } catch (error) {
      console.error('Google account linking error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// ======================
// BOAT OWNER AUTHENTICATION ENDPOINTS
// ======================

/**
 * @swagger
 * /api/auth/boat-owner/register:
 *   post:
 *     summary: Register a new boat owner
 *     description: Register a new boat owner with optional business details and manual email verification. Individual operators can register without company information.
 *     tags: [Boat Owner Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *               - phone
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User email address
 *               password:
 *                 type: string
 *                 minLength: 8
 *                 description: User password (minimum 8 characters)
 *               firstName:
 *                 type: string
 *                 description: User first name
 *               lastName:
 *                 type: string
 *                 description: User last name
 *               phone:
 *                 type: string
 *                 description: Malaysia phone number (e.g., +***********)
 *               companyName:
 *                 type: string
 *                 description: Company name (optional, for business operators)
 *               brn:
 *                 type: string
 *                 description: Business Registration Number (optional, required if companyName provided)
 *     responses:
 *       201:
 *         description: Registration successful
 *       400:
 *         description: Validation error or user already exists
 */
router.post('/boat-owner/register', authLimiter, boatOwnerRegisterValidation, handleValidationErrors, async (req, res) => {
  try {
    const { email, password, firstName, lastName, phone, companyName, brn } = req.body;
    const clientInfo = getClientInfo(req);

    console.log(`Boat owner registration attempt: ${email}`);

    const result = await authService.registerBoatOwner({
      email,
      password,
      firstName,
      lastName,
      phone,
      companyName,
      brn
    });

    console.log(`Boat owner registration successful: ${email}`);

    res.status(201).json({
      success: true,
      message: result.message,
      user: result.user,
      emailVerificationToken: result.emailVerificationToken
    });
  } catch (error) {
    console.error('Boat owner registration error:', error);
    
    // Check for specific error types
    if (error.message.includes('already exists') || error.message.includes('Gmail variant')) {
      return res.status(400).json({
        success: false,
        message: error.message,
        code: 'USER_ALREADY_EXISTS'
      });
    }

    if (error.message.includes('Password validation failed')) {
      return res.status(400).json({
        success: false,
        message: error.message,
        code: 'WEAK_PASSWORD'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Registration failed. Please try again.',
      code: 'REGISTRATION_ERROR'
    });
  }
});

/**
 * @swagger
 * /api/auth/boat-owner/login:
 *   post:
 *     summary: Login boat owner
 *     description: Authenticate boat owner with email and password, includes approval status checking
 *     tags: [Boat Owner Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       400:
 *         description: Invalid credentials or account not approved
 *       423:
 *         description: Account locked
 */
router.post('/boat-owner/login', authLimiter, boatOwnerLoginValidation, handleValidationErrors, async (req, res) => {
  try {
    const { email, password } = req.body;
    const clientInfo = getClientInfo(req);

    console.log(`Boat owner login attempt: ${email}`);

    const result = await authService.loginBoatOwner(
      email,
      password,
      clientInfo.ipAddress,
      clientInfo.userAgent
    );

    console.log(`Boat owner login successful: ${email}`);

    res.json({
      success: true,
      message: result.message,
      accessToken: result.accessToken,
      refreshToken: result.refreshToken,
      sessionToken: result.sessionToken,
      user: result.user,
      expiresAt: result.expiresAt
    });
  } catch (error) {
    console.error('Boat owner login error:', error);

    // Handle specific error types
    if (error.message.includes('locked')) {
      return res.status(423).json({
        success: false,
        message: error.message,
        code: 'ACCOUNT_LOCKED'
      });
    }

    if (error.message.includes('deactivated')) {
      return res.status(400).json({
        success: false,
        message: error.message,
        code: 'ACCOUNT_DEACTIVATED'
      });
    }

    if (error.message.includes('verify your email')) {
      return res.status(400).json({
        success: false,
        message: error.message,
        code: 'EMAIL_NOT_VERIFIED'
      });
    }

    if (error.message.includes('pending admin approval')) {
      return res.status(400).json({
        success: false,
        message: error.message,
        code: 'PENDING_ADMIN_APPROVAL'
      });
    }

    if (error.message.includes('Invalid email or password')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email or password',
        code: 'INVALID_CREDENTIALS'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Login failed. Please try again.',
      code: 'LOGIN_ERROR'
    });
  }
});

/**
 * @swagger
 * /api/auth/boat-owner/verify-email:
 *   post:
 *     summary: Verify boat owner email
 *     description: Verify boat owner email address and trigger admin notification
 *     tags: [Boat Owner Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *     responses:
 *       200:
 *         description: Email verified successfully
 *       400:
 *         description: Invalid or expired token
 */
router.post('/boat-owner/verify-email', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Verification token is required',
        code: 'MISSING_TOKEN'
      });
    }

    console.log(`Boat owner email verification attempt with token: ${token}`);

    const result = await authService.verifyBoatOwnerEmail(token);

    console.log('Boat owner email verification successful');

    res.json({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('Boat owner email verification error:', error);

    if (error.message.includes('Invalid or expired')) {
      return res.status(400).json({
        success: false,
        message: error.message,
        code: 'INVALID_TOKEN'
      });
    }

    if (error.message.includes('already been verified')) {
      return res.status(400).json({
        success: false,
        message: error.message,
        code: 'ALREADY_VERIFIED'
      });
    }

    if (error.message.includes('Invalid verification method')) {
      return res.status(400).json({
        success: false,
        message: error.message,
        code: 'INVALID_USER_TYPE'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Email verification failed. Please try again.',
      code: 'VERIFICATION_ERROR'
    });
  }
});

module.exports = router;
