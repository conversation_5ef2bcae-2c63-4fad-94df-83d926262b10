const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const JwtStrategy = require('passport-jwt').Strategy;
const ExtractJwt = require('passport-jwt').ExtractJwt;
const { PrismaClient } = require('@prisma/client');
const { sanitizeUserForToken } = require('../utils/auth');
const dataProtection = require('../utils/dataProtection');

// Ensure environment variables are loaded
require('dotenv').config({ path: require('path').join(__dirname, '../../../.env') });

const prisma = new PrismaClient();

/**
 * Passport configuration for GoSea platform
 * Supports Google OAuth and JWT authentication
 */

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        profile: true
      }
    });

    // Decrypt profile data if user exists
    if (user && user.profile) {
      user.profile = dataProtection.decryptProfileData(user.profile);
    }

    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Google OAuth Strategy
if (process.env.ENABLE_GOOGLE_OAUTH === 'true') {
  // Validate that Google OAuth credentials are configured
  if (!process.env.GOOGLE_CLIENT_ID || process.env.GOOGLE_CLIENT_ID === 'your-google-client-id') {
    console.error('ERROR: GOOGLE_CLIENT_ID is not configured. Please set up Google OAuth credentials in .env file.');
    console.error('Visit https://console.cloud.google.com/apis/credentials to create OAuth 2.0 credentials.');
  }
  if (!process.env.GOOGLE_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET === 'your-google-client-secret') {
    console.error('ERROR: GOOGLE_CLIENT_SECRET is not configured. Please set up Google OAuth credentials in .env file.');
  }

  passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: process.env.GOOGLE_REDIRECT_URI || '/api/auth/google/callback'
  },
  async (accessToken, refreshToken, profile, done) => {
    try {
      console.log('Google OAuth callback received:', {
        id: profile.id,
        email: profile.emails?.[0]?.value,
        name: profile.displayName
      });

      const email = profile.emails?.[0]?.value;
      if (!email) {
        return done(new Error('No email found in Google profile'), null);
      }

      // Normalize email for consistent checking
      const normalizedEmail = email.toLowerCase().trim();

      // Enhanced duplicate check - check both by email and by normalized email
      let user = await prisma.user.findFirst({
        where: {
          OR: [
            { email: normalizedEmail },
            { email: { equals: normalizedEmail, mode: 'insensitive' } }
          ]
        },
        include: {
          profile: true
        }
      });

      // Additional check for Gmail variations if not found (Gmail ignores dots and + aliases)
      if (!user && normalizedEmail.includes('@gmail.com')) {
        const localPart = normalizedEmail.split('@')[0];
        const baseLocalPart = localPart.replace(/\./g, '').split('+')[0];
        
        // Find all Gmail users and check if any match this pattern
        const gmailUsers = await prisma.user.findMany({
          where: {
            email: {
              endsWith: '@gmail.com',
              mode: 'insensitive'
            }
          },
          include: { profile: true }
        });
        
        // Check if any existing Gmail users match this base pattern
        for (const gmailUser of gmailUsers) {
          const existingLocalPart = gmailUser.email.split('@')[0];
          const existingBaseLocalPart = existingLocalPart.replace(/\./g, '').split('+')[0];
          
          if (existingBaseLocalPart === baseLocalPart && gmailUser.email.toLowerCase() !== normalizedEmail) {
            console.log(`Google OAuth attempted with Gmail variant. Trying: ${normalizedEmail}, Existing: ${gmailUser.email}`);
            user = gmailUser;
            break;
          }
        }
      }

      if (user) {
        // User exists - update Google ID if not set
        if (!user.googleId) {
          user = await prisma.user.update({
            where: { id: user.id },
            data: { googleId: profile.id },
            include: { profile: true }
          });
        }

        // Update last login
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLoginAt: new Date() }
        });

        console.log('Existing user logged in via Google:', user.email);
        return done(null, { ...user, isFirstTimeGoogleUser: false });
      }

      // Create new user with Google OAuth
      const names = profile.displayName?.split(' ') || ['', ''];
      const firstName = names[0] || profile.name?.givenName || '';
      const lastName = names.slice(1).join(' ') || profile.name?.familyName || '';

      const result = await prisma.$transaction(async (tx) => {
        // Create user
        const newUser = await tx.user.create({
          data: {
            email: normalizedEmail,
            googleId: profile.id,
            role: 'CUSTOMER', // Default role, can be changed later
            emailVerified: true, // Google emails are pre-verified
            emailVerifiedAt: new Date(),
            isActive: true,
            lastLoginAt: new Date()
          }
        });

        // Create profile
        const newProfile = await tx.profile.create({
          data: {
            userId: newUser.id,
            firstName,
            lastName,
            profilePicture: profile.photos?.[0]?.value || null
          }
        });

        return { user: newUser, profile: newProfile };
      });

      console.log('New user created via Google OAuth:', result.user.email);

      // Send welcome email
      try {
        const emailService = require('../services/emailService');
        await emailService.sendWelcomeEmail(
          result.user.email,
          result.profile.firstName || 'User'
        );
        console.log(`Welcome email sent to ${result.user.email}`);
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
        // Don't fail OAuth if welcome email fails
      }

      // Mark this as a first-time Google user for profile completion
      return done(null, {
        ...result.user,
        profile: result.profile,
        isFirstTimeGoogleUser: true
      });
    } catch (error) {
      console.error('Google OAuth error:', error);
      return done(error, null);
    }
  }));
}

// JWT Strategy for API authentication
passport.use(new JwtStrategy({
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  secretOrKey: process.env.JWT_SECRET
}, async (payload, done) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        profile: true
      }
    });

    if (user && user.isActive) {
      // Decrypt profile data before sanitizing
      if (user.profile) {
        user.profile = dataProtection.decryptProfileData(user.profile);
      }
      return done(null, sanitizeUserForToken(user));
    }
    return done(null, false);
  } catch (error) {
    return done(error, false);
  }
}));

module.exports = passport;
