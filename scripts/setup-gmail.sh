#!/bin/bash

# Gmail Setup Script for GoSea Platform
# This script helps configure Gmail for sending verification emails

echo "🌊 GoSea Platform - Gmail Setup"
echo "================================"
echo ""

# Check if .env files exist
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    exit 1
fi

if [ ! -f ".env.local" ]; then
    echo "❌ Error: .env.local file not found!"
    exit 1
fi

echo "📧 Current Gmail Configuration:"
echo "Gmail Account: <EMAIL>"
echo "From Address: <EMAIL>"
echo "Real Email Mode: Enabled"
echo ""

# Check if app password is configured
if grep -q "GMAIL_APP_PASSWORD=your-gmail-app-password" .env; then
    echo "⚠️  Gmail App Password is not configured yet!"
    echo ""
    echo "📋 To configure Gmail App Password:"
    echo "1. Go to: https://myaccount.google.com/"
    echo "2. Click 'Security' → 'App passwords'"
    echo "3. Generate app password for 'GoSea Platform'"
    echo "4. Copy the 16-character password"
    echo "5. Run this script again with the password"
    echo ""
    
    read -p "Enter Gmail App Password (or press Enter to skip): " app_password
    
    if [ ! -z "$app_password" ]; then
        # Update .env file
        sed -i.bak "s/GMAIL_APP_PASSWORD=your-gmail-app-password/GMAIL_APP_PASSWORD=$app_password/" .env
        
        # Update .env.local file
        sed -i.bak "s/GMAIL_APP_PASSWORD=your-gmail-app-password/GMAIL_APP_PASSWORD=$app_password/" .env.local
        
        echo "✅ Gmail App Password updated in both .env files"
        echo ""
        
        # Restart backend
        echo "🔄 Restarting backend to apply changes..."
        docker compose restart backend
        
        echo ""
        echo "✅ Setup complete! Gmail is now configured."
        echo ""
        echo "🧪 To test:"
        echo "1. Go to http://localhost:3000"
        echo "2. Create a new boat owner account"
        echo "3. Check <EMAIL> inbox for verification email"
        echo ""
    else
        echo "⏭️  Skipped app password configuration"
        echo "   Run this script again when you have the app password"
    fi
else
    echo "✅ Gmail App Password is already configured"
    echo ""
    
    # Check if backend is running
    if docker compose ps | grep -q "gosea-backend.*Up"; then
        echo "✅ Backend is running"
        
        # Check email configuration in logs
        echo ""
        echo "📋 Checking email configuration..."
        docker compose logs backend --tail=20 | grep -i email || echo "No recent email logs found"
        
    else
        echo "⚠️  Backend is not running"
        echo "🔄 Starting backend..."
        docker compose up -d backend
    fi
    
    echo ""
    echo "🧪 To test email functionality:"
    echo "1. Go to http://localhost:3000"
    echo "2. Create a new boat owner account"
    echo "3. Check <EMAIL> inbox for verification email"
    echo ""
    echo "📊 Monitor backend logs:"
    echo "docker compose logs backend -f | grep -i email"
fi

echo ""
echo "📚 For detailed setup instructions, see: docs/GMAIL_SETUP_GUIDE.md"
