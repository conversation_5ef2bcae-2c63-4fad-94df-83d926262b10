import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Clock } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

/**
 * Custom Time Picker Component
 * Based on CustomDateTimePicker but simplified for time-only selection
 * Selection-only (no manual text input) for better UX
 */

const CustomTimePicker = ({
  value,
  onChange,
  name = 'time',
  placeholder = 'Select time',
  className = '',
  error = false,
  disabled = false,
  format12Hour = false // Option to use 12-hour format with AM/PM
}) => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTime, setSelectedTime] = useState({ hour: '', minute: '', ampm: 'AM' });
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const [isMobile, setIsMobile] = useState(false);
  const dropdownRef = useRef(null);
  const portalRef = useRef(null);
  const inputRef = useRef(null);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Calculate dropdown position
  const calculateDropdownPosition = () => {
    if (inputRef.current && !isMobile) {
      const rect = inputRef.current.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
      const viewportHeight = window.innerHeight;
      const dropdownHeight = 300; // Estimated dropdown height

      // Check if dropdown fits below the input
      const spaceBelow = viewportHeight - rect.bottom;
      const spaceAbove = rect.top;

      let top, positioning;

      if (spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove) {
        // Position below input
        top = rect.bottom + scrollTop + 4;
        positioning = 'below';
      } else {
        // Position above input
        top = rect.top + scrollTop - dropdownHeight - 4;
        positioning = 'above';
      }

      setDropdownPosition({
        top,
        left: rect.left + scrollLeft,
        width: Math.max(rect.width, 280), // Minimum width for better UX
        positioning
      });
    }
  };

  // Close dropdown when clicking outside and handle positioning
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (portalRef.current && !portalRef.current.contains(event.target) &&
          inputRef.current && !inputRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    const handleResize = () => {
      if (isOpen) {
        calculateDropdownPosition();
      }
    };

    const handleScroll = () => {
      if (isOpen && !isMobile) {
        // Recalculate position on scroll for desktop
        calculateDropdownPosition();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll);
      calculateDropdownPosition();
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isOpen]);

  // Parse time value when prop changes
  useEffect(() => {
    if (value) {
      const timeMatch = value.match(/^(\d{1,2}):(\d{2})(?:\s?(AM|PM))?$/i);
      if (timeMatch) {
        let [, hour, minute, ampm] = timeMatch;
        
        if (format12Hour) {
          setSelectedTime({ 
            hour: hour.padStart(2, '0'), 
            minute: minute, 
            ampm: ampm || 'AM' 
          });
        } else {
          // Convert 24-hour format
          setSelectedTime({ 
            hour: hour.padStart(2, '0'), 
            minute: minute, 
            ampm: 'AM' 
          });
        }
      }
    } else {
      setSelectedTime({ hour: '', minute: '', ampm: 'AM' });
    }
  }, [value, format12Hour]);

  // Generate hour options
  const generateHourOptions = () => {
    const hours = [];
    if (format12Hour) {
      for (let hour = 1; hour <= 12; hour++) {
        hours.push(String(hour).padStart(2, '0'));
      }
    } else {
      for (let hour = 0; hour < 24; hour++) {
        hours.push(String(hour).padStart(2, '0'));
      }
    }
    return hours;
  };

  // Generate minute options (00, 15, 30, 45)
  const generateMinuteOptions = () => {
    return ['00', '15', '30', '45'];
  };

  // Format time for display
  const formatTime = (hour, minute, ampm) => {
    if (!hour || !minute) return '';
    if (format12Hour && ampm) {
      return `${hour}:${minute} ${ampm}`;
    }
    return `${hour}:${minute}`;
  };

  // Convert 12-hour to 24-hour format for form submission
  const convertTo24Hour = (hour, minute, ampm) => {
    if (!format12Hour) return `${hour}:${minute}`;
    
    let hour24 = parseInt(hour);
    if (ampm === 'PM' && hour24 !== 12) {
      hour24 += 12;
    } else if (ampm === 'AM' && hour24 === 12) {
      hour24 = 0;
    }
    
    return `${String(hour24).padStart(2, '0')}:${minute}`;
  };

  // Handle time selection
  const handleTimeSelect = (type, value) => {
    const newTime = { ...selectedTime, [type]: value };
    setSelectedTime(newTime);
    updateValue(newTime);
  };

  // Update the form value
  const updateValue = (time) => {
    if (!time.hour || !time.minute) return;
    
    let formattedValue;
    if (format12Hour) {
      formattedValue = formatTime(time.hour, time.minute, time.ampm);
    } else {
      formattedValue = convertTo24Hour(time.hour, time.minute, time.ampm);
    }
    
    onChange({ target: { name, value: formattedValue } });
  };

  // Complete selection and close dropdown
  const handleComplete = () => {
    if (selectedTime.hour && selectedTime.minute) {
      setIsOpen(false);
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Input Field */}
      <div
        ref={inputRef}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={`w-full px-3 py-2 border border-gray-300 rounded-lg cursor-pointer transition-colors ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white hover:border-gray-400'
        } ${error ? 'border-red-500' : 'border-gray-300'} ${
          isOpen ? 'focus:ring-2 focus:ring-amber-500 focus:border-amber-500' : ''
        }`}
        style={{
          outline: 'none'
        }}
      >
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center space-x-2 flex-1">
            <Clock className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <span
              className={`flex-1 text-left truncate ${(selectedTime.hour && selectedTime.minute) ? 'font-medium' : ''}`}
              style={{ color: (selectedTime.hour && selectedTime.minute) ? '#374151' : '#6b7280' }}
            >
              {formatTime(selectedTime.hour, selectedTime.minute, selectedTime.ampm) || placeholder}
            </span>
          </div>
          <svg
            className={`text-gray-400 w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''} flex-shrink-0`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Dropdown Portal */}
      {isOpen && typeof window !== 'undefined' && typeof document !== 'undefined' && document.body && createPortal(
        <div
          ref={portalRef}
          className={isMobile
            ? "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
            : "fixed bg-white border border-gray-300 rounded-md shadow-lg"
          }
          style={isMobile
            ? { zIndex: 9998 }
            : {
                top: dropdownPosition.top,
                left: dropdownPosition.left,
                width: Math.max(dropdownPosition.width, 280),
                maxWidth: '320px',
                borderColor: '#d1d5db',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                zIndex: 9999,
                overflow: 'hidden'
              }
          }
          onClick={isMobile ? () => setIsOpen(false) : undefined}
        >
          <div
            className={isMobile
              ? "bg-white rounded-lg shadow-xl w-full max-w-sm max-h-[70vh] overflow-hidden"
              : ""
            }
            onClick={isMobile ? (e) => e.stopPropagation() : undefined}
            style={isMobile ? {
              borderColor: '#d1d5db',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            } : {}}
          >
            {/* Mobile Header */}
            {isMobile && (
              <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: '#e5e7eb' }}>
                <h3 className="text-lg font-semibold" style={{ color: '#111827' }}>
                  {t('selectTime')}
                </h3>
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="p-1 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Content Container */}
            <div className={isMobile ? "overflow-y-auto pt-6 px-4 pb-4" : "pt-6 px-4 pb-4"} style={isMobile ? { maxHeight: 'calc(70vh - 80px)' } : {}}>
              {/* Time Selection */}
              <div className="space-y-4">
                <div className={`grid gap-3 ${format12Hour ? 'grid-cols-3' : 'grid-cols-2'}`}>
                  {/* Hour Selection */}
                  <div>
                    <label className="block text-xs font-medium mb-1" style={{ color: '#374151' }}>
                      {t('hour')}
                    </label>
                    <div className="max-h-32 overflow-y-auto border rounded-md" style={{ borderColor: '#d1d5db' }}>
                      {generateHourOptions().map(hour => (
                        <button
                          key={hour}
                          type="button"
                          onClick={() => handleTimeSelect('hour', hour)}
                          className={`w-full px-2 py-1.5 text-left hover:bg-gray-100 transition-colors text-sm ${
                            selectedTime.hour === hour ? 'font-bold' : ''
                          }`}
                          style={{
                            backgroundColor: selectedTime.hour === hour ? '#fef3c7' : 'transparent',
                            color: selectedTime.hour === hour ? '#f59e0b' : '#374151'
                          }}
                        >
                          {hour}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Minute Selection */}
                  <div>
                    <label className="block text-xs font-medium mb-1" style={{ color: '#374151' }}>
                      {t('minute')}
                    </label>
                    <div className="max-h-32 overflow-y-auto border rounded-md" style={{ borderColor: '#d1d5db' }}>
                      {generateMinuteOptions().map(minute => (
                        <button
                          key={minute}
                          type="button"
                          onClick={() => handleTimeSelect('minute', minute)}
                          className={`w-full px-2 py-1.5 text-left hover:bg-gray-100 transition-colors text-sm ${
                            selectedTime.minute === minute ? 'font-bold' : ''
                          }`}
                          style={{
                            backgroundColor: selectedTime.minute === minute ? '#fef3c7' : 'transparent',
                            color: selectedTime.minute === minute ? '#f59e0b' : '#374151'
                          }}
                        >
                          {minute}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* AM/PM Selection (12-hour format only) */}
                  {format12Hour && (
                    <div>
                      <label className="block text-xs font-medium mb-1" style={{ color: '#374151' }}>
                        {t('period')}
                      </label>
                      <div className="space-y-1">
                        {['AM', 'PM'].map(period => (
                          <button
                            key={period}
                            type="button"
                            onClick={() => handleTimeSelect('ampm', period)}
                            className={`w-full px-2 py-1.5 text-left hover:bg-gray-100 transition-colors text-sm border rounded ${
                              selectedTime.ampm === period ? 'font-bold' : ''
                            }`}
                            style={{
                              backgroundColor: selectedTime.ampm === period ? '#fef3c7' : 'transparent',
                              borderColor: selectedTime.ampm === period ? '#f59e0b' : '#d1d5db',
                              color: selectedTime.ampm === period ? '#f59e0b' : '#374151'
                            }}
                          >
                            {period}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Selected Time Display */}
                {selectedTime.hour && selectedTime.minute && (
                  <div className="text-center p-3 rounded-md" style={{ backgroundColor: '#fef3c7' }}>
                    <p className="text-sm font-medium" style={{ color: '#f59e0b' }}>
                      {t('selectedTime')}: {formatTime(selectedTime.hour, selectedTime.minute, selectedTime.ampm)}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="p-3 border-t flex justify-end space-x-2" style={{ borderColor: '#e5e7eb' }}>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 text-sm rounded transition-colors"
                style={{ backgroundColor: '#f3f4f6', color: '#6b7280' }}
              >
                {t('cancel')}
              </button>
              {selectedTime.hour && selectedTime.minute && (
                <button
                  type="button"
                  onClick={handleComplete}
                  className="px-4 py-2 text-sm rounded transition-colors"
                  style={{ backgroundColor: '#f59e0b', color: '#ffffff' }}
                >
                  {t('done')}
                </button>
              )}
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default CustomTimePicker;