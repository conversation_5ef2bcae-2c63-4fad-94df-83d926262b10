import { createContext, useContext, useState, useEffect } from 'react';
import JettySelector from '../components/search/JettySelector';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Translation data
const translations = {
  en: {
    // Navigation
    boats: 'Boats',
    features: 'Features',
    howItWorks: 'How It Works',
    services: 'Services',
    about: 'About',
    contact: 'Contact',
    signIn: 'Sign In',
    signUp: 'Sign Up',
    success: 'Success',
    getStarted: 'Get Started',
    dashboard: 'Dashboard',
    boatOwner: 'Boat Owner',
    agent: 'Agent',
    businessOptions: 'Business Options',
    
    // Hero Section
    heroTitle: 'Your Gateway to',
    heroSubtitle: 'Malaysian Sea Adventures',
    heroDescription: 'Discover breathtaking snorkeling spots, book island hopping tours, and enjoy seamless passenger transportation across Malaysia\'s most beautiful islands. Your perfect boat adventure starts here.',
    bookAdventure: '🏊‍♂️ Book Your Adventure',
    seeHowItWorks: '🎥 See How It Works',
    
    // Trust Indicators
    verifiedOperators: 'Verified Boat Operators',
    secureBooking: 'Secure Online Booking',
    customerSupport: '24/7 Customer Support',
    
    // Features Section
    perfectForEveryone: 'Perfect for Everyone',
    featuresDescription: 'Whether you\'re seeking adventure, running a boat business, or looking to earn through partnerships, GoSea has the perfect solution for you.',

    // Top Providers Section
    topBoatProviders: 'Top Boat Providers',
    trustedProvidersDescription: 'Discover our most trusted and experienced boat operators offering exceptional marine adventures.',
    viewServices: 'View Services',
    
    // Customer Section
    adventureSeekers: 'For Adventure Seekers',
    customerDescription: 'Discover amazing snorkeling spots, book island hopping tours, and enjoy safe passenger transportation across Malaysia\'s stunning islands.',
    browseOperators: 'Browse verified boat operators',
    realTimeAvailability: 'Real-time availability & pricing',
    securePayment: 'Secure online booking & payment',
    support247: '24/7 customer support',
    startAdventure: 'Find a Boat',

    // Pricing
    pricingPackages: 'Pricing Packages',
    pricing: 'Pricing',
    includes: 'Includes',
    
    // Boat Owner Section
    boatOwners: 'For Boat Owners',
    ownerDescription: 'List your boats, manage bookings, and grow your marine business with our comprehensive platform designed for boat operators.',
    easyListing: 'Easy boat listing & management',
    automatedBooking: 'Automated booking system',
    revenueTracking: 'Revenue tracking & analytics',
    marketingTools: 'Marketing & promotion tools',
    listYourBoat: 'List Your Boat',
    
    // Affiliate Section
    affiliatePartners: 'For Affiliate Partners',
    affiliateDescription: 'Earn commissions by promoting boat experiences to your network. Perfect for travel agencies, tour guides, and hospitality businesses.',
    competitiveRates: 'Competitive commission rates',
    marketingMaterials: 'Marketing materials provided',
    earningsTracking: 'Real-time earnings tracking',
    partnerSupport: 'Dedicated partner support',
    becomePartner: 'Become a Partner',
    
    // How It Works
    howItWorksTitle: 'How GoSea Works',
    howItWorksDescription: 'Getting started with your boat adventure is simple and secure',
    browseSearch: 'Browse & Search',
    browseDescription: 'Explore our curated selection of verified boats and experiences across Malaysian islands',
    bookSecurely: 'Book Securely',
    bookDescription: 'Choose your preferred date, time, and package. Complete your booking with secure online payment',
    getConfirmed: 'Get Confirmed',
    confirmDescription: 'Receive instant confirmation with detailed trip information and operator contact details',
    enjoyAdventure: 'Enjoy Adventure',
    enjoyDescription: 'Meet your operator at the designated location and enjoy your amazing boat experience',
    
    // CTA Section
    readyToStart: 'Ready to Start Your Island Adventure?',
    ctaDescription: 'Join thousands of satisfied customers who have discovered Malaysia\'s hidden gems with GoSea',
    bookFirstTrip: 'Book Your First Trip',
    
    // Footer
    footerDescription: 'Your trusted platform for discovering and booking amazing boat experiences across Malaysia\'s beautiful islands.',
    quickLinks: 'Quick Links',
    support: 'Support',
    helpCenter: 'Help Center',
    safetyGuidelines: 'Safety Guidelines',
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy',
    allRightsReserved: 'All rights reserved. Built for Malaysian sea adventures.',

    // Boat Owner Page
    backToHome: 'Back to Home',
    boatOwnerWelcome: 'Welcome Boat Owners',
    boatOwnerDescription: 'Join our platform and start earning by renting out your boats to adventure seekers across Malaysia.',
    signUpAsBoatOwner: 'Sign Up as Boat Owner',
    signInAsBoatOwner: 'Sign In as Boat Owner',
    continueWithGoogle: 'Continue with Google',
    continueWithEmail: 'Continue with Email',
    cancel: 'Cancel',
    earnIncome: 'Earn Income',
    earnIncomeDescription: 'Generate revenue by listing your boats on our platform.',
    easyManagement: 'Easy Management',
    easyManagementDescription: 'Manage bookings and availability with our intuitive dashboard.',
    securePayments: 'Secure Payments',
    securePaymentsDescription: 'Receive payments securely with our trusted payment system.',

    // Boat Owner Authentication
    boatOwnerSignIn: 'Boat Owner Sign In',
    newToGoSea: 'New to GoSea?',
    dontHaveBusinessAccount: "Don't have a business account?",
    registerBusiness: 'Register Business',
    signInBoatOwner: 'Sign In',
    registerBusinessAccount: 'Register Business',
    
    // Boat Owner Registration
    boatOwnerRegistration: 'Boat Owner Registration',
    registerAsBoatOwner: 'Register as a boat owner on GoSea Platform',
    joinGoSeaPlatform: 'Join GoSea Platform and start offering your marine services to customers',
    businessInformation: 'Business Information',
    requiredFields: '* Required fields',
    businessImages: 'Business Images',
    personalInformation: 'Personal Information',
    contactInformation: 'Contact Information',
    accountSecurity: 'Account Security',
    companyName: 'Company Name',
    enterCompanyName: 'Enter your company or business name',
    businessRegistrationNumber: 'Business Registration Number (BRN)',
    enterBRN: 'e.g., ************',
    businessEmailAddress: 'Business Email Address',
    enterBusinessEmail: '<EMAIL>',
    enterPhoneNumber: '+60*********',
    createStrongPassword: 'Create a strong password',
    confirmYourPassword: 'Confirm your password',
    passwordRequirements: 'Password Requirements',
    passwordMinLength: 'At least 8 characters long',
    passwordUppercase: 'Contains uppercase and lowercase letters',
    passwordLowercase: 'Contains at least one lowercase letter',
    passwordNumber: 'Contains at least one number',
    passwordSpecialChar: 'Contains at least one special character',
    passwordUppercaseSimple: 'One uppercase letter',
    passwordSpecialCharSimple: 'One special character',
    passwordMinLengthError: 'Password must be at least 8 characters long',
    passwordRequirementsError: 'Password must contain at least one uppercase letter and one special character',
    createBoatOwnerAccount: 'Create Boat Owner Account',
    signInHere: 'Sign in here',
    whatHappensAfterRegistration: 'What happens after registration?',
    nextSteps: 'Next Steps',
    checkEmailVerification: 'Check your email for a verification link',
    clickVerificationLink: 'Click the verification link to confirm your email',
    adminReviewApplication: 'Our admin team will review your application',
    receiveApprovalNotification: 'You\'ll receive approval notification within 1-2 business days',
    onceApprovedAccess: 'Once approved, you\'ll be able to sign in and start listing your boats and services.',
    returnToHomepage: 'Return to Homepage',
    learnMoreBoatOwnerFeatures: 'Learn More About Boat Owner Features',
    emailVerification: 'Email Verification',
    checkInboxVerifyEmail: 'Check your inbox and verify your email address',
    adminReview: 'Admin Review',
    teamReviewsApplication: 'Our team reviews your business application',
    startListing: 'Start Listing',
    addBoatsServices: 'Add your boats and services to the platform',
    creatingAccount: 'Creating Your Account...',
    fixPasswordRequirements: 'Please fix password requirements:',
    passwordsDoNotMatch: 'Passwords do not match',
    invalidPhoneNumberFormat: 'Please provide a valid Malaysia phone number (e.g., +60*********, 0*********)',
    accountAlreadyExists: 'An account with this email address already exists. Please use a different email or sign in instead.',
    registrationFailed: 'Registration failed. Please try again.',
    networkError: 'Network error. Please check your connection and try again.',
    registrationSuccessful: 'Registration Successful!',
    emailVerified: 'Email Verified!',
    emailVerificationSuccessful: 'Your business email has been successfully verified! Our admin team will review your application shortly.',
    boatOwnerRegistrationSuccess: 'Your boat owner registration was successful',
    thankYouForRegistering: 'Thank you for registering as a boat owner with GoSea',
    backToHomepage: 'Back to Homepage',
    malaysiaPhoneFormat: 'Malaysia phone number format',
    
    // Additional Boat Owner Registration Translations
    operatorType: 'Operator Type',
    individualOperator: 'Individual Operator',
    individualOperatorDescription: 'Operate as an individual without a registered business',
    businessOperator: 'Business Operator',
    businessOperatorDescription: 'Operate as a registered business entity',
    companyNameRequired: 'Company name is required for business operators',
    brnRequired: 'Business Registration Number is required for business operators',
    
    // Admin Dashboard
    boatOwnerApprovedSuccessfully: 'Boat owner approved successfully',
    failedToApproveBoatOwner: 'Failed to approve boat owner',
    boatOwnerRejectedSuccessfully: 'Boat owner rejected successfully',
    failedToRejectBoatOwner: 'Failed to reject boat owner',
    owner: 'Owner',
    created: 'Created',
    activated: 'activated',
    deactivated: 'deactivated',
    successfully: 'successfully',
    failedToUpdate: 'Failed to update',
    userStatus: 'user status',
    userRole: 'user role',
    updated: 'updated',
    providerVerification: 'provider verification',
    boatStatus: 'boat status',
    
    // Agent Page
    agentWelcome: 'Welcome Affiliate Agents',
    agentDescription: 'Partner with us and earn commissions by promoting boat rentals to your network.',
    signUpAsAgent: 'Sign Up as Agent',
    signInAsAgent: 'Sign In as Agent',
    earnCommission: 'Earn Commission',
    earnCommissionDescription: 'Get 10% commission on every successful booking you refer.',
    affiliateLinks: 'Affiliate Links',
    affiliateLinksDescription: 'Generate custom links to track your referrals and earnings.',
    trackPerformance: 'Track Performance',
    trackPerformanceDescription: 'Monitor your bookings and commission earnings in real-time.',

    // Authentication Modal
    orContinueWith: 'or continue with',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    firstName: 'First Name',
    lastName: 'Last Name',
    phoneNumber: 'Phone Number',
    enterEmail: 'Enter your email',
    enterPassword: 'Enter your password',
    enterFirstName: 'Enter first name',
    enterLastName: 'Enter last name',
    rememberMe: 'Remember me',
    forgotPassword: 'Forgot password?',
    signingIn: 'Signing in...',
    creatingAccount: 'Creating account...',
    createAccount: 'Create Account',
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: 'Already have an account?',

    // Profile Completion
    editProfile: 'Edit Profile',
    userProfile: 'User Profile',
    welcomeToGoSea: 'Welcome to GoSea!',
    profileCompletionOptional: 'You can complete your profile later from the dashboard. Continue with your booking for now.',
    'Please complete your profile information to continue with your booking.': 'Please complete your profile information to continue with your booking.',
    'Complete Profile': 'Complete Profile',
    'Complete Your Profile': 'Complete Your Profile',
    'Phone Number': 'Phone Number',
    'Date of Birth': 'Date of Birth',
    'Address': 'Address',
    'Emergency Contact': 'Emergency Contact',
    'Optional': 'Optional',
    'Phone number is required': 'Phone number is required',
    'Please enter a valid Malaysian phone number (e.g., +60*********, 0*********, or *********)': 'Please enter a valid Malaysian phone number (e.g., +60*********, 0*********, or *********)',
    'e.g., +60*********, 0*********, or *********': 'e.g., +60*********, 0*********, or *********',
    'Please enter a valid date of birth': 'Please enter a valid date of birth',
    'Select your date of birth': 'Select your date of birth',
    'Complete Profile': 'Complete Profile',
    'Skip for Now': 'Skip for Now',
    'Calendar': 'Calendar',
    'Type Date': 'Type Date',
    'Enter Date Manually': 'Enter Date Manually',
    'Supported formats: DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD': 'Supported formats: DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD',
    'Apply': 'Apply',
    'Cancel': 'Cancel',
    'Please enter a valid date of birth (age must be between 13 and 120)': 'Please enter a valid date of birth (age must be between 13 and 120)',
    'Please enter a valid date format (DD/MM/YYYY, DD-MM-YYYY, or YYYY-MM-DD)': 'Please enter a valid date format (DD/MM/YYYY, DD-MM-YYYY, or YYYY-MM-DD)',
    'Address Information': 'Address Information',
    'Street Address Line 1': 'Street Address Line 1',
    'Street Address Line 2': 'Street Address Line 2',
    'e.g., 123 Jalan Bukit Bintang': 'e.g., 123 Jalan Bukit Bintang',
    'e.g., Apartment, suite, unit, building, floor, etc.': 'e.g., Apartment, suite, unit, building, floor, etc.',
    'Postcode': 'Postcode',
    'e.g., 55100': 'e.g., 55100',
    'City': 'City',
    'e.g., Kuala Lumpur': 'e.g., Kuala Lumpur',
    'State': 'State',
    'Select State': 'Select State',
    'Emergency Contact Phone': 'Emergency Contact Phone',
    'Please enter a valid Malaysian phone number for emergency contact (e.g., +60*********, 0*********, or *********)': 'Please enter a valid Malaysian phone number for emergency contact (e.g., +60*********, 0*********, or *********',

    // Dashboard
    home: 'Home',
    profile: 'Profile',
    settings: 'Settings',
    logout: 'Logout',
    welcomeBack: 'Welcome back',
    dashboardSubtitle: 'Ready to explore the seas? Your maritime adventure continues here. Manage your bookings, discover new services, and dive into your next ocean experience.',
    account: 'Account',
    customer: 'Customer',
    boatowner: 'Boat Owner',
    affiliateagent: 'Affiliate Agent',
    admin: 'Admin',

    // Dashboard Cards
    accountInformation: 'Account Information',
    fullName: 'Full Name',
    emailAddress: 'Email Address',
    accountStatus: 'Account Status',
    role: 'Role',
    verificationStatus: 'Verification Status',
    verified: 'Verified',
    pendingVerification: 'Pending Verification',
    quickActions: 'Quick Actions',

    // Admin Dashboard
    contactPhone: 'Contact Phone',
    registrationDate: 'Registration Date',
    emailVerificationStatus: 'Email Verification Status',
    adminDashboard: 'Admin Dashboard',
    manageUsersProvidersBoats: 'Manage users, providers, and boats across the GoSea platform',
    overview: 'Overview',
    users: 'Users',
    providers: 'Providers',
    boats: 'Boats',
    totalUsers: 'Total Users',
    totalProviders: 'Total Providers',
    totalBoats: 'Total Boats',
    totalBookings: 'Total Bookings',
    active: 'Active',
    inactive: 'Inactive',
    approved: 'Approved',
    pending: 'Pending',
    verified: 'Verified',
    unverified: 'Unverified',
    inLast7Days: 'in last 7 days',
    allRoles: 'All Roles',
    allStatus: 'All Status',
    allVerificationStatus: 'All Verification Status',
    allActiveStatus: 'All Active Status',
    search: 'Search',
    searchByEmail: 'Search by email or name...',
    searchByCompany: 'Search by company name or email...',
    searchByBoatName: 'Search by boat name or registration...',
    searchByNameOrEmail: 'Search by name or email...',
    user: 'User',
    boatOwnerApprovals: 'Boat Owner Approvals',
    managePendingBoatOwnerApplications: 'Manage pending boat owner applications',
    companyInformation: 'Company Information',
    registrationDetails: 'Registration Details',
    noPendingApprovals: 'No Pending Approvals',
    allBoatOwnerApplicationsProcessed: 'All boat owner applications have been processed',
    emailPending: 'Email Pending',
    nameNotProvided: 'Name Not Provided',
    allDates: 'All Dates',
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    verification: 'Verification',
    status: 'Status',
    joined: 'Joined',
    actions: 'Actions',
    activate: 'Activate',
    deactivate: 'Deactivate',
    verify: 'Verify',
    unverify: 'Unverify',

    // Boat Owner Module
    providerStatus: 'Provider Status',
    approvedOn: 'Approved On',
    businessAddress: 'Business Address',
    licenseExpiryDate: 'License Expiry Date',
    websiteUrl: 'Website URL',
    businessPhone: 'Business Phone',
    businessEmail: 'Business Email',
    operatingLicense: 'Operating License',
    displayName: 'Display Name',
    personalAddress: 'Personal Address',
    timezone: 'Timezone',
    personalPhone: 'Personal Phone',
    boatOwnerModule: 'Boat Owner Module',
    boatOwnerDashboard: 'Boat Owner Dashboard',
    businessProfile: 'Business Profile',
    myBoats: 'My Boats',
    myServices: 'My Services',
    manageYourBoatBusiness: 'Manage your boat business',
    manageYourBusinessProfile: 'Manage your business profile',
    manageYourBoatFleet: 'Manage your boat fleet',
    manageYourServices: 'Manage your services',
    manageYourBookings: 'Manage your bookings',
    viewYourBusinessAnalytics: 'View your business analytics',
    welcomeBack: 'Welcome Back',
    hereIsYourBusinessOverview: 'Here is your business overview',
    activeServices: 'Active Services',
    recentBookings: 'Recent Bookings',
    monthlyRevenue: 'Monthly Revenue',
    fromLastMonth: 'from last month',
    quickActions: 'Quick Actions',
    addNewBoat: 'Add Boat',
    addBoatToFleet: 'Add boat to your fleet',
    createService: 'Create Service',
    createNewService: 'Create new service',
    viewBookings: 'View Bookings',
    manageBookings: 'Manage bookings',
    viewAnalytics: 'View Analytics',
    businessInsights: 'Business insights',
    recentActivity: 'Recent Activity',
    noRecentActivity: 'No recent activity',
    activityWillAppearHere: 'Activity will appear here',

    // Profile Management
    editBusinessProfile: 'Edit Business Profile',
    updateYourBusinessInformation: 'Update your business information',
    profileOverview: 'Profile Overview',
    businessInformation: 'Business Information',
    businessStatistics: 'Business Statistics',
    businessDocuments: 'Business Documents',
    memberSince: 'Member since',
    notProvided: 'Not provided',
    noDescriptionProvided: 'No description provided',
    noDocumentsUploaded: 'No documents uploaded',
    uploadDocuments: 'Upload documents',
    uploadedOn: 'Uploaded on',
    personalInformation: 'Personal Information',
    businessDescription: 'Business Description',
    describeYourBusiness: 'Describe your business',
    profile: 'Profile',
    notAvailable: 'Not available',
    english: 'English',
    bahasaMalaysia: 'Bahasa Malaysia',
    businessLogo: 'Business Logo',
    coverImage: 'Cover Image',
    logo: 'Logo',
    uploadLogo: 'Upload Logo',
    uploadCoverImage: 'Upload Cover Image',
    invalidImageFormat: 'Please select a valid image file (JPG, PNG, WebP)',
    imageSizeLimit: 'Image size must be less than 5MB',
    failedToUploadLogo: 'Failed to upload logo',
    failedToUploadCoverImage: 'Failed to upload cover image',
    boatName: 'Boat Name',
    boatNameRequired: 'Boat name is required',
    enterBoatName: 'Enter boat name',
    validCapacityRequired: 'Valid capacity is required',
    maxPassengers: 'Max passengers',
    registrationNumber: 'Registration Number',
    registrationNumberRequired: 'Registration number is required',
    enterRegistrationNumber: 'Enter registration number',
    location: 'Location',
    locationRequired: 'Location is required',
    boatLocation: 'Boat location',
    yearBuilt: 'Year Built',
    enterYearBuilt: 'Enter year built',
    serviceType: 'Service Type',
    selectServiceType: 'Select service type',
    describeYourBoat: 'Describe your boat',
    boatImages: 'Boat Images',
    clickToUploadImages: 'Click to upload images',
    maxImagesAllowed: 'Maximum {{max}} images allowed',
    imageLimitExceeded: 'You can only upload a maximum of {{max}} images. Please remove some images before adding more.',
    technicalSpecifications: 'Technical Specifications',
    length: 'Length',
    boatLength: 'Boat length',
    engineType: 'Engine Type',
    enginePower: 'Engine Power',
    safetyRating: 'Safety Rating',
    safetyRatingA: 'A - Very Safe',
    safetyRatingB: 'B - Safe',
    safetyRatingC: 'C - Moderate',
    material: 'Material',
    boatMaterial: 'Boat material',
    amenities: 'Amenities',
    airConditioning: 'Air Conditioning',
    wifi: 'WiFi',
    restroom: 'Restroom',
    kitchen: 'Kitchen',
    soundSystem: 'Sound System',
    fishingEquipment: 'Fishing Equipment',
    snorkelingGear: 'Snorkeling Gear',
    lifeJackets: 'Life Jackets',
    includedItems: 'Included Items',
    refreshments: 'Refreshments',
    towels: 'Towels',
    sunscreen: 'Sunscreen',
    firstAidKit: 'First Aid Kit',
    waterSports: 'Water Sports',
    approvedBy: 'Approved By',
    approvedAt: 'Approved At',
    rejectionReason: 'Rejection Reason',
    basePrice: 'Base Price',
    basePriceRequired: 'Base price is required',
    // Add routes translations
    routes: 'Routes',
    searchRoutes: 'Search Routes',
    searchForRoutes: 'Search for routes...',
    availableRoutes: 'Available Routes',
    selectedRoutes: 'Selected Routes',
    noRoutesFound: 'No routes found',
    startTypingToSearchRoutes: 'Start typing to search routes...',
    noRoutesSelected: 'Click "Search Routes" field to select destinations for your service',

    // Boat Details
    boatDetails: 'Boat Details',
    boatNotFound: 'Boat not found',
    backToBoats: 'Back to Boats',
    failedToLoadBoat: 'Failed to load boat',
    passengers: 'passengers',
    edit: 'Edit',
    delete: 'Delete',
    noImagesAvailable: 'No images available',
    description: 'Description',
    noDescriptionAvailable: 'No description available',
    basicInformation: 'Basic Information',
    assignedServices: 'Assigned Services',
    noServicesAssigned: 'No services assigned',
    view: 'View',
    confirmDeleteBoat: 'Are you sure you want to delete this boat?',
    confirmDeleteBoatTitle: 'Confirm Boat Deletion',
    confirmDeleteBoatMessage: 'Are you sure you want to delete this boat? This action cannot be undone.',
    failedToDeleteBoat: 'Failed to delete boat',

    // Boat List
    searchBoats: 'Search boats',
    allStatuses: 'All Statuses',
    noBoatsFound: 'No boats found',
    noBoatsMatchFilters: 'No boats match the current filters',
    startByAddingYourFirstBoat: 'Start by adding your first boat',
    addYourFirstBoat: 'Add First Boat',

    // Service Management
    createYourFirstService: 'Create First Service',
    searchServices: 'Search services',
    allCategories: 'All Categories',
    dayTrip: 'Day Trip',
    snorkeling: 'Snorkeling',
    fishing: 'Fishing',
    islandHopping: 'Island Hopping',
    sunset: 'Sunset',
    diving: 'Diving',
    watersports: 'Watersports',
    charter: 'Charter',
    max: 'max',
    routes: 'routes',
    assignedBoats: 'Assigned Boats',
    more: 'more',
    noServicesFound: 'No services found',
    noServicesMatchFilters: 'No services match the current filters',
    startByCreatingYourFirstService: 'Start by creating your first service',
    confirmDeleteService: 'Are you sure you want to delete this service?',
    failedToDeleteService: 'Failed to delete service',
    category: 'Category',

    // Bookings Management
    bookings: 'Bookings',
    confirmed: 'Confirmed',
    cancelled: 'Cancelled',
    completed: 'Completed',
    allServices: 'All Services',
    dateFrom: 'Date From',
    dateTo: 'Date To',
    filter: 'Filter',
    confirm: 'Confirm',
    cancel: 'Cancel',
    markCompleted: 'Mark Completed',
    bookingStatusUpdated: 'Booking status updated',
    failedToUpdateBookingStatus: 'Failed to update booking status',
    noBookingsFound: 'No bookings found',
    noBookingsMatchFilters: 'No bookings match the current filters',
    bookingsWillAppearHere: 'Bookings will appear here',
    manageServices: 'Manage Services',

    // Analytics
    analytics: 'Analytics',
    last7Days: 'Last 7 Days',
    last30Days: 'Last 30 Days',
    last90Days: 'Last 90 Days',
    lastYear: 'Last Year',
    confirmedBookings: 'Confirmed Bookings',
    totalRevenue: 'Total Revenue',
    conversionRate: 'Conversion Rate',
    bookingStatusBreakdown: 'Booking Status Breakdown',
    performanceSummary: 'Performance Summary',
    bookingConversionRate: 'Booking Conversion Rate',
    averageBookingValue: 'Average Booking Value',
    cancellationRate: 'Cancellation Rate',
    revenueInsights: 'Revenue Insights',
    dailyAverageRevenue: 'Daily Average Revenue',
    recommendations: 'Recommendations',
    improveConversionRate: 'Improve Conversion Rate',
    conversionRateRecommendation: 'Your conversion rate is below 50%. Try improving service quality and competitive pricing.',
    getYourFirstBooking: 'Get Your First Booking',
    firstBookingRecommendation: 'Make sure your services are attractive and reasonably priced to attract your first customers.',
    excellentPerformance: 'Excellent Performance',
    excellentPerformanceMessage: 'Congratulations! Your conversion rate is excellent. Keep up the great work!',
    noDataAvailable: 'No data available',
    unknown: 'Unknown',

    // Common Terms
    retry: 'Retry',
    saving: 'Saving',
    saveChanges: 'Save Changes',
    editBoat: 'Edit Boat',
    editYourBoatDetails: 'Edit your boat details',
    boatUpdatedSuccessfully: 'Boat updated successfully',
    failedToUpdateBoat: 'Failed to update boat',
    serviceName: 'Service Name',
    serviceNameRequired: 'Service name is required',
    enterServiceName: 'Enter service name',
    categoryRequired: 'Category is required',
    selectCategory: 'Select category',
    hours: 'hours',
    serviceDuration: 'Service duration',
    maxCapacityRequired: 'Maximum capacity is required',
    maximumPassengers: 'Maximum passengers',
    describeYourService: 'Describe your service',
    pricing: 'Pricing',
    adultPrice: 'Adult Price',
    adultPriceRequired: 'Adult price is required',
    childPrice: 'Child Price',
    infantPrice: 'Infant Price',
    zeroValuePricingTitle: 'Confirm Free Pricing',
    zeroValuePricingMessage: 'The following age categories have 0 value and will be regarded as FREE',
    zeroValuePricingBasicMessage: 'The price per person is set to 0 and will be regarded as FREE. Do you want to continue?',
    zeroValuePricingConfirm: 'Do you want to continue?',
    packageTypeRequired: 'Package type is required',
    packagePriceRequired: 'Package price is required and must be greater than 0',
    atLeastOneAgePriceRequired: 'At least one age category price must be set',
    assignBoats: 'Assign Boats',
    selectAtLeastOneBoat: 'Please select at least one boat',
    noBoatsAvailable: 'No boats available',
    addBoatsFirst: 'Please add boats to your fleet first',
    createNewServiceForYourBusiness: 'Create a new service for your business',
    serviceCreatedSuccessfully: 'Service created successfully',
    failedToCreateService: 'Failed to create service',
    signInToAddBoat: 'Sign In to Add Your Boat',
    signInToAddBoatDescription: 'Please sign in to your boat owner account to add boats to your fleet.',
    draft: 'Draft',
    pendingApproval: 'Pending Approval',
    approved: 'Approved',
    rejected: 'Rejected',
    allStatuses: 'All Statuses',
    regNo: 'Reg No',
    provider: 'Provider',
    owner: 'Owner',
    services: 'Services',
    created: 'Created',
    boat: 'Boat',
    capacity: 'Capacity',
    seats: 'seats',
    people: 'people',
    noRegistration: 'No registration',
    noProvider: 'No provider',
    draft: 'Draft',
    pendingApproval: 'Pending Approval',
    rejected: 'Rejected',
    maintenance: 'Maintenance',
    customer: 'Customer',
    boatOwner: 'Boat Owner',
    affiliateAgent: 'Affiliate Agent',
    loadingAdminDashboard: 'Loading admin dashboard...',
    showing: 'Showing',
    to: 'to',
    of: 'of',
    results: 'results',
    previous: 'Previous',
    next: 'Next',
    servicesCount: 'services',
    boatsCount: 'boats',
    allActiveStatus: 'All Active Status',
    activeStatus: 'Active Status',

    // Expandable Account Information
    phoneNumber: 'Phone Number',
    dateOfBirth: 'Date of Birth',
    address: 'Address',
    emergencyContact: 'Emergency Contact',
    companyName: 'Company Name',
    businessRegistrationNumber: 'Business Registration Number',
    agencyName: 'Agency Name',
    memberSince: 'Member Since',
    lastLogin: 'Last Login',
    viewMore: 'View More',
    viewLess: 'View Less',
    completeYourProfile: 'Complete Your Profile',
    addMoreInfoToProfile: 'Add more information to your profile for a better experience',
    updateProfile: 'Update Profile',
    viewProfile: 'View Profile',
    profile: 'Profile',
    manageYourProfileInformation: 'Manage your profile information',
    basicInformation: 'Basic Information',
    accountInformation: 'Account Information',
    businessInformation: 'Business Information',
    agencyInformation: 'Agency Information',
    language: 'Language',

    // Profile Edit Page
    editProfile: 'Edit Profile',
    updateYourProfileInformation: 'Update your profile information',
    firstName: 'First Name',
    lastName: 'Last Name',
    enterFirstName: 'Enter your first name',
    enterLastName: 'Enter your last name',
    firstNameRequired: 'First name is required',
    preferredLanguage: 'Preferred Language',
    languagePreferenceHelp: 'Select your preferred language for the application interface',
    selectDateOfBirth: 'Select your date of birth',
    enterAddressLine1: 'Enter address line 1',
    enterAddressLine2: 'Enter address line 2',
    enterCity: 'Enter city',
    enterPostcode: 'Enter postcode',
    enterState: 'Enter state',
    enterCompanyName: 'Enter company name',
    enterDisplayName: 'Enter display name',
    enterBRN: 'Enter Business Registration Number',
    enterOperatingLicense: 'Enter operating license',
    enterBusinessEmail: 'Enter business email',
    enterAgencyName: 'Enter agency name',
    selectLicenseExpiryDate: 'Select license expiry date',
    describeYourBusiness: 'Describe your business',
    personalAddress: 'Personal Address',
    businessAddress: 'Business Address',
    websiteUrlPlaceholder: 'Enter website url',

    // Standard Modal
    close: 'Close',
    continue: 'Continue',
    details: 'Details',
    operationSuccessful: 'Operation Successful',
    operationFailed: 'Operation Failed',
    successMessage: 'Your request has been processed successfully.',
    errorMessage: 'An error occurred while processing your request. Please try again.',
    tryAgain: 'Try Again',
    ok: 'OK',
    
    // Forgot Password Modal
    sendResetLink: 'Send Reset Link',
    backToSignIn: 'Back to Sign In',
    sending: 'Sending...',

    // Profile Completion Modal
    profileCompletionSuccess: 'Your profile has been completed successfully. You can now access all features.',
    updateProfileError: 'Profile Update Error',
    profileUpdateFailed: 'Failed to update profile. Please try again.',

    // Boat Search
    searchBoats: 'Search Boats',
    findPerfectBoat: 'Find the perfect boat for your adventure',
    boatProviders: 'Boat Providers',
    searchingBoatProviders: 'Searching for boat providers...',
    noBoatProvidersFound: 'No Boat Providers Found',
    searchFilters: 'Search Filters',
    hideFilters: 'Hide Filters',
    showFilters: 'Show Filters',
    serviceType: 'Service Type',
    allServices: 'All Services',
    snorkeling: 'Snorkeling',
    passengerBoat: 'Passenger Boat',
    location: 'Location',
    allLocations: 'All Locations',
    redangIsland: 'Redang Island',
    perhentianIsland: 'Perhentian Island',
    date: 'Date',
    time: 'Time',
    when: 'When',
    selectDate: 'Select Date',
    selectTime: 'Select time',
    selectDateTime: 'Pick a date and time',
    selectedTime: 'Selected time',
    selectServiceType: 'Select service type',
    serviceTypeRequired: 'Please select a service type',
    maxCapacityRequired: 'Maximum capacity is required',
    routeRequired: 'Route is required',
    boatRequired: 'Boat is required',
    serviceCreatedSuccessfully: 'Service created successfully!',
    failedToCreateService: 'Failed to create service',
    basicInformation: 'Basic Information',
    serviceName: 'Service Name',
    serviceNameRequired: 'Service name is required',
    serviceType: 'Service Type',
    selectServiceType: 'Select service type',
    duration: 'Duration',
    minutes: 'minutes',
    serviceDuration: 'Service duration',
    maxCapacity: 'Maximum Capacity',
    maximumPassengers: 'Maximum passengers',
    description: 'Description',
    descriptionRequired: 'Description is required',
    describeYourService: 'Describe your service',
    pricing: 'Pricing',
    basePrice: 'Base Price',
    pricingModel: 'Pricing Model',
    basicPricing: 'Basic Pricing',
    packageOnly: 'Package Only',
    ageBased: 'Age Based',
    packageAndAge: 'Package and Age Based',
    inclusionsExclusions: 'Inclusions & Exclusions',
    includedItems: 'Included Items',
    excludedItems: 'Excluded Items',
    enterItem: 'Enter item',
    addItem: 'Add Item',
    searchInclusions: 'Search for inclusions or type to add custom...',
    searchExclusions: 'Search for exclusions or type to add custom...',
    suggestedInclusions: 'Suggested Inclusions',
    suggestedExclusions: 'Suggested Exclusions',
    addCustom: 'Add Custom',
    startTypingToSearch: 'Start typing to search...',
    selectedInclusions: 'Selected Inclusions',
    selectedExclusions: 'Selected Exclusions',
    specialInstructions: 'Special Instructions',
    enterSpecialInstructions: 'Enter special instructions',
    routes: 'Routes',
    routeAndBoat: 'Route & Boat',
    route: 'Route',
    selectRoute: 'Select route',
    priceModifier: 'Price Modifier',
    removeRoute: 'Remove Route',
    addRoute: 'Add Route',
    duplicateRouteNotAllowed: 'Duplicate routes are not allowed',
    boatAssignments: 'Boat Assignments',
    boat: 'Boat',
    selectBoat: 'Select boat',
    primaryBoat: 'Primary Boat',
    capacityOverride: 'Capacity Override',
    leaveEmptyForFullCapacity: 'Leave empty for full capacity',
    removeBoat: 'Remove Boat',
    addBoat: 'Add Boat',
    duplicateBoatNotAllowed: 'Duplicate boats are not allowed',
    packages: 'Packages',
    packageType: 'Package Type',
    selectPackageType: 'Select package type',
    ageBasedPricing: 'Age Based Pricing',
    ageBasedPrice: 'Age Based Price',
    addAgeCategory: 'Add Age Category',
    allAgeCategoriesAdded: 'All age categories added',
    clickAddAgeCategoryText: 'Click "Add Age Category" to set pricing for different age groups',
    atLeastOneAgeCategoryRequired: 'At least one age category must have a price greater than 0',
    removeAgeCategory: 'Remove Age Category',
    ageCategoryRemovalNote: 'Remove inapplicable age categories using the - button',
    removePackage: 'Remove Package',
    addPackage: 'Add Package',
    schedules: 'Schedules',
    dayOfWeek: 'Day of Week',
    daily: 'Daily',
    sunday: 'Sunday',
    monday: 'Monday',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    departureTime: 'Departure Time',
    availableCapacity: 'Available Capacity',
    removeSchedule: 'Remove Schedule',
    addSchedule: 'Add Schedule',
    
    // Itinerary
    itinerary: 'Itinerary',
    optional: 'Optional',
    itineraryDescription: 'Create a detailed schedule for your service to help customers understand what to expect during their trip.',
    noItineraryItems: 'No itinerary items added yet. Click "Add Itinerary Item" to get started.',
    time: 'Time',
    activity: 'Activity',
    location: 'Location',
    activityPlaceholder: 'Snorkeling at Coral Point',
    locationPlaceholder: 'Coral Point',
    removeItineraryItem: 'Remove Itinerary Item',
    addItineraryItem: 'Add Itinerary Item',
    moveItemUp: 'Move Item Up',
    moveItemDown: 'Move Item Down',
    reorderItineraryItems: 'Reorder Itinerary Items',
    duplicateTimeError: 'This time is already used in another itinerary item. Please select a different time.',
    
    // Images
    images: 'Images',
    uploadImages: 'Upload Images',
    clickToUpload: 'Click to upload',
    orDragAndDrop: 'or drag and drop',
    imageRequirements: 'PNG, JPG, GIF up to 10MB',
    maxImagesAllowed: 'Maximum {max} images allowed',
    previous: 'Previous',
    next: 'Next',
    createService: 'Create Service',
    creating: 'Creating...',
    step1: 'Basic Info',
    step2: 'Routes',
    step3: 'Boats',
    step4: 'Packages',
    step5: 'Pricing',
    step6: 'Inclusions',
    step7: 'Schedule',
    selectLocation: 'Select location',
    selectJetty: 'Select jetty',
    serviceTypeRequired: 'Please select a service type',
    locationRequired: 'Please select a location',
    dateTimeRequired: 'Please select date and time',
    destinationRequired: 'Please select a destination',
    from: 'from',
    jetty: 'Jetty',

    // Search Form Loading States
    loadingLocations: 'Loading locations...',
    loadingJetties: 'Loading jetties...',
    loadingServices: 'Loading services...',
    loadingDestinations: 'Loading destinations...',

    // Search Form Empty States
    noJettiesAvailable: 'No jetties available',
    noServicesAvailable: 'No service types available',
    noDestinationsAvailable: 'No destinations available',
    noDestinationsFromLocation: 'No destinations available from this location',

    // Destination Selector
    destination: 'Destination',
    allDestinations: 'All Destinations',
    selectDestination: 'Select destination',
    selectLocationFirst: 'Select location first',

    // Booking Wizard
    bookThisBoat: 'Book This Boat',
    selectPackage: 'Select Package',
    clickToSelectDate: 'Click to select a date',

    // Age-based Pricing
    adults: 'Adults',
    children: 'Children',
    toddlers: 'Toddlers',
    adultsAge: 'Adults (12+ years)',
    childrenAge: 'Children (4-11 years)',
    toddlersAge: 'Toddlers (0-3 years)',
    passengerBreakdown: 'Passenger Breakdown',
    ageBasedPricing: 'Age-Based Pricing',
    free: 'Free',
    selectPassengers: 'Select Passengers',
    selectedDate: 'Selected Date',
    unavailable: 'Unavailable',
    adults: 'Adults',
    children: 'Children',
    perPerson: 'per person',
    perChild: 'per child',
    totalPrice: 'Total Price',
    previous: 'Previous',
    next: 'Next',
    bookNow: 'Book Now',

    // Boat Details
    itineraryAndInclusions: 'Itinerary & Inclusions',
    schedule: 'Schedule',
    boatNotFound: 'Boat Not Found',
    boatNotFoundDescription: 'The boat you are looking for could not be found.',
    backToSearch: 'Back to Search',
    description: 'Description',
    packages: 'Packages',
    amenities: 'Amenities',
    boatOwner: 'Boat Owner',
    signInRequired: 'Sign In Required',
    pleaseSignInToBook: 'Please sign in to make a booking',
    home: 'Home',
    getStarted: 'Get Started',
    simpleProcess: 'Simple Process',
    contactUs: 'Contact Us',
    messageSentSuccess: 'Message sent successfully!',
    messageSentError: 'Failed to send message. Please try again.',
    fullName: 'Full Name',
    enterFullName: 'Enter your full name',
    enterEmail: 'Enter your email address',
    subject: 'Subject',
    enterSubject: 'Enter message subject',
    message: 'Message',
    enterMessage: 'Enter your message',
    sending: 'Sending...',
    sendMessage: 'Send Message',
    hour: 'Hour',
    minute: 'Minute',
    period: 'Period',
    done: 'Done',
    cancel: 'Cancel',
    capacity: 'Capacity',
    numberOfPeople: 'Number of people',
    searching: 'Searching...',
    clearFilters: 'Clear Filters',
    searchingBoats: 'Searching for boats...',
    noBoatsFound: 'No boats found',
    tryDifferentFilters: 'Try adjusting your search filters',
    searchResults: 'Search Results',
    people: 'people',
    trip: 'trip',
    viewDetails: 'View Details',
    readyToSearch: 'Ready to find your boat?',
    useFiltersAbove: 'Use the filters above to search for available boats',
    searchHelpText: 'Find the perfect boat for your Malaysian sea adventure',

    // Boat Details
    boatNotFound: 'Boat Not Found',
    boatNotFoundDescription: 'The boat you are looking for could not be found or is no longer available.',
    backToResults: 'Back to Results',
    backToSearch: 'Back to Search',
    photos: 'Photos',
    description: 'Description',
    packages: 'Packages',
    included: 'Included',
    amenities: 'Amenities',
    bookThisBoat: 'Book This Boat',
    basePrice: 'Base Price',
    selectedPackage: 'Selected Package',
    total: 'Total',
    bookNow: 'Book Now',
    bookingTerms: 'By booking, you agree to our terms and conditions',
    boatOwner: 'Boat Owner',
    signInRequired: 'Sign In Required',
    pleaseSignInToBook: 'Please sign in to your account to book this boat.',
    per: 'per',
    lastNameRequired: 'Last name is required',
    phoneNumber: 'Phone Number',
    optional: 'Optional',
    dateOfBirth: 'Date of Birth',
    selectDateOfBirth: 'Select your date of birth',
    addressInformation: 'Address Information',
    addressLine1: 'Address Line 1',
    addressLine2: 'Address Line 2',
    enterAddressLine1: 'Enter address line 1',
    enterAddressLine2: 'Enter address line 2',
    postcode: 'Postcode',
    city: 'City',
    state: 'State',
    enterCity: 'Enter city',
    enterState: 'Enter state',
    emergencyContact: 'Emergency Contact',
    invalidPhoneNumber: 'Please enter a valid Malaysian phone number',
    invalidEmergencyContact: 'Please enter a valid emergency contact number',
    uploadPhoto: 'Upload Photo',
    changePhoto: 'Change Photo',
    supportedFormats: 'Supported formats',
    cancel: 'Cancel',
    saveChanges: 'Save Changes',
    saving: 'Saving...',
    profileUpdatedSuccessfully: 'Profile updated successfully!',
    updateProfileError: 'Failed to update profile. Please try again.',
    backToDashboard: 'Back to Dashboard',
    browseServices: 'Browse Services',
    myBookings: 'My Bookings',
    manageBoats: 'Manage Boats',
    viewBookings: 'View Bookings',
    referralLinks: 'Referral Links',
    commissionReport: 'Commission Report',
    accountSettings: 'Account Settings',
    recentActivity: 'Recent Activity',
    noRecentActivity: 'No recent activity to display.',
    startExploring: 'Start exploring to see your activity here!',

    // Customer Dashboard
    discoverMarineServices: 'Discover Marine Services',
    exploreServices: 'Explore our wide range of marine services and book your next adventure.',
    snorkeling: 'Snorkeling',
    snorkelingDesc: 'Discover underwater wonders and explore vibrant coral reefs',
    boatTours: 'Boat Tours',
    boatToursDesc: 'Scenic island hopping and coastal exploration adventures',
    islandTrips: 'Island Trips',
    islandTripsDesc: 'Explore pristine islands and hidden tropical paradises',

    // Boat Owner Dashboard
    boatManagement: 'Boat Management',
    manageFleet: 'Manage your fleet and track bookings for your boats.',
    totalBoats: 'Total Boats',
    addFirstBoat: 'Add First Boat',
    activeBookings: 'Active Bookings',
    noActiveBookings: 'No active bookings',
    monthlyRevenue: 'Monthly Revenue',
    startEarning: 'Start earning today',

    // Affiliate Agent Dashboard
    affiliateDashboard: 'Affiliate Dashboard',
    trackReferrals: 'Track your referrals and commission earnings.',
    totalReferrals: 'Total Referrals',
    startReferring: 'Start referring customers',
    commissionEarned: 'Commission Earned',
    noCommissions: 'No commissions yet',
    conversionRate: 'Conversion Rate',
    noDataAvailable: 'No data available',

    // Booking Page Translations
    yearsOld: 'years old',
    bookService: 'Book Service',
    bookingDetails: 'Booking Details',
    bookingSummary: 'Booking Summary',
    missingProviderService: 'Missing provider or service information',
    failedLoadProvider: 'Failed to load provider details',
    failedLoadService: 'Failed to load service details',
    authenticationRequired: 'Authentication Required',
    serviceRequired: 'Service Required',
    pleaseSelectService: 'Please select a service to book.',
    routeRequired: 'Route Required',
    pleaseSelectDestination: 'Please select a destination.',
    dateTimeRequiredBooking: 'Date and Time Required',
    pleaseSelectDateTime: 'Please select both date and time for your booking.',
    bookingFailed: 'Booking Failed',
    failedCreateBooking: 'Failed to create booking',
    bookingError: 'Booking Error',
    bookingErrorMessage: 'An error occurred while creating your booking. Please try again.',
    bookingConfirmed: 'Booking Confirmed!',
    bookingConfirmedMessage: 'Your booking has been confirmed. Booking ID:',
    standardService: 'Standard Service',
    basicServiceOffering: 'Basic service offering',
    packageOffering: 'Package offering',
    serviceIncluded: 'Service included',
    transportation: 'Transportation',
    professionalGuide: 'Professional guide',
    safetyEquipment: 'Safety equipment',
    notSelected: 'Not selected',
    specialRequestsOptional: 'Special Requests (Optional)',
    specialRequestsPlaceholder: 'Any special requirements or requests...',
    confirmBooking: 'Confirm Booking',
    creatingBooking: 'Creating Booking...',
    mostPopular: 'Most Popular',
    popular: 'Popular',
    free: 'FREE',
    perPerson: 'per person',
    adults: 'Adults',
    children: 'Children',
    toddlers: 'Toddlers',
    seniors: 'Seniors',
    pwd: 'PWD',
    passengers: 'Passengers',
    adultsAge: 'Adults (13+ years)',
    childrenAge: 'Children (3-12 years)',
    toddlersAge: 'Toddlers (0-2 years)',
    provider: 'Provider',
    route: 'Route',
    totalPassengers: 'Total Passengers',
    person: 'Person',
    maximumCapacity: 'Maximum capacity',
    totalCapacityLabel: 'Total',
    package: 'Package',
    available: 'Available',
    unavailable: 'Unavailable',
    noTimeSlotsAvailable: 'No time slots available for the selected date',
    numberOfPassengers: 'Number of Passengers',
    personWithDisabilities: 'Person with Disabilities',

    // Calendar and Time
    sun: 'Sun',
    mon: 'Mon',
    tue: 'Tue',
    wed: 'Wed',
    thu: 'Thu',
    fri: 'Fri',
    sat: 'Sat',

    // Month names
    january: 'January',
    february: 'February',
    march: 'March',
    april: 'April',
    may: 'May',
    june: 'June',
    july: 'July',
    august: 'August',
    september: 'September',
    october: 'October',
    november: 'November',
    december: 'December',

    // Day names (full)
    sunday: 'Sunday',
    monday: 'Monday',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',

    // Time picker
    hour: 'Hour',
    minute: 'Minute',

    availableTimeSlots: 'Select Time Slots',

    // Common Actions and States
    loading: 'Loading...',
    error: 'Error',
    failed: 'Failed',
    required: 'Required',
    optional: 'Optional',
    available: 'Available',
    unavailable: 'Unavailable',
    highAvailability: 'High availability',
    mediumAvailability: 'Medium availability',
    lowAvailability: 'Low availability',
    seatAvailabilityLegend: 'Numbers show available/total seats',
    select: 'Select',
    choose: 'Choose',
    add: 'Add',
    remove: 'Remove',
    update: 'Update',
    create: 'Create',
    confirm: 'Confirm',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    submit: 'Submit',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    show: 'Show',
    hide: 'Hide',
    more: 'More',
    less: 'Less',
    all: 'All',
    none: 'None',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    close: 'Close',
    open: 'Open',

    // Validation Messages
    pleaseSelectDate: 'Please select a date',
    pleaseSelectTime: 'Please select a time',
    pleaseSelectPassengers: 'Please select number of passengers',
    atLeastOneAdult: 'At least one adult passenger is required',
    pleaseSelectPackage: 'Please select a package',

    // Provider Page Translations
    loadingProviderDetails: 'Loading provider details...',
    providerNotFound: 'Provider Not Found',
    providerNotFoundMessage: 'The provider you are looking for does not exist.',
    backToSearch: 'Back to Search',
    providerLogo: 'Provider Logo',
    providerNameNotAvailable: 'Provider Name Not Available',
    operatingSince: 'Operating since',
    rating: 'rating',
    availableServices: 'Available Services',
    noServicesAvailable: 'No services available at the moment.',
    fleet: 'Fleet',
    boats: 'Boats',
    viewDetails: 'View Details',
    bookNow: 'Book Now',
    failedLoadProviderDetails: 'Failed to load provider details',
    errorLoadingProvider: 'An error occurred while loading provider details',
    upTo: 'Up to',

    // Service Page Translations
    uploadServiceImages: 'Upload Service Images',
    selectRoutes: 'Select Routes',
    atLeastOneRouteRequired: 'At least one route is required',
    atLeastOneInclusionRequired: 'At least one included item is required',
    service: 'Service',
    serviceNotFound: 'Service Not Found',
    serviceNotFoundMessage: 'The requested service could not be found.',
    errorLoadingService: 'Failed to load service details. Please try again.',
    backToProvider: 'Back to',
    serviceDetails: 'Service Details',
    duration: 'Duration',
    maxCapacity: 'Max Capacity',
    passengers: 'passengers',
    provider: 'Provider',
    includedItems: 'What\'s Included',
    excludedItems: 'What\'s Not Included',
    includedExcludedItems: 'What\'s Included & Not Included',
    specialInstructions: 'Special Instructions',
    itinerary: 'Itinerary',
    noItineraryAvailable: 'No itinerary available for this service',
    noItineraryMessage: 'The detailed itinerary for this service has not been provided yet. Please contact the provider for more information about the schedule.',
    bookThisService: 'Book This Service',
    location: 'Location',
    fromService: 'from',
    availableDestinations: 'Available Destinations',

    // Booking Page Additional Translations
    standardService: 'Standard Service',
    basicServiceOffering: 'Basic service offering',
    packageOffering: 'Package offering',
    serviceIncluded: 'Service included',
    transportation: 'Transportation',
    professionalGuide: 'Professional guide',
    safetyEquipment: 'Safety equipment',
    selectPackage: 'Select Package',
    mostPopular: 'Most Popular',
    popular: 'Popular',
    free: 'FREE',
    perPerson: 'per person',
    adults: 'Adults',
    children: 'Children',
    toddlers: 'Toddlers',
    seniors: 'Seniors',
    adultsAge: 'Adults (13+ years)',
    childrenAge: 'Children (3-12 years)',
    toddlersAge: 'Toddlers (0-2 years)',
    numberOfPassengers: 'Number of Passengers',
    maximumCapacity: 'Maximum capacity',
    totalPassengers: 'Total Passengers',
    packageLabel: 'Package',
    dateLabel: 'Date',
    timeLabel: 'Time',
    notSelected: 'Not selected',
    person: 'Person',
    more: 'more',
    additionalItems: 'Additional Items',
    noPackagesAvailable: 'No packages available for this service',
    uploadImages: 'Upload Images',
    
    // Package-only pricing translations
    packageOnlyNote: 'The price and package should be detailed out in next step: packages',
    basicPricingOption: 'Basic Pricing',
    packageOnlyOption: 'Package Only Pricing',
    ageBasedOption: 'Age Based Pricing',
    packageAndAgeOption: 'Package and Age Based Pricing',
  },
  ms: {
    // Navigation
    boats: 'Bot',
    features: 'Fitur',
    howItWorks: 'Cara Ia Berfungsi',
    services: 'Perkhidmatan',
    about: 'Tentang',
    contact: 'Hubungi Kami',
    signIn: 'Log Masuk',
    signUp: 'Daftar',
    success: 'Berjaya',
    getStarted: 'Mula Sekarang',
    dashboard: 'Papan Pemuka',
    boatOwner: 'Pemilik Bot',
    agent: 'Ejen',
    businessOptions: 'Pilihan Perniagaan',
    
    // Hero Section
    heroTitle: 'Aplikasi Utama Anda ke',
    heroSubtitle: 'Pengembaraan Laut Malaysia',
    heroDescription: 'Temui lokasi snorkeling yang menakjubkan, tempah pakej lawatan lompat pulau, dan nikmati pengangkutan penumpang yang lancar merentasi pulau-pulau tercantik di Malaysia. Pengembaraan bot sempurna anda bermula di sini.',
    bookAdventure: '🏊‍♂️ Tempah Pengembaraan',
    seeHowItWorks: '🎥 Lihat Cara Kerja',
    
    // Trust Indicators
    verifiedOperators: 'Pengendali Bot Disahkan',
    secureBooking: 'Tempahan Dalam Talian Selamat',
    customerSupport: 'Sokongan Pelanggan 24/7',
    
    // Features Section
    perfectForEveryone: 'Sesuai untuk Semua',
    featuresDescription: 'Sama ada anda mencari pengalaman penuh pengembaraan, mengusahakan perniagaan bot, atau ingin menjana pendapatan melalui kerjasama, GoSea menawarkan penyelesaian terbaik untuk anda.',

    // Top Providers Section
    topBoatProviders: 'Penyedia Bot Terbaik',
    trustedProvidersDescription: 'Temui pengendali bot yang paling dipercayai dan berpengalaman yang menawarkan pengembaraan marin yang luar biasa.',
    viewServices: 'Lihat Perkhidmatan',
    
    // Customer Section
    adventureSeekers: 'Untuk Pencari Pengembaraan',
    customerDescription: 'Temui tempat snorkeling yang menakjubkan, tempah lawatan melompat pulau, dan nikmati pengangkutan penumpang yang selamat merentasi pulau-pulau indah Malaysia.',
    browseOperators: 'Layari pengendali bot yang disahkan',
    realTimeAvailability: 'Ketersediaan & harga masa nyata',
    securePayment: 'Tempahan dalam talian selamat',
    support247: 'Sokongan pelanggan 24/7',
    startAdventure: 'Mula mencari bot',

    // Pricing
    pricingPackages: 'Harga Pakej',
    pricing: 'Harga',
    includes: 'Disediakan',
    
    // Boat Owner Section
    boatOwners: 'Untuk Pemilik Bot',
    ownerDescription: 'Senaraikan bot anda, urus tempahan, dan kembangkan perniagaan marin anda dengan platform komprehensif kami yang direka untuk pengendali bot.',
    easyListing: 'Penyenaraian & pengurusan bot mudah',
    automatedBooking: 'Sistem tempahan automatik',
    revenueTracking: 'Penjejakan hasil & analitik',
    marketingTools: 'Alat pemasaran & promosi',
    listYourBoat: 'Senaraikan Bot Anda',
    
    // Affiliate Section
    affiliatePartners: 'Untuk Rakan Kongsi Ahli Gabungan',
    affiliateDescription: 'Peroleh komisen dengan mempromosikan pengalaman bot kepada rangkaian anda. Sempurna untuk agensi pelancongan, pemandu pelancong, dan perniagaan hospitaliti.',
    competitiveRates: 'Kadar komisen yang kompetitif',
    marketingMaterials: 'Bahan pemasaran disediakan',
    earningsTracking: 'Penjejakan pendapatan masa nyata',
    partnerSupport: 'Sokongan rakan kongsi khusus',
    becomePartner: 'Jadi Rakan Kongsi',
    
    // How It Works
    howItWorksTitle: 'Cara GoSea Berfungsi',
    howItWorksDescription: 'Memulakan pengembaraan bot anda adalah mudah dan selamat',
    browseSearch: 'Layari & Cari',
    browseDescription: 'Terokai pilihan bot dan pengalaman yang dikurasi merentasi pulau-pulau Malaysia',
    bookSecurely: 'Tempah dengan Selamat',
    bookDescription: 'Pilih tarikh, masa, dan pakej pilihan anda. Lengkapkan tempahan dengan pembayaran dalam talian yang selamat',
    getConfirmed: 'Dapatkan Pengesahan',
    confirmDescription: 'Terima pengesahan segera dengan maklumat perjalanan terperinci dan butiran hubungan pengendali',
    enjoyAdventure: 'Nikmati Pengembaraan',
    enjoyDescription: 'Temui pengendali anda di lokasi yang ditetapkan dan nikmati pengalaman bot yang menakjubkan',
    
    // CTA Section
    readyToStart: 'Bersedia untuk Memulakan Pengembaraan Pulau Anda?',
    ctaDescription: 'Sertai ribuan pelanggan yang berpuas hati yang telah menemui permata tersembunyi Malaysia dengan GoSea',
    bookFirstTrip: 'Tempah Perjalanan Pertama',
    
    // Footer
    footerDescription: 'Platform utama anda untuk meneroka dan menempah pengalaman bot yang menakjubkan di laut-laut indah seluruh Malaysia.',
    quickLinks: 'Pautan Pantas',
    support: 'Sokongan',
    helpCenter: 'Pusat Bantuan',
    safetyGuidelines: 'Garis Panduan Keselamatan',
    termsOfService: 'Terma Perkhidmatan',
    privacyPolicy: 'Dasar Privasi',
    allRightsReserved: 'Hak cipta terpelihara. Dibina untuk pengembaraan laut Malaysia.',

    // Boat Owner Page
    backToHome: 'Kembali ke Laman Utama',
    boatOwnerWelcome: 'Selamat Datang Pemilik Bot',
    boatOwnerDescription: 'Sertai platform kami dan mula menjana pendapatan dengan menyewakan bot anda kepada pencari pengembaraan di seluruh Malaysia.',
    signUpAsBoatOwner: 'Daftar sebagai Pemilik Bot',
    signInAsBoatOwner: 'Log Masuk sebagai Pemilik Bot',
    continueWithGoogle: 'Teruskan dengan Google',
    continueWithEmail: 'Teruskan dengan E-mel',
    cancel: 'Batal',
    earnIncome: 'Jana Pendapatan',
    earnIncomeDescription: 'Hasilkan pendapatan dengan menyenaraikan bot anda di platform kami.',
    easyManagement: 'Pengurusan Mudah',
    easyManagementDescription: 'Urus tempahan dan ketersediaan dengan papan pemuka intuitif kami.',
    securePayments: 'Pembayaran Selamat',
    securePaymentsDescription: 'Terima pembayaran dengan selamat melalui sistem pembayaran terpercaya kami.',

    // Boat Owner Authentication
    boatOwnerSignIn: 'Log Masuk Pemilik Bot',
    newToGoSea: 'Baharu di GoSea?',
    dontHaveBusinessAccount: 'Belum ada akaun perniagaan?',
    registerBusiness: 'Daftar Perniagaan',
    signInBoatOwner: 'Log Masuk',
    registerBusinessAccount: 'Daftar Perniagaan',
    
    // Boat Owner Registration
    boatOwnerRegistration: 'Pendaftaran Pemilik Bot',
    registerAsBoatOwner: 'Daftar sebagai pemilik bot di Platform GoSea',
    joinGoSeaPlatform: 'Sertai Platform GoSea dan mula menawarkan perkhidmatan marin anda kepada pelanggan',
    businessInformation: 'Maklumat Perniagaan',
    requiredFields: '* Medan wajib diisi',
    businessImages: 'Gambar Syarikat',
    personalInformation: 'Maklumat Peribadi',
    contactInformation: 'Maklumat Hubungan',
    accountSecurity: 'Keselamatan Akaun',
    companyName: 'Nama Syarikat',
    enterCompanyName: 'Masukkan nama syarikat atau perniagaan anda',
    businessRegistrationNumber: 'Nombor Pendaftaran Perniagaan (BRN)',
    enterBRN: 'cth: ************',
    businessEmailAddress: 'Alamat E-mel Perniagaan',
    enterBusinessEmail: '<EMAIL>',
    enterPhoneNumber: '+60*********',
    createStrongPassword: 'Cipta kata laluan yang kuat',
    confirmYourPassword: 'Sahkan kata laluan anda',
    passwordRequirements: 'Keperluan Kata Laluan',
    passwordMinLength: 'Sekurang-kurangnya 8 aksara',
    passwordUppercase: 'Mengandungi huruf besar dan kecil',
    passwordLowercase: 'Mengandungi sekurang-kurangnya satu huruf kecil',
    passwordNumber: 'Mengandungi sekurang-kurangnya satu nombor',
    passwordSpecialChar: 'Mengandungi sekurang-kurangnya satu aksara khas',
    passwordUppercaseSimple: 'Satu huruf besar',
    passwordSpecialCharSimple: 'Satu aksara khas',
    passwordMinLengthError: 'Kata laluan mestilah sekurang-kurangnya 8 aksara',
    passwordRequirementsError: 'Kata laluan mestilah mengandungi sekurang-kurangnya satu huruf besar dan satu aksara khas',
    createBoatOwnerAccount: 'Cipta Akaun Pemilik Bot',
    signInHere: 'log masuk di sini',
    whatHappensAfterRegistration: 'Apa yang berlaku selepas pendaftaran?',
    nextSteps: 'Langkah Seterusnya',
    checkEmailVerification: 'Semak e-mel anda untuk pautan pengesahan',
    clickVerificationLink: 'Klik pautan pengesahan untuk mengesahkan e-mel anda',
    adminReviewApplication: 'Pasukan pentadbir kami akan menyemak permohonan anda',
    receiveApprovalNotification: 'Anda akan menerima pemberitahuan kelulusan dalam masa 1-2 hari bekerja',
    onceApprovedAccess: 'Sebaik sahaja diluluskan, anda boleh log masuk dan mula menyenaraikan bot dan perkhidmatan anda.',
    returnToHomepage: 'Kembali ke Laman Utama',
    learnMoreBoatOwnerFeatures: 'Ketahui Lebih Lanjut Mengenai Ciri-ciri Pemilik Bot',
    emailVerification: 'Pengesahan E-mel',
    checkInboxVerifyEmail: 'Semak peti masuk anda dan sahkan alamat e-mel anda',
    adminReview: 'Semakan Pentadbir',
    teamReviewsApplication: 'Pasukan kami menyemak permohonan perniagaan anda',
    startListing: 'Mula Menyenaraikan',
    addBoatsServices: 'Tambah bot dan perkhidmatan anda ke platform',
    creatingAccount: 'Sedang Mencipta Akaun Anda...',
    fixPasswordRequirements: 'Sila betulkan keperluan kata laluan:',
    passwordsDoNotMatch: 'Kata laluan tidak sepadan',
    invalidPhoneNumberFormat: 'Sila berikan nombor telefon Malaysia yang sah (cth: +60*********, 0*********)',
    accountAlreadyExists: 'Akaun dengan alamat e-mel ini sudah wujud. Sila gunakan e-mel yang berbeza atau log masuk sahaja.',
    registrationFailed: 'Pendaftaran gagal. Sila cuba lagi.',
    networkError: 'Ralat rangkaian. Sila semak sambungan anda dan cuba lagi.',
    registrationSuccessful: 'Pendaftaran Berjaya!',
    emailVerified: 'E-mel Disahkan!',
    emailVerificationSuccessful: 'E-mel perniagaan anda telah berjaya disahkan! Pasukan pentadbir kami akan menyemak permohonan anda sebentar lagi.',
    boatOwnerRegistrationSuccess: 'Pendaftaran pemilik bot anda berjaya',
    thankYouForRegistering: 'Terima kasih kerana mendaftar sebagai pemilik bot dengan GoSea',
    backToHomepage: 'Kembali ke Laman Utama',
    malaysiaPhoneFormat: 'Format nombor telefon Malaysia',
    
    // Additional Boat Owner Registration Translations
    operatorType: 'Jenis Pengendali',
    individualOperator: 'Pengendali Individu',
    individualOperatorDescription: 'Beroperasi sebagai individu tanpa perniagaan berdaftar',
    businessOperator: 'Pengendali Perniagaan',
    businessOperatorDescription: 'Beroperasi sebagai entiti perniagaan berdaftar',
    companyNameRequired: 'Nama syarikat diperlukan untuk pengendali perniagaan',
    brnRequired: 'Nombor Pendaftaran Perniagaan diperlukan untuk pengendali perniagaan',
    
    // Admin Dashboard
    boatOwnerApprovedSuccessfully: 'Pemilik bot diluluskan dengan jayanya',
    failedToApproveBoatOwner: 'Gagal meluluskan pemilik bot',
    boatOwnerRejectedSuccessfully: 'Pemilik bot ditolak dengan jayanya',
    failedToRejectBoatOwner: 'Gagal menolak pemilik bot',
    owner: 'Pemilik',
    created: 'Dicipta',
    activated: 'diaktifkan',
    deactivated: 'dinyahaktifkan',
    successfully: 'dengan jayanya',
    failedToUpdate: 'Gagal mengemas kini',
    userStatus: 'status pengguna',
    userRole: 'peranan pengguna',
    updated: 'dikemas kini',
    providerVerification: 'pengesahan penyedia',
    boatStatus: 'status bot',
    
    // Agent Page
    agentWelcome: 'Selamat Datang Ejen Gabungan',
    agentDescription: 'Bekerjasama dengan kami dan peroleh komisen dengan mempromosikan penyewaan bot kepada rangkaian anda.',
    signUpAsAgent: 'Daftar sebagai Ejen',
    signInAsAgent: 'Log Masuk sebagai Ejen',
    earnCommission: 'Peroleh Komisen',
    earnCommissionDescription: 'Dapatkan komisen 10% untuk setiap tempahan berjaya yang anda rujuk.',
    affiliateLinks: 'Pautan Gabungan',
    affiliateLinksDescription: 'Hasilkan pautan tersuai untuk menjejaki rujukan dan pendapatan anda.',
    trackPerformance: 'Jejak Prestasi',
    trackPerformanceDescription: 'Pantau tempahan dan pendapatan komisen anda dalam masa nyata.',

    // Authentication Modal
    orContinueWith: 'atau teruskan dengan',
    email: 'E-mel',
    password: 'Kata Laluan',
    confirmPassword: 'Sahkan Kata Laluan',
    firstName: 'Nama Pertama',
    lastName: 'Nama Akhir',
    phoneNumber: 'Nombor Telefon',
    enterEmail: 'Masukkan e-mel anda',
    enterPassword: 'Masukkan kata laluan anda',
    enterFirstName: 'Masukkan nama pertama',
    enterLastName: 'Masukkan nama akhir',
    rememberMe: 'Ingat saya',
    forgotPassword: 'Lupa kata laluan?',
    signingIn: 'Sedang log masuk...',
    creatingAccount: 'Sedang mendaftar akaun...',
    createAccount: 'Daftar Akaun',
    dontHaveAccount: 'Belum ada akaun?',
    alreadyHaveAccount: 'Sudah ada akaun?',

    // Profile Completion
    editProfile: 'Edit Profil',
    userProfile: 'Profil Pengguna',
    welcomeToGoSea: 'Selamat Datang ke GoSea!',
    profileCompletionOptional: 'Anda boleh melengkapkan profil anda kemudian dari dashboard. Teruskan dengan tempahan anda sekarang.',
    'Please complete your profile information to continue with your booking.': 'Sila lengkapkan maklumat profil anda untuk meneruskan tempahan.',
    'Complete Profile': 'Lengkapkan Profil',
    'Complete Your Profile': 'Lengkapkan Profil Anda',
    'Phone Number': 'Nombor Telefon',
    'Date of Birth': 'Tarikh Lahir',
    'Address': 'Alamat',
    'Emergency Contact': 'Hubungan Kecemasan',
    'Optional': 'Pilihan',
    'Phone number is required': 'Nombor telefon diperlukan',
    'Please enter a valid Malaysian phone number (e.g., +60*********, 0*********, or *********)': 'Sila masukkan nombor telefon Malaysia yang sah (cth: +60*********, 0*********, atau *********)',
    'e.g., +60*********, 0*********, or *********': 'cth: +60*********, 0*********, atau *********',
    'Please enter a valid date of birth': 'Sila masukkan tarikh lahir yang sah',
    'Select your date of birth': 'Pilih tarikh lahir anda',
    'Complete Profile': 'Lengkapkan Profil',
    'Skip for Now': 'Langkau Buat Masa Ini',
    'Calendar': 'Kalendar',
    'Type Date': 'Taip Tarikh',
    'Enter Date Manually': 'Masukkan Tarikh Secara Manual',
    'Supported formats: DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD': 'Format yang disokong: DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD',
    'Apply': 'Guna',
    'Cancel': 'Batal',
    'Please enter a valid date of birth (age must be between 13 and 120)': 'Sila masukkan tarikh lahir yang sah (umur mestilah antara 13 dan 120)',
    'Please enter a valid date format (DD/MM/YYYY, DD-MM-YYYY, or YYYY-MM-DD)': 'Sila masukkan format tarikh yang sah (DD/MM/YYYY, DD-MM-YYYY, atau YYYY-MM-DD)',
    'Address Information': 'Maklumat Alamat',
    'Street Address Line 1': 'Alamat Jalan Baris 1',
    'Street Address Line 2': 'Alamat Jalan Baris 2',
    'e.g., 123 Jalan Bukit Bintang': 'cth: 123 Jalan Bukit Bintang',
    'e.g., Apartment, suite, unit, building, floor, etc.': 'cth: Apartmen, suite, unit, bangunan, tingkat, dll.',
    'Postcode': 'Poskod',
    'e.g., 55100': 'cth: 55100',
    'City': 'Bandar',
    'e.g., Kuala Lumpur': 'cth: Kuala Lumpur',
    'State': 'Negeri',
    'Select State': 'Pilih Negeri',
    'Emergency Contact Phone': 'Telefon Kenalan Kecemasan',
    'Please enter a valid Malaysian phone number for emergency contact (e.g., +60*********, 0*********, or *********)': 'Sila masukkan nombor telefon Malaysia yang sah untuk kenalan kecemasan (cth: +60*********, 0*********, atau *********)',

    // Dashboard
    home: 'Laman Utama',
    profile: 'Profil',
    settings: 'Tetapan',
    logout: 'Log Keluar',
    welcomeBack: 'Selamat kembali',
    dashboardSubtitle: 'Bersedia untuk meneroka lautan? Pengembaraan maritim anda berterusan di sini. Urus tempahan anda, temui perkhidmatan baharu, dan terjun ke pengalaman lautan seterusnya.',
    account: 'Akaun',
    customer: 'Pelanggan',
    boatowner: 'Pemilik Bot',
    affiliateagent: 'Ejen Gabungan',
    admin: 'Pentadbir',

    // Dashboard Cards
    accountInformation: 'Maklumat Akaun',
    fullName: 'Nama Penuh',
    emailAddress: 'Alamat E-mel',
    accountStatus: 'Status Akaun',
    role: 'Peranan',
    verificationStatus: 'Status Pengesahan',
    verified: 'Disahkan',
    pendingVerification: 'Menunggu Pengesahan',
    quickActions: 'Tindakan Pantas',

    // Admin Dashboard
    contactPhone: 'Telefon No.',
    registrationDate: 'Tarikh Pendaftaran',
    emailVerificationStatus: 'Status Pengesahan E-mel',
    adminDashboard: 'Dashboard Pentadbir',
    manageUsersProvidersBoats: 'Urus pengguna, penyedia, dan bot di seluruh platform GoSea',
    overview: 'Gambaran Keseluruhan',
    users: 'Pengguna',
    providers: 'Penyedia',
    boats: 'Bot',
    totalUsers: 'Jumlah Pengguna',
    totalProviders: 'Jumlah Penyedia',
    totalBoats: 'Jumlah Bot',
    totalBookings: 'Jumlah Tempahan',
    active: 'Aktif',
    inactive: 'Tidak Aktif',
    approved: 'Diluluskan',
    pending: 'Menunggu',
    verified: 'Disahkan',
    unverified: 'Belum Disahkan',
    inLast7Days: 'dalam 7 hari terakhir',
    allRoles: 'Semua Peranan',
    allStatus: 'Semua Status',
    allVerificationStatus: 'Semua Status Pengesahan',
    allActiveStatus: 'Semua Status Aktif',
    search: 'Cari',
    searchByEmail: 'Cari mengikut e-mel atau nama...',
    searchByCompany: 'Cari mengikut nama syarikat atau e-mel...',
    searchByBoatName: 'Cari mengikut nama bot atau pendaftaran...',
    searchByNameOrEmail: 'Cari mengikut nama atau e-mel...',
    user: 'Pengguna',
    boatOwnerApprovals: 'Kelulusan Pemilik Bot',
    managePendingBoatOwnerApplications: 'Urus permohonan pemilik bot yang menunggu',
    companyInformation: 'Maklumat Syarikat',
    registrationDetails: 'Butiran Pendaftaran',
    noPendingApprovals: 'Tiada Kelulusan Menunggu',
    allBoatOwnerApplicationsProcessed: 'Semua permohonan pemilik bot telah diproses',
    emailPending: 'E-mel Menunggu',
    nameNotProvided: 'Nama Tidak Disediakan',
    allDates: 'Semua Tarikh',
    today: 'Hari Ini',
    thisWeek: 'Minggu Ini',
    thisMonth: 'Bulan Ini',
    verification: 'Pengesahan',
    status: 'Status',
    joined: 'Menyertai',
    actions: 'Tindakan',
    activate: 'Aktifkan',
    deactivate: 'Nyahaktifkan',
    verify: 'Sahkan',
    unverify: 'Nyahsah',

    // Boat Owner Module
    providerStatus: 'Status Penyedia',
    approvedOn: 'Diluluskan Pada',
    businessAddress: 'Alamat Perniagaan',
    licenseExpiryDate: 'Tarikh Luput Lesen',
    websiteUrl: 'URL Laman Web',
    businessPhone: 'Telefon Perniagaan',
    businessEmail: 'E-mel Perniagaan',
    operatingLicense: 'Lesen Operasi',
    displayName: 'Nama Paparan',
    personalAddress: 'Alamat Peribadi',
    timezone: 'Zon Masa',
    personalPhone: 'Telefon Peribadi',
    boatOwnerModule: 'Modul Pemilik Bot',
    boatOwnerDashboard: 'Dashboard Pemilik Bot',
    businessProfile: 'Profil Perniagaan',
    myBoats: 'Bot Saya',
    myServices: 'Perkhidmatan Saya',
    manageYourBoatBusiness: 'Urus perniagaan bot anda',
    manageYourBusinessProfile: 'Urus profil perniagaan anda',
    manageYourBoatFleet: 'Urus armada bot anda',
    manageYourServices: 'Urus perkhidmatan anda',
    manageYourBookings: 'Urus tempahan anda',
    viewYourBusinessAnalytics: 'Lihat analitik perniagaan anda',
    welcomeBack: 'Selamat Kembali',
    hereIsYourBusinessOverview: 'Berikut adalah gambaran keseluruhan perniagaan anda',
    activeServices: 'Perkhidmatan Aktif',
    recentBookings: 'Tempahan Terkini',
    monthlyRevenue: 'Pendapatan Bulanan',
    fromLastMonth: 'dari bulan lalu',
    quickActions: 'Tindakan Pantas',
    addNewBoat: 'Tambah Bot',
    addBoatToFleet: 'Tambah bot ke armada anda',
    createService: 'Cipta Perkhidmatan',
    createNewService: 'Cipta perkhidmatan baru',
    viewBookings: 'Lihat Tempahan',
    manageBookings: 'Urus tempahan',
    viewAnalytics: 'Lihat Analitik',
    businessInsights: 'Wawasan perniagaan',
    recentActivity: 'Aktiviti Terkini',
    noRecentActivity: 'Tiada aktiviti terkini',
    activityWillAppearHere: 'Aktiviti akan muncul di sini',

    // Profile Management
    editBusinessProfile: 'Edit Profil Perniagaan',
    updateYourBusinessInformation: 'Kemas kini maklumat perniagaan anda',
    profileOverview: 'Gambaran Profil',
    businessInformation: 'Maklumat Perniagaan',
    businessStatistics: 'Statistik Perniagaan',
    businessDocuments: 'Dokumen Perniagaan',
    memberSince: 'Ahli sejak',
    notProvided: 'Tidak disediakan',
    noDescriptionProvided: 'Tiada penerangan disediakan',
    noDocumentsUploaded: 'Tiada dokumen dimuat naik',
    uploadDocuments: 'Muat naik dokumen',
    uploadedOn: 'Dimuat naik pada',
    personalInformation: 'Maklumat Peribadi',
    businessDescription: 'Penerangan Perniagaan',
    describeYourBusiness: 'Terangkan perniagaan anda',
    profile: 'Profil',
    notAvailable: 'Tidak tersedia',
    english: 'English',
    bahasaMalaysia: 'Bahasa Malaysia',
    businessLogo: 'Logo Perniagaan',
    coverImage: 'Imej Muka Depan',
    logo: 'Logo',
    uploadLogo: 'Muat Naik Logo',
    uploadCoverImage: 'Muat Naik Imej Muka Depan',
    invalidImageFormat: 'Sila pilih fail imej yang sah (JPG, PNG, WebP)',
    imageSizeLimit: 'Saiz imej mestilah kurang daripada 5MB',
    failedToUploadLogo: 'Gagal memuat naik logo',
    failedToUploadCoverImage: 'Gagal memuat naik imej muka depan',
    asiaKualaLumpur: 'Asia/Kuala_Lumpur',
    asiaKuching: 'Asia/Kuching',
    asiaSingapore: 'Asia/Singapore',
    malaysiaPhoneNumberPlaceholder: '+60*********',
    websiteUrlPlaceholder: 'https://www.contoh.com',

    // Boat Management
    descriptionRequired: 'Penerangan diperlukan',
    addNewBoatToYourFleet: 'Tambah bot baru ke armada anda',
    createBoat: 'Cipta Bot',
    creating: 'Mencipta',
    boatCreatedSuccessfully: 'Bot berjaya dicipta',
    failedToCreateBoat: 'Gagal mencipta bot',
    boatName: 'Nama Bot',
    boatNameRequired: 'Nama bot diperlukan',
    enterBoatName: 'Masukkan nama bot',
    capacity: 'Kapasiti',
    seats: 'Tempat Duduk',
    validCapacityRequired: 'Kapasiti yang sah diperlukan',
    maxPassengers: 'Penumpang maksimum',
    registrationNumber: 'Nombor Pendaftaran',
    registrationNumberRequired: 'Nombor pendaftaran diperlukan',
    enterRegistrationNumber: 'Masukkan nombor pendaftaran',
    location: 'Lokasi',
    locationRequired: 'Lokasi diperlukan',
    boatLocation: 'Lokasi bot',
    yearBuilt: 'Tahun Dibina',
    enterYearBuilt: 'Masukkan tahun dibina',
    serviceType: 'Jenis Perkhidmatan',
    selectServiceType: 'Pilih jenis perkhidmatan',
    describeYourBoat: 'Terangkan bot anda',
    boatImages: 'Gambar Bot',
    clickToUploadImages: 'Klik untuk muat naik gambar',
    maxImagesAllowed: 'Maksimum {{max}} gambar dibenarkan',
    imageLimitExceeded: 'Anda hanya boleh muat naik maksimum {{max}} gambar. Sila buang beberapa gambar sebelum menambah lagi.',
    technicalSpecifications: 'Spesifikasi Teknikal',
    length: 'Panjang',
    boatLength: 'Panjang bot',
    engineType: 'Jenis Enjin',
    enginePower: 'Kuasa Enjin',
    safetyRating: 'Penarafan Keselamatan',
    safetyRatingA: 'A - Sangat Selamat',
    safetyRatingB: 'B - Selamat',
    safetyRatingC: 'C - Sederhana',
    material: 'Bahan',
    boatMaterial: 'Bahan bot',
    amenities: 'Kemudahan',
    airConditioning: 'Penyaman Udara',
    wifi: 'WiFi',
    restroom: 'Bilik Air',
    kitchen: 'Dapur',
    soundSystem: 'Sistem Bunyi',
    fishingEquipment: 'Peralatan Memancing',
    snorkelingGear: 'Peralatan Snorkeling',
    lifeJackets: 'Jaket Keselamatan',
    includedItems: 'Item Termasuk',
    refreshments: 'Minuman Ringan',
    towels: 'Tuala',
    sunscreen: 'Pelindung Matahari',
    firstAidKit: 'Kit Pertolongan Cemas',
    waterSports: 'Sukan Air',
    approvedBy: 'Diluluskan Oleh',
    approvedAt: 'Diluluskan Pada',
    rejectionReason: 'Sebab Penolakan',
    basePrice: 'Harga Asas',
    basePriceRequired: 'Harga asas diperlukan',
    // Add routes translations
    routes: 'Laluan',
    searchRoutes: 'Cari Laluan',
    searchForRoutes: 'Cari laluan...',
    availableRoutes: 'Laluan Tersedia',
    selectedRoutes: 'Laluan Dipilih',
    noRoutesFound: 'Tiada laluan dijumpai',
    startTypingToSearchRoutes: 'Mula menaip untuk mencari laluan...',
    noRoutesSelected: 'Klik "Cari Laluan" untuk memilih destinasi bagi perkhidmatan anda',
    // Boat Details
    boatDetails: 'Butiran Bot',
    boatNotFound: 'Bot tidak dijumpai',
    backToBoats: 'Kembali ke Bot',
    failedToLoadBoat: 'Gagal memuatkan bot',
    passengers: 'penumpang',
    edit: 'Edit',
    delete: 'Padam',
    noImagesAvailable: 'Tiada gambar tersedia',
    description: 'Penerangan',
    noDescriptionAvailable: 'Tiada penerangan tersedia',
    basicInformation: 'Maklumat Asas',
    assignedServices: 'Perkhidmatan Ditugaskan',
    noServicesAssigned: 'Tiada perkhidmatan ditugaskan',
    view: 'Lihat',
    confirmDeleteBoat: 'Adakah anda pasti mahu memadam bot ini?',
    confirmDeleteBoatTitle: 'Sahkan Pemadaman Bot',
    confirmDeleteBoatMessage: 'Adakah anda pasti mahu memadam bot ini? Tindakan ini tidak boleh dibuat asal.',
    failedToDeleteBoat: 'Gagal memadam bot',

    // Boat List
    searchBoats: 'Cari bot',
    allStatuses: 'Semua Status',
    maintenance: 'Penyelenggaraan',
    services: 'perkhidmatan',
    noBoatsFound: 'Tiada bot dijumpai',
    noBoatsMatchFilters: 'Tiada bot sepadan dengan penapis',
    startByAddingYourFirstBoat: 'Mulakan dengan menambah bot pertama anda',
    addYourFirstBoat: 'Tambah Bot Pertama Anda',

    // Service Management
    createYourFirstService: 'Cipta Perkhidmatan Pertama Anda',
    searchServices: 'Cari perkhidmatan',
    allCategories: 'Semua Kategori',
    draft: 'Draf',
    dayTrip: 'Perjalanan Harian',
    snorkeling: 'Snorkeling',
    fishing: 'Memancing',
    islandHopping: 'Melawat Pulau',
    sunset: 'Matahari Terbenam',
    diving: 'Menyelam',
    watersports: 'Sukan Air',
    charter: 'Carter',
    max: 'maks',
    routes: 'laluan',
    assignedBoats: 'Bot Ditugaskan',
    more: 'lagi',
    noServicesFound: 'Tiada perkhidmatan dijumpai',
    noServicesMatchFilters: 'Tiada perkhidmatan sepadan dengan penapis',
    startByCreatingYourFirstService: 'Mulakan dengan mencipta perkhidmatan pertama anda',
    confirmDeleteService: 'Adakah anda pasti mahu memadam perkhidmatan ini?',
    failedToDeleteService: 'Gagal memadam perkhidmatan',
    category: 'Kategori',

    // Bookings Management
    bookings: 'Tempahan',
    confirmed: 'Disahkan',
    cancelled: 'Dibatalkan',
    completed: 'Selesai',
    allServices: 'Semua Perkhidmatan',
    dateFrom: 'Tarikh Dari',
    dateTo: 'Tarikh Hingga',
    filter: 'Tapis',
    confirm: 'Sahkan',
    cancel: 'Batal',
    markCompleted: 'Tandakan Selesai',
    bookingStatusUpdated: 'Status tempahan dikemas kini',
    failedToUpdateBookingStatus: 'Gagal mengemas kini status tempahan',
    noBookingsFound: 'Tiada tempahan dijumpai',
    noBookingsMatchFilters: 'Tiada tempahan sepadan dengan penapis',
    bookingsWillAppearHere: 'Tempahan akan muncul di sini',
    manageServices: 'Urus Perkhidmatan',

    // Analytics
    analytics: 'Analitik',
    last7Days: '7 Hari Terakhir',
    last30Days: '30 Hari Terakhir',
    last90Days: '90 Hari Terakhir',
    lastYear: 'Tahun Lepas',
    confirmedBookings: 'Tempahan Disahkan',
    totalRevenue: 'Jumlah Pendapatan',
    conversionRate: 'Kadar Penukaran',
    bookingStatusBreakdown: 'Pecahan Status Tempahan',
    performanceSummary: 'Ringkasan Prestasi',
    bookingConversionRate: 'Kadar Penukaran Tempahan',
    averageBookingValue: 'Nilai Purata Tempahan',
    cancellationRate: 'Kadar Pembatalan',
    revenueInsights: 'Wawasan Pendapatan',
    dailyAverageRevenue: 'Purata Pendapatan Harian',
    recommendations: 'Cadangan',
    improveConversionRate: 'Tingkatkan Kadar Penukaran',
    conversionRateRecommendation: 'Kadar penukaran anda di bawah 50%. Cuba tingkatkan kualiti perkhidmatan dan harga yang kompetitif.',
    getYourFirstBooking: 'Dapatkan Tempahan Pertama Anda',
    firstBookingRecommendation: 'Pastikan perkhidmatan anda menarik dan harga berpatutan untuk menarik pelanggan pertama.',
    excellentPerformance: 'Prestasi Cemerlang',
    excellentPerformanceMessage: 'Tahniah! Kadar penukaran anda sangat baik. Teruskan kerja yang baik!',
    noDataAvailable: 'Tiada data tersedia',
    unknown: 'Tidak diketahui',

    // Common Terms
    retry: 'Cuba Lagi',
    saving: 'Menyimpan',
    saveChanges: 'Simpan Perubahan',
    editBoat: 'Edit Bot',
    editYourBoatDetails: 'Edit butiran bot anda',
    boatUpdatedSuccessfully: 'Bot berjaya dikemas kini',
    failedToUpdateBoat: 'Gagal mengemas kini bot',
    serviceName: 'Nama Perkhidmatan',
    serviceNameRequired: 'Nama perkhidmatan diperlukan',
    serviceNameRequired: 'Nama perkhidmatan diperlukan',
    enterServiceName: 'Masukkan nama perkhidmatan',
    categoryRequired: 'Kategori diperlukan',
    selectCategory: 'Pilih kategori',
    hours: 'jam',
    serviceDuration: 'Tempoh perkhidmatan',
    maxCapacityRequired: 'Kapasiti maksimum diperlukan',
    maximumPassengers: 'Penumpang maksimum',
    describeYourService: 'Terangkan perkhidmatan anda',
    pricing: 'Harga',
    adultPrice: 'Harga Dewasa',
    adultPriceRequired: 'Harga dewasa diperlukan',
    childPrice: 'Harga Kanak-kanak',
    infantPrice: 'Harga Bayi',
    zeroValuePricingTitle: 'Sahkan Harga Percuma',
    zeroValuePricingMessage: 'Kategori umur berikut mempunyai nilai 0 dan akan dianggap sebagai PERCUMA',
    zeroValuePricingBasicMessage: 'Harga setiap orang ditetapkan kepada 0 dan akan dianggap sebagai PERCUMA. Adakah anda ingin meneruskan?',
    zeroValuePricingConfirm: 'Adakah anda ingin meneruskan?',
    packageTypeRequired: 'Jenis pakej diperlukan',
    packagePriceRequired: 'Harga pakej diperlukan dan mesti lebih besar daripada 0',
    atLeastOneAgePriceRequired: 'Sekurang-kurangnya satu harga kategori umur mesti ditetapkan',
    assignBoats: 'Tugaskan Bot',
    selectAtLeastOneBoat: 'Sila pilih sekurang-kurangnya satu bot',
    noBoatsAvailable: 'Tiada bot tersedia',
    addBoatsFirst: 'Sila tambah bot ke armada anda dahulu',
    createNewServiceForYourBusiness: 'Cipta perkhidmatan baru untuk perniagaan anda',
    serviceCreatedSuccessfully: 'Perkhidmatan berjaya dicipta',
    failedToCreateService: 'Gagal mencipta perkhidmatan',
    basicInformation: 'Maklumat Asas',
    serviceName: 'Nama Perkhidmatan',
    serviceType: 'Jenis Perkhidmatan',
    selectServiceType: 'Pilih jenis perkhidmatan',
    serviceTypeRequired: 'Sila pilih jenis perkhidmatan',
    duration: 'Tempoh',
    minutes: 'minit',
    maxCapacity: 'Kapasiti Maksimum',
    description: 'Penerangan',
    descriptionRequired: 'Penerangan diperlukan',
    basePrice: 'Harga Asas',
    pricingModel: 'Model Harga',
    basicPricing: 'Harga Asas',
    packageOnly: 'Pakej Sahaja',
    ageBased: 'Berdasarkan Umur',
    packageAndAge: 'Pakej dan Berdasarkan Umur',
    inclusionsExclusions: 'Termasuk & Tidak Termasuk',
    includedItems: 'Item Termasuk',
    excludedItems: 'Item Tidak Termasuk',
    enterItem: 'Masukkan item',
    addItem: 'Tambah Item',
    searchInclusions: 'Cari termasuk atau taip untuk tambah kustom...',
    searchExclusions: 'Cari tidak termasuk atau taip untuk tambah kustom...',
    suggestedInclusions: 'Cadangan Termasuk',
    suggestedExclusions: 'Cadangan Tidak Termasuk',
    addCustom: 'Tambah Kustom',
    startTypingToSearch: 'Mula menaip untuk mencari...',
    selectedInclusions: 'Termasuk Dipilih',
    selectedExclusions: 'Tidak Termasuk Dipilih',
    specialInstructions: 'Arahan Khas',
    enterSpecialInstructions: 'Masukkan arahan khas',
    routes: 'Laluan',
    routeAndBoat: 'Laluan & Bot',
    route: 'Laluan',
    selectRoute: 'Pilih laluan',
    priceModifier: 'Pengubahsuai Harga',
    removeRoute: 'Buang Laluan',
    addRoute: 'Tambah Laluan',
    duplicateRouteNotAllowed: 'Laluan pendua tidak dibenarkan',
    boatAssignments: 'Tugasan Bot',
    boat: 'Bot',
    selectBoat: 'Pilih bot',
    primaryBoat: 'Bot Utama',
    capacityOverride: 'Mengatasi Kapasiti',
    leaveEmptyForFullCapacity: 'Biarkan kosong untuk kapasiti penuh',
    removeBoat: 'Buang Bot',
    addBoat: 'Tambah Bot',
    duplicateBoatNotAllowed: 'Bot pendua tidak dibenarkan',
    packages: 'Pakej',
    packageType: 'Jenis Pakej',
    selectPackageType: 'Pilih jenis pakej',
    ageBasedPricing: 'Harga Berdasarkan Umur',
    removeAgeCategory: 'Buang Kategori Umur',
    ageCategoryRemovalNote: 'Padam kategori umur yang tidak berkaitan dengan menggunakan butang -',
    removePackage: 'Buang Pakej',
    addPackage: 'Tambah Pakej',
    schedules: 'Jadual',
    dayOfWeek: 'Hari dalam Minggu',
    daily: 'Harian',
    sunday: 'Ahad',
    monday: 'Isnin',
    tuesday: 'Selasa',
    wednesday: 'Rabu',
    thursday: 'Khamis',
    friday: 'Jumaat',
    saturday: 'Sabtu',
    departureTime: 'Masa Berlepas',
    availableCapacity: 'Kapasiti Tersedia',
    removeSchedule: 'Buang Jadual',
    addSchedule: 'Tambah Jadual',
    
    // Itinerary
    itinerary: 'Itinerari',
    optional: 'Pilihan',
    itineraryDescription: 'Cipta jadual terperinci untuk perkhidmatan anda supaya pelanggan memahami apa yang dapat dijangkakan semasa perjalanan mereka.',
    noItineraryItems: 'Tiada item jadual ditambah lagi. Klik "Tambah Item Jadual" untuk memulakan.',
    time: 'Masa',
    activity: 'Aktiviti',
    location: 'Lokasi',
    activityPlaceholder: 'Snorkeling di Coral Point',
    locationPlaceholder: 'Coral Point',
    removeItineraryItem: 'Buang Item Jadual',
    addItineraryItem: 'Tambah Item Jadual',
    moveItemUp: 'Alih Item Ke Atas',
    moveItemDown: 'Alih Item Ke Bawah',
    reorderItineraryItems: 'Susun Semula Item Jadual',
    duplicateTimeError: 'Masa ini sudah digunakan dalam item jadual lain. Sila pilih masa yang berbeza.',
    
    // Images
    images: 'Imej',
    uploadImages: 'Muat Naik Imej',
    clickToUpload: 'Klik untuk muat naik',
    orDragAndDrop: 'atau seret dan lepaskan',
    imageRequirements: 'PNG, JPG, GIF sehingga 10MB',
    maxImagesAllowed: 'Maksimum {max} imej dibenarkan',
    draft: 'Draf',
    pendingApproval: 'Menunggu Kelulusan',
    approved: 'Diluluskan',
    rejected: 'Ditolak',
    allStatuses: 'Semua Status',
    regNo: 'No Daftar',
    previous: 'Sebelumnya',
    next: 'Seterusnya',
    provider: 'Penyedia',
    owner: 'Pemilik',
    services: 'Perkhidmatan',
    created: 'Dicipta',
    boat: 'Bot',
    capacity: 'Kapasiti',
    people: 'orang',
    noRegistration: 'Tiada pendaftaran',
    noProvider: 'Tiada penyedia',
    draft: 'Draf',
    pendingApproval: 'Menunggu Kelulusan',
    rejected: 'Ditolak',
    maintenance: 'Penyelenggaraan',
    customer: 'Pelanggan',
    boatOwner: 'Pemilik Bot',
    affiliateAgent: 'Ejen Gabungan',
    loadingAdminDashboard: 'Memuatkan dashboard pentadbir...',
    showing: 'Menunjukkan',
    to: 'ke',
    of: 'daripada',
    results: 'keputusan',
    previous: 'Sebelumnya',
    next: 'Seterusnya',
    servicesCount: 'perkhidmatan',
    boatsCount: 'bot',
    allActiveStatus: 'Semua Status Aktif',
    activeStatus: 'Status Aktif',
    createService: 'Cipta Perkhidmatan',
    creating: 'Mencipta...',
    step1: 'Maklumat Asas',
    step2: 'Laluan',
    step3: 'Bot',
    step4: 'Pakej',
    step5: 'Harga',
    step6: 'Termasuk',
    step7: 'Jadual',
    routeRequired: 'Laluan diperlukan',
    boatRequired: 'Bot diperlukan',

    // Expandable Account Information
    phoneNumber: 'Nombor Telefon',
    dateOfBirth: 'Tarikh Lahir',
    address: 'Alamat',
    emergencyContact: 'Nombor Telefon Kecemasan',
    companyName: 'Nama Syarikat',
    businessRegistrationNumber: 'Nombor Pendaftaran Perniagaan',
    agencyName: 'Nama Agensi',
    memberSince: 'Ahli Sejak',
    lastLogin: 'Log Masuk Terakhir',
    viewMore: 'Lihat Lagi',
    viewLess: 'Lihat Kurang',
    completeYourProfile: 'Lengkapkan Profil Anda',
    addMoreInfoToProfile: 'Tambah maklumat lanjut pada profil anda untuk pengalaman yang lebih baik',
    updateProfile: 'Kemas Kini Profil',
    viewProfile: 'Lihat Profil',
    profile: 'Profil',
    manageYourProfileInformation: 'Urus maklumat profil anda',
    basicInformation: 'Maklumat Asas',
    accountInformation: 'Maklumat Akaun',
    businessInformation: 'Maklumat Perniagaan',
    agencyInformation: 'Maklumat Agensi',
    language: 'Bahasa',

    // Profile Edit Page
    editProfile: 'Edit Profil',
    updateYourProfileInformation: 'Kemas kini maklumat profil anda',
    firstName: 'Nama Pertama',
    lastName: 'Nama Akhir',
    enterFirstName: 'Masukkan nama pertama anda',
    enterLastName: 'Masukkan nama akhir anda',
    preferredLanguage: 'Bahasa Pilihan',
    languagePreferenceHelp: 'Pilih bahasa pilihan anda untuk antara muka aplikasi',
    selectDateOfBirth: 'Pilih tarikh lahir anda',
    enterAddressLine1: 'Masukkan alamat baris 1',
    enterAddressLine2: 'Masukkan alamat baris 2',
    enterCity: 'Masukkan bandar',
    enterPostcode: 'Masukkan poskod',
    enterState: 'Masukkan negeri',
    enterCompanyName: 'Masukkan nama syarikat',
    enterDisplayName: 'Masukkan nama paparan',
    enterBRN: 'Masukkan Nombor Pendaftaran Perniagaan',
    enterOperatingLicense: 'Masukkan lesen operasi',
    enterBusinessEmail: 'Masukkan e-mel perniagaan',
    enterAgencyName: 'Masukkan nama agensi',
    selectLicenseExpiryDate: 'Pilih tarikh tamat lesen',
    describeYourBusiness: 'Terangkan perniagaan anda',
    personalAddress: 'Alamat Peribadi',
    businessAddress: 'Alamat Perniagaan',
    websiteUrlPlaceholder: 'Masukkan Url Sistem',

    // Standard Modal
    close: 'Tutup',
    continue: 'Teruskan',
    details: 'Butiran',
    operationSuccessful: 'Operasi Berjaya',
    operationFailed: 'Operasi Gagal',
    successMessage: 'Permintaan anda telah diproses dengan jayanya.',
    errorMessage: 'Ralat berlaku semasa memproses permintaan anda. Sila cuba lagi.',
    tryAgain: 'Cuba Lagi',
    ok: 'OK',
    
    // Forgot Password Modal
    sendResetLink: 'Hantar Pautan Reset',
    backToSignIn: 'Kembali ke Log Masuk',
    sending: 'Menghantar...',

    // Profile Completion Modal
    profileCompletionSuccess: 'Profil anda telah dilengkapkan dengan jayanya. Anda kini boleh mengakses semua fitur.',
    updateProfileError: 'Ralat Kemas Kini Profil',
    profileUpdateFailed: 'Gagal mengemaskini profil. Sila cuba lagi.',

    // Boat Search
    searchBoats: 'Cari Bot',
    findPerfectBoat: 'Cari bot yang sesuai untuk aktiviti anda',
    boatProviders: 'Penyedia Bot',
    searchingBoatProviders: 'Mencari penyedia bot...',
    noBoatProvidersFound: 'Tiada Penyedia Bot Dijumpai',
    searchFilters: 'Penapis Carian',
    hideFilters: 'Sembunyikan Penapis',
    showFilters: 'Tunjukkan Penapis',
    serviceType: 'Jenis Perkhidmatan',
    allServices: 'Semua Perkhidmatan',
    snorkeling: 'Snorkeling',
    passengerBoat: 'Bot Penumpang',
    location: 'Lokasi',
    allLocations: 'Semua Lokasi',
    redangIsland: 'Pulau Redang',
    perhentianIsland: 'Pulau Perhentian',
    date: 'Tarikh',
    time: 'Masa',
    when: 'Bila',
    selectDate: 'Pilih tarikh',
    selectTime: 'Pilih masa',
    selectDateTime: 'Pilih tarikh dan masa',
    selectedTime: 'Masa dipilih',
    selectServiceType: 'Pilih jenis perkhidmatan',
    selectLocation: 'Pilih lokasi',
    selectJetty: 'Pilih jetty',
    serviceTypeRequired: 'Sila pilih jenis perkhidmatan',
    locationRequired: 'Sila pilih lokasi',
    dateTimeRequired: 'Sila pilih tarikh dan masa',
    destinationRequired: 'Sila pilih destinasi',
    Jetty: 'Jeti',

    // Search Form Loading States
    loadingLocations: 'Memuatkan lokasi...',
    loadingServices: 'Memuatkan perkhidmatan...',
    loadingDestinations: 'Memuatkan destinasi...',
    loadingJetties: 'Memuatkan jeti...',

    // Search Form Empty States
    noJettiesAvailable: 'Tiada jeti tersedia',
    noServicesAvailable: 'Tiada kategori perkhidmatan tersedia',
    noDestinationsAvailable: 'Tiada destinasi tersedia',
    noDestinationsFromLocation: 'Tiada destinasi tersedia dari lokasi ini',

    // Destination Selector
    destination: 'Destinasi',
    allDestinations: 'Semua Destinasi',
    selectDestination: 'Pilih destinasi',
    selectLocationFirst: 'Pilih lokasi dahulu',

    // Booking Wizard
    bookThisBoat: 'Tempah Bot Ini',
    selectPackage: 'Pilih Pakej',
    clickToSelectDate: 'Klik untuk memilih tarikh',

    // Age-based Pricing
    adults: 'Dewasa',
    children: 'Kanak-kanak',
    toddlers: 'Kanak-kanak kecil',
    adultsAge: 'Dewasa (12+ tahun)',
    childrenAge: 'Kanak-kanak (4-11 tahun)',
    toddlersAge: 'Kanak-kanak kecil (0-3 tahun)',
    passengerBreakdown: 'Pecahan Penumpang',
    ageBasedPricing: 'Harga Berdasarkan Umur',
    ageBasedPrice: 'Harga Berdasarkan Umur',
    addAgeCategory: 'Tambah Kategori Umur',
    allAgeCategoriesAdded: 'Semua kategori umur telah ditambah',
    clickAddAgeCategoryText: 'Klik "Tambah Kategori Umur" untuk menetapkan harga untuk kumpulan umur yang berbeza',
    atLeastOneAgeCategoryRequired: 'Sekurang-kurangnya satu kategori umur mesti mempunyai harga lebih daripada 0',
    free: 'Percuma',
    selectPassengers: 'Pilih Penumpang',
    selectedDate: 'Tarikh Dipilih',
    unavailable: 'Tidak Tersedia',
    adults: 'Dewasa',
    children: 'Kanak-kanak',
    perPerson: 'setiap orang',
    perChild: 'setiap kanak-kanak',
    totalPrice: 'Jumlah Harga',
    previous: 'Sebelumnya',
    next: 'Seterusnya',
    bookNow: 'Tempah Sekarang',
    from: 'Harga Bermula',

    // Boat Details
    itineraryAndInclusions: 'Jadual & Termasuk',
    schedule: 'Jadual',
    boatNotFound: 'Bot Tidak Dijumpai',
    boatNotFoundDescription: 'Bot yang anda cari tidak dapat dijumpai.',
    backToSearch: 'Kembali ke Carian',
    description: 'Penerangan',
    packages: 'Pakej',
    amenities: 'Kemudahan',
    boatOwner: 'Pemilik Bot',
    signInRequired: 'Log Masuk Diperlukan',
    pleaseSignInToBook: 'Sila log masuk untuk membuat tempahan',
    home: 'Laman Utama',
    getStarted: 'Mula Sekarang',
    simpleProcess: 'Proses Mudah',
    contactUs: 'Hubungi Kami',
    messageSentSuccess: 'Mesej berjaya dihantar!',
    messageSentError: 'Gagal menghantar mesej. Sila cuba lagi.',
    fullName: 'Nama Penuh',
    enterFullName: 'Masukkan nama penuh anda',
    enterEmail: 'Masukkan alamat emel anda',
    subject: 'Subjek',
    enterSubject: 'Masukkan subjek mesej',
    message: 'Mesej',
    enterMessage: 'Masukkan mesej anda',
    sending: 'Menghantar...',
    sendMessage: 'Hantar Mesej',
    hour: 'Jam',
    minute: 'Minit',
    period: 'Waktu',
    done: 'Selesai',
    cancel: 'Batal',
    capacity: 'Kapasiti',
    numberOfPeople: 'Bilangan orang',
    searching: 'Mencari...',
    clearFilters: 'Set Semula',
    searchingBoats: 'Mencari bot...',
    noBoatsFound: 'Tiada bot dijumpai',
    tryDifferentFilters: 'Cuba laraskan penapis carian anda',
    searchResults: 'Hasil Carian',
    people: 'orang',
    trip: 'perjalanan',
    viewDetails: 'Lihat Butiran',
    readyToSearch: 'Bersedia untuk mencari bot anda?',
    useFiltersAbove: 'Gunakan penapis di atas untuk mencari bot yang tersedia',
    searchHelpText: 'Cari bot yang sempurna untuk pengembaraan laut Malaysia anda',

    // Boat Details
    boatNotFound: 'Bot Tidak Dijumpai',
    boatNotFoundDescription: 'Bot yang anda cari tidak dapat dijumpai atau tidak lagi tersedia.',
    backToResults: 'Kembali ke Hasil',
    backToSearch: 'Kembali ke Carian',
    photos: 'Foto',
    description: 'Penerangan',
    packages: 'Pakej',
    included: 'Termasuk',
    amenities: 'Kemudahan',
    bookThisBoat: 'Tempah Bot Ini',
    basePrice: 'Harga Asas',
    selectedPackage: 'Pakej Dipilih',
    total: 'Jumlah',
    bookNow: 'Tempah Sekarang',
    bookingTerms: 'Dengan menempah, anda bersetuju dengan terma dan syarat kami',
    boatOwner: 'Pemilik Bot',
    signInRequired: 'Log Masuk Diperlukan',
    pleaseSignInToBook: 'Sila log masuk ke akaun anda untuk menempah bot ini.',
    per: 'setiap',
    firstNameRequired: 'Nama pertama diperlukan',
    lastNameRequired: 'Nama akhir diperlukan',
    phoneNumber: 'Nombor Telefon',
    optional: 'Pilihan',
    dateOfBirth: 'Tarikh Lahir',
    selectDateOfBirth: 'Pilih tarikh lahir anda',
    addressInformation: 'Maklumat Alamat',
    addressLine1: 'Alamat Baris 1',
    addressLine2: 'Alamat Baris 2',
    enterAddressLine1: 'Masukkan alamat baris 1',
    enterAddressLine2: 'Masukkan alamat baris 2',
    postcode: 'Poskod',
    city: 'Bandar',
    state: 'Negeri',
    enterCity: 'Masukkan bandar',
    enterState: 'Masukkan negeri',
    emergencyContact: 'Nombor Telefon Kecemasan',
    invalidPhoneNumber: 'Sila masukkan nombor telefon Malaysia yang sah',
    invalidEmergencyContact: 'Sila masukkan nombor kenalan kecemasan yang sah',
    uploadPhoto: 'Muat Naik Foto',
    changePhoto: 'Tukar Foto',
    supportedFormats: 'Format yang disokong',
    cancel: 'Batal',
    saveChanges: 'Simpan Perubahan',
    saving: 'Menyimpan...',
    profileUpdatedSuccessfully: 'Profil berjaya dikemas kini!',
    updateProfileError: 'Gagal mengemas kini profil. Sila cuba lagi.',
    backToDashboard: 'Kembali ke Dashboard',
    browseServices: 'Layari Perkhidmatan',
    myBookings: 'Tempahan Saya',
    manageBoats: 'Urus Bot',
    viewBookings: 'Lihat Tempahan',
    referralLinks: 'Pautan Rujukan',
    commissionReport: 'Laporan Komisen',
    accountSettings: 'Tetapan Akaun',
    recentActivity: 'Aktiviti Terkini',
    noRecentActivity: 'Tiada aktiviti terkini untuk dipaparkan.',
    startExploring: 'Mula meneroka untuk melihat aktiviti anda di sini!',

    // Customer Dashboard
    discoverMarineServices: 'Temui Perkhidmatan Marin',
    exploreServices: 'Terokai pelbagai perkhidmatan marin kami dan tempah pengembaraan seterusnya.',
    snorkeling: 'Snorkeling',
    snorkelingDesc: 'Temui keajaiban bawah air dan terokai terumbu karang yang berwarna-warni',
    boatTours: 'Lawatan Bot',
    boatToursDesc: 'Pengembaraan melompat pulau yang indah dan penerokaan pantai',
    islandTrips: 'Perjalanan Pulau',
    islandTripsDesc: 'Terokai pulau-pulau murni dan syurga tropika tersembunyi',

    // Boat Owner Dashboard
    boatManagement: 'Pengurusan Bot',
    manageFleet: 'Urus armada anda dan jejaki tempahan untuk bot anda.',
    totalBoats: 'Jumlah Bot',
    addFirstBoat: 'Tambah bot pertama anda',
    activeBookings: 'Tempahan Aktif',
    noActiveBookings: 'Tiada tempahan aktif',
    monthlyRevenue: 'Hasil Bulanan',
    startEarning: 'Mula menjana hari ini',

    // Affiliate Agent Dashboard
    affiliateDashboard: 'Papan Pemuka Gabungan',
    trackReferrals: 'Jejaki rujukan dan pendapatan komisen anda.',
    totalReferrals: 'Jumlah Rujukan',
    startReferring: 'Mula merujuk pelanggan',
    commissionEarned: 'Komisen Diperoleh',
    noCommissions: 'Belum ada komisen',
    conversionRate: 'Kadar Penukaran',
    noDataAvailable: 'Tiada data tersedia',

    // Booking Page Translations
    yearsOld: 'tahun',
    bookService: 'Tempah Servis',
    bookingDetails: 'Butiran Tempahan',
    bookingSummary: 'Ringkasan Tempahan',
    missingProviderService: 'Maklumat penyedia atau perkhidmatan hilang',
    failedLoadProvider: 'Gagal memuatkan butiran penyedia',
    failedLoadService: 'Gagal memuatkan butiran perkhidmatan',
    authenticationRequired: 'Pengesahan Diperlukan',
    serviceRequired: 'Perkhidmatan Diperlukan',
    pleaseSelectService: 'Sila pilih perkhidmatan untuk ditempah.',
    routeRequired: 'Laluan Diperlukan',
    pleaseSelectDestination: 'Sila pilih destinasi.',
    dateTimeRequiredBooking: 'Tarikh dan Masa Diperlukan',
    pleaseSelectDateTime: 'Sila pilih kedua-dua tarikh dan masa untuk tempahan anda.',
    bookingFailed: 'Tempahan Gagal',
    failedCreateBooking: 'Gagal membuat tempahan',
    bookingError: 'Ralat Tempahan',
    bookingErrorMessage: 'Ralat berlaku semasa membuat tempahan anda. Sila cuba lagi.',
    bookingConfirmed: 'Tempahan Disahkan!',
    bookingConfirmedMessage: 'Tempahan anda telah disahkan. ID Tempahan:',
    standardService: 'Perkhidmatan Standard',
    basicServiceOffering: 'Penawaran perkhidmatan asas',
    packageOffering: 'Penawaran pakej',
    serviceIncluded: 'Perkhidmatan termasuk',
    transportation: 'Pengangkutan',
    professionalGuide: 'Pemandu profesional',
    safetyEquipment: 'Peralatan keselamatan',
    notSelected: 'Tidak dipilih',
    specialRequestsOptional: 'Permintaan Khas (Pilihan)',
    specialRequestsPlaceholder: 'Sebarang keperluan atau permintaan khas...',
    confirmBooking: 'Sahkan Tempahan',
    creatingBooking: 'Membuat Tempahan...',
    mostPopular: 'Paling Popular',
    popular: 'Popular',
    free: 'PERCUMA',
    perPerson: 'setiap orang',
    adults: 'Dewasa',
    children: 'Kanak-kanak',
    toddlers: 'Kanak-kanak kecil',
    seniors: 'Warga Emas',
    pwd: 'OKU',
    passengers: 'Penumpang',
    adultsAge: 'Dewasa (13+ tahun)',
    childrenAge: 'Kanak-kanak (3-12 tahun)',
    toddlersAge: 'Kanak-kanak kecil (0-2 tahun)',
    provider: 'Penyedia',
    route: 'Laluan',
    totalPassengers: 'Jumlah Penumpang',
    person: 'Penumpang',
    maximumCapacity: 'Kapasiti maksimum',
    totalCapacityLabel: 'Jumlah',
    package: 'Pakej',
    available: 'Tersedia',
    unavailable: 'Tidak Tersedia',
    noTimeSlotsAvailable: 'Tiada slot masa tersedia untuk tarikh yang dipilih',
    numberOfPassengers: 'Bilangan Penumpang',
    personWithDisabilities: 'Orang Kurang Upaya',


    // Calendar and Time
    sun: 'Ahd',
    mon: 'Isn',
    tue: 'Sel',
    wed: 'Rab',
    thu: 'Kha',
    fri: 'Jum',
    sat: 'Sab',

    // Month names
    january: 'Januari',
    february: 'Februari',
    march: 'Mac',
    april: 'April',
    may: 'Mei',
    june: 'Jun',
    july: 'Julai',
    august: 'Ogos',
    september: 'September',
    october: 'Oktober',
    november: 'November',
    december: 'Disember',

    // Day names (full)
    sunday: 'Ahad',
    monday: 'Isnin',
    tuesday: 'Selasa',
    wednesday: 'Rabu',
    thursday: 'Khamis',
    friday: 'Jumaat',
    saturday: 'Sabtu',

    // Time picker
    hour: 'Jam',
    minute: 'Minit',

    availableTimeSlots: 'Pilih Slot Masa',

    // Common Actions and States
    loading: 'Memuatkan...',
    error: 'Ralat',
    failed: 'Gagal',
    required: 'Diperlukan',
    optional: 'Pilihan',
    available: 'Tersedia',
    unavailable: 'Tidak Tersedia',
    highAvailability: 'Ketersediaan tinggi',
    mediumAvailability: 'Ketersediaan sederhana',
    lowAvailability: 'Ketersediaan rendah',
    seatAvailabilityLegend: 'Nombor menunjukkan tempat duduk tersedia/jumlah',
    select: 'Pilih',
    choose: 'Pilih',
    add: 'Tambah',
    remove: 'Buang',
    update: 'Kemas Kini',
    create: 'Cipta',
    confirm: 'Sahkan',
    save: 'Simpan',
    edit: 'Edit',
    delete: 'Padam',
    submit: 'Hantar',
    back: 'Kembali',
    next: 'Seterusnya',
    previous: 'Sebelumnya',
    show: 'Tunjuk',
    hide: 'Sembunyi',
    more: 'Lagi',
    less: 'Kurang',
    all: 'Semua',
    none: 'Tiada',
    yes: 'Ya',
    no: 'Tidak',
    ok: 'OK',
    close: 'Tutup',
    open: 'Buka',
    selectDate: 'Pilih tarikh',
    pleaseSelectDate: 'Sila pilih tarikh',
    pleaseSelectTime: 'Sila pilih masa',
    pleaseSelectPassengers: 'Sila pilih bilangan penumpang',
    atLeastOneAdult: 'Sekurang-kurangnya setiap orang penumpang dewasa diperlukan',
    pleaseSelectPackage: 'Sila pilih pakej',
    selectDateOfBirth: 'Pilih tarikh lahir anda',
    selectLicenseExpiryDate: 'Pilih tarikh tamat lesen',

    // Provider Page Translations
    loadingProviderDetails: 'Memuatkan butiran penyedia...',
    providerNotFound: 'Penyedia Tidak Dijumpai',
    providerNotFoundMessage: 'Penyedia yang anda cari tidak wujud.',
    backToSearch: 'Kembali ke Carian',
    providerLogo: 'Logo Penyedia',
    providerNameNotAvailable: 'Nama Penyedia Tidak Tersedia',
    operatingSince: 'Beroperasi sejak',
    rating: 'penilaian',
    availableServices: 'Perkhidmatan Tersedia',
    noServicesAvailable: 'Tiada perkhidmatan tersedia pada masa ini.',
    fleet: 'Kumpulan Bot',
    boats: 'Bot',
    viewDetails: 'Lihat Butiran',
    bookNow: 'Tempah Sekarang',
    failedLoadProviderDetails: 'Gagal memuatkan butiran penyedia',
    errorLoadingProvider: 'Ralat berlaku semasa memuatkan butiran penyedia',
    upTo: 'Hingga',

    // Service Page Translations
    uploadServiceImages: 'Muat Naik Gambar Perkhidmatan',
    selectRoutes: 'Sila pilih laluan untuk perkhidmatan ini.',
    atLeastOneRouteRequired: 'Sekurang-kurangnya setiap laluan diperlukan',
    atLeastOneInclusionRequired: 'Sekurang-kurangnya satu item yang disertakan diperlukan',
    service: 'Perkhidmatan',
    serviceNotFound: 'Perkhidmatan Tidak Dijumpai',
    serviceNotFoundMessage: 'Perkhidmatan yang diminta tidak dapat dijumpai.',
    errorLoadingService: 'Gagal memuatkan butiran perkhidmatan. Sila cuba lagi.',
    backToProvider: 'Kembali ke',
    serviceDetails: 'Butiran Perkhidmatan',
    duration: 'Tempoh',
    maxCapacity: 'Kapasiti Maksimum',
    passengers: 'penumpang',
    provider: 'Penyedia',
    includedItems: 'Apa Yang Disediakan',
    excludedItems: 'Apa Yang Tidak Disediakan',
    includedExcludedItems: 'Apa Yang Disediakan & Tidak Disediakan',
    specialInstructions: 'Arahan Khas',
    itinerary: 'Itinerari',
    noItineraryAvailable: 'Tiada itinerari tersedia untuk perkhidmatan ini',
    noItineraryMessage: 'Itinerari terperinci untuk perkhidmatan ini belum disediakan lagi. Sila hubungi penyedia untuk maklumat lanjut tentang jadual.',
    bookThisService: 'Tempah Perkhidmatan Ini',
    location: 'Lokasi',
    fromService: 'Dari',
    availableDestinations: 'Destinasi Tersedia',

    // Booking Page Additional Translations
    standardService: 'Perkhidmatan Standard',
    basicServiceOffering: 'Penawaran perkhidmatan asas',
    packageOffering: 'Penawaran pakej',
    serviceIncluded: 'Perkhidmatan termasuk',
    transportation: 'Pengangkutan',
    professionalGuide: 'Pemandu profesional',
    safetyEquipment: 'Peralatan keselamatan',
    selectPackage: 'Pilih Pakej',
    mostPopular: 'Paling Popular',
    popular: 'Popular',
    free: 'PERCUMA',
    perPerson: 'setiap orang',
    adults: 'Dewasa',
    children: 'Kanak-kanak',
    toddlers: 'Kanak-kanak kecil',
    seniors: 'Warga Emas',
    pwd: 'OKU',
    adultsAge: 'Dewasa (13+ tahun)',
    childrenAge: 'Kanak-kanak (3-12 tahun)',
    toddlersAge: 'Kanak-kanak kecil (0-2 tahun)',
    numberOfPassengers: 'Bilangan Penumpang',
    maximumCapacity: 'Kapasiti maksimum',
    totalPassengers: 'Jumlah Penumpang',
    packageLabel: 'Pakej',
    dateLabel: 'Tarikh',
    timeLabel: 'Masa',
    notSelected: 'Belum dipilih',
    person: 'Penumpang',
    more: 'lagi',
    additionalItems: 'Item Tambahan',
    noPackagesAvailable: 'Tiada pakej tersedia untuk perkhidmatan ini',
    uploadImages: 'Muat Naik Imej',
    
    // Package-only pricing translations
    packageOnlyNote: 'Harga dan pakej harus diperincikan dalam langkah seterusnya (pakej)',
    basicPricingOption: 'Harga Asas',
    packageOnlyOption: 'Pakej Sahaja',
    ageBasedOption: 'Berdasarkan Umur',
    packageAndAgeOption: 'Pakej dan Berdasarkan Umur',
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en');

  // Load language from localStorage on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('gosea-language');
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ms')) {
      setLanguage(savedLanguage);
    }
  }, []);

  // Save language to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('gosea-language', language);
  }, [language]);

  const t = (key, variables = {}) => {
    let translation = translations[language][key] || key;

    // Replace variables in the format {{variableName}}
    Object.keys(variables).forEach(variable => {
      const placeholder = `{{${variable}}}`;
      translation = translation.replace(new RegExp(placeholder, 'g'), variables[variable]);
    });

    return translation;
  };

  const changeLanguage = (newLanguage) => {
    if (newLanguage === 'en' || newLanguage === 'ms') {
      setLanguage(newLanguage);
    }
  };

  const value = {
    language,
    changeLanguage,
    t,
    isEnglish: language === 'en',
    isMalay: language === 'ms'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
