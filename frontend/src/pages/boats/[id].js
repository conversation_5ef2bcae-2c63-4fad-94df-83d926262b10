import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import Navbar from '../../components/Navbar';
import StandardModal from '../../components/StandardModal';
import BoatItinerary from '../../components/boat/BoatItinerary';
import BookingWizard from '../../components/boat/BookingWizard';
import {
  ArrowLeftIcon,
  MapPinIcon,
  UsersIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ClockIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import Footer from '../../components/Footer';

export default function BoatDetailsPage() {
  const { t } = useLanguage();
  const { user } = useAuth();
  const router = useRouter();
  const { id } = router.query;
  
  // State
  const [boat, setBoat] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPhoto, setSelectedPhoto] = useState(0);
  const [selectedPackage, setSelectedPackage] = useState(null);
  
  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    type: 'info',
    title: '',
    message: '',
    details: null
  });

  // Fetch boat details
  useEffect(() => {
    if (id) {
      fetchBoatDetails();
    }
  }, [id]);

  const fetchBoatDetails = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/boats/${id}`);
      const data = await response.json();

      if (data.success) {
        setBoat(data.boat);
        if (data.boat.packages && data.boat.packages.length > 0) {
          setSelectedPackage(data.boat.packages[0]);
        }
      } else {
        showErrorModal('Boat Not Found', data.message || 'The requested boat could not be found.');
      }
    } catch (error) {
      console.error('Fetch boat details error:', error);
      showErrorModal('Error', 'Failed to load boat details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Modal helpers
  const showErrorModal = (title, message, details = null) => {
    setModalConfig({ type: 'error', title, message, details });
    setShowModal(true);
  };

  const showInfoModal = (title, message, details = null) => {
    setModalConfig({ type: 'info', title, message, details });
    setShowModal(true);
  };

  // Handle booking completion from wizard
  const handleBookingComplete = (bookingData) => {
    if (!user) {
      showInfoModal(
        t('signInRequired'),
        t('pleaseSignInToBook'),
        null
      );
      return;
    }

    // Navigate to booking page with complete booking data
    const bookingQuery = {
      boatId: boat.id,
      packageId: bookingData.selectedPackage?.id || null,
      selectedDate: bookingData.selectedDate?.date?.toISOString() || null,
      adults: bookingData.adults,
      children: bookingData.children,
      totalPrice: bookingData.totalPrice
    };

    router.push({
      pathname: '/booking',
      query: bookingQuery
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
              </div>
              <div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!boat) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🚫</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {t('boatNotFound')}
            </h1>
            <p className="text-gray-600 mb-6">
              {t('boatNotFoundDescription')}
            </p>
            <button
              onClick={() => router.push('/search')}
              className="px-6 py-3 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
            >
              {t('backToSearch')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Boat Details - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <button
          onClick={() => router.back()}
          className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 mb-6"
        >
          <ArrowLeftIcon className="w-5 h-5" />
          <span>{t('backToResults')}</span>
        </button>

        {/* Boat Header */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
            <div>
              <h1 className="text-lg lg:text-2xl font-bold text-gray-900">
                {boat.name}
              </h1>
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <MapPinIcon className="w-4 h-4" />
                  <span>
                    {boat.location === 'REDANG_ISLAND' ? t('redangIsland') : t('perhentianIsland')}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <UsersIcon className="w-4 h-4" />
                  <span>{boat.capacity} {t('people')}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="px-2 py-1 bg-amber-100 text-amber-800 rounded-full text-xs font-medium">
                    {boat.serviceType === 'SNORKELING' ? t('snorkeling') : t('passengerBoat')}
                  </span>
                </div>
              </div>
            </div>
            <div className="mt-4 lg:mt-0 text-right">
              <div className="text-3xl font-bold text-amber-600">
                RM {selectedPackage ? selectedPackage.price : boat.basePrice}
              </div>
              <div className="text-sm text-gray-500">
                {selectedPackage ? `${t('per')} ${selectedPackage.name}` : `${t('per')} ${t('trip')}`}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Photo Gallery */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {t('photos')}
              </h2>
              
              {boat.photos && boat.photos.length > 0 ? (
                <div>
                  {/* Main Photo */}
                  <div className="aspect-w-16 aspect-h-9 mb-4">
                    <img
                      src={boat.photos[selectedPhoto].url}
                      alt={boat.photos[selectedPhoto].alt}
                      className="w-full h-64 object-cover rounded-lg"
                    />
                  </div>
                  
                  {/* Photo Thumbnails */}
                  {boat.photos.length > 1 && (
                    <div className="grid grid-cols-4 gap-2">
                      {boat.photos.map((photo, index) => (
                        <button
                          key={photo.id}
                          onClick={() => setSelectedPhoto(index)}
                          className={`aspect-w-16 aspect-h-9 rounded-lg overflow-hidden border-2 transition-colors ${
                            selectedPhoto === index ? 'border-amber-500' : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <img
                            src={photo.url}
                            alt={photo.alt}
                            className="w-full h-16 object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-4xl text-gray-400">⛵</span>
                </div>
              )}
            </div>

            {/* Description */}
            {boat.description && (
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('description')}
                </h2>
                <p className="text-gray-600 leading-relaxed">
                  {boat.description}
                </p>
              </div>
            )}

            {/* Packages */}
            {boat.packages && boat.packages.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('packages')}
                </h2>
                <div className="space-y-4">
                  {boat.packages.map((pkg) => (
                    <div
                      key={pkg.id}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedPackage?.id === pkg.id 
                          ? 'border-amber-500 bg-amber-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedPackage(pkg)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {pkg.name}
                        </h3>
                        <div className="text-xl font-bold text-amber-600">
                          RM {pkg.price}
                        </div>
                      </div>
                      
                      {pkg.description && (
                        <p className="text-gray-600 mb-3">
                          {pkg.description}
                        </p>
                      )}
                      
                      {pkg.duration && (
                        <div className="flex items-center space-x-1 text-sm text-gray-500 mb-2">
                          <ClockIcon className="w-4 h-4" />
                          <span>{pkg.duration}</span>
                        </div>
                      )}
                      
                      {pkg.includedItems && pkg.includedItems.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            {t('included')}:
                          </h4>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {pkg.includedItems.map((item, index) => (
                              <li key={index} className="flex items-center space-x-2">
                                <span className="text-green-500">✓</span>
                                <span>{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Itinerary */}
            <BoatItinerary
              itinerary={boat.itinerary || []}
              includedItems={boat.includedItems || []}
              packageDetails={boat.packageDetails}
            />

            {/* Amenities */}
            {boat.amenities && boat.amenities.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('amenities')}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {boat.amenities.map((amenity) => (
                    <div key={amenity.id} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {amenity.name}
                        </div>
                        {amenity.description && (
                          <div className="text-sm text-gray-600">
                            {amenity.description}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Booking Wizard */}
            <div className="sticky top-8">
              <BookingWizard
                boat={boat}
                onBookingComplete={handleBookingComplete}
              />
            </div>

            {/* Owner Info */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('boatOwner')}
              </h3>
              
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-amber-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">
                    {boat.owner.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <div className="font-medium text-gray-900">
                    {boat.owner.name}
                  </div>
                  <div className="text-sm text-gray-600">
                    {t('boatOwner')}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />

      {/* Modal */}
      <StandardModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        type={modalConfig.type}
        title={modalConfig.title}
        message={modalConfig.message}
        details={modalConfig.details}
      />
    </div>
    </>
  );
}
