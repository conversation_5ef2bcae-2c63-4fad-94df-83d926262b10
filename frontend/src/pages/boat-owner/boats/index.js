import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import ConfirmationModal from '../../../components/ConfirmationModal';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  MapPinIcon
} from '@heroicons/react/24/outline';
import { Ship, IdCard, Users, MapPin } from 'lucide-react';

const BoatOwnerBoats = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, accessToken } = useAuth();
  const { t } = useLanguage();

  const [boats, setBoats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // Confirmation modal state
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [boatToDelete, setBoatToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Fetch boats data
  useEffect(() => {
    const fetchBoats = async () => {
      if (!accessToken) return;

      try {
        const params = new URLSearchParams({
          page: currentPage.toString(),
          limit: '12',
          ...(searchTerm && { search: searchTerm }),
          ...(statusFilter && { status: statusFilter })
        });

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/boats?${params}`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          setBoats(data.data);
          setTotalPages(data.pagination.pages);
        } else {
          setError('Failed to load boats');
        }
      } catch (error) {
        console.error('Boats fetch error:', error);
        setError('Failed to load boats');
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken) {
      fetchBoats();
    }
  }, [isAuthenticated, user, accessToken, currentPage, searchTerm, statusFilter]);

  const confirmDeleteBoat = async () => {
    if (!boatToDelete || !accessToken) return;

    setIsDeleting(true);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/boats/${boatToDelete}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        setBoats(boats.filter(boat => boat.id !== boatToDelete));
        setShowConfirmationModal(false);
        setBoatToDelete(null);
        // Show success feedback if needed
      } else {
        const errorData = await response.json();
        // Handle error feedback if needed
        alert(errorData.message || t('failedToDeleteBoat'));
      }
    } catch (error) {
      console.error('Delete boat error:', error);
      // Handle error feedback if needed
      alert(t('failedToDeleteBoat'));
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteBoat = async (boatId) => {
    // Set the boat to delete and show confirmation modal
    setBoatToDelete(boatId);
    setShowConfirmationModal(true);
  };

  const getImageUrl = (imagePath) => {
    if (!imagePath) return '/images/boat-placeholder.jpg';
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/uploads/')) {
      return `${process.env.NEXT_PUBLIC_API_URL}${imagePath}`;
    }
    return imagePath;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-emerald-100 text-emerald-800';
      case 'PENDING_APPROVAL':
        return 'bg-amber-100 text-amber-800';
      case 'MAINTENANCE':
        return 'bg-sky-100 text-sky-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading || loading) {

    return (
      <>
        <Head>
          <title>My Boats - GoSea</title>
          <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-600"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>My Boats - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/gosea_favicon.png" />
        <link rel="shortcut icon" href="/gosea_favicon.png" />
        <link rel="apple-touch-icon" href="/gosea_favicon.png" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-7xl mx-auto px-4 py-4 md:py-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-4 md:mb-6">
            <h1 className="text-lg lg:text-2xl font-bold text-gray-900">
              {t('myBoats')}
            </h1>
            <Link
              href="/boat-owner/boats/create"
              className="flex items-center justify-center px-3 py-2 text-white bg-sky-500 rounded-md hover:bg-sky-600 active:bg-sky-600 active:text-white transition-colors font-medium text-sm touch-manipulation w-auto"
              style={{
                WebkitTapHighlightColor: 'transparent',
                touchAction: 'manipulation',
                cursor: 'pointer',
                userSelect: 'none'
              }}
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              {t('addNewBoat')}
            </Link>
          </div>

          {/* Search and Filters */}
          <div className="bg-white sm:rounded-md shadow p-6">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--base-content)' }}>{t('searchBoats')}</label>
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder={t('searchBoats')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  />
                </div>
              </div>
              <div className="sm:w-1/4">
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--base-content)' }}>{t('status')}</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">{t('allStatuses')}</option>
                  <option value="DRAFT">{t('draft')}</option>
                  <option value="PENDING_APPROVAL">{t('pendingApproval')}</option>
                  <option value="APPROVED">{t('approved')}</option>
                  <option value="REJECTED">{t('rejected')}</option>
                  <option value="INACTIVE">{t('inactive')}</option>
                  <option value="MAINTENANCE">{t('maintenance')}</option>
                </select>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* Boats Grid */}
            {boats.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {boats.map((boat) => (
                  <div
                    key={boat.id}
                    className="bg-white rounded-lg shadow-sm border hover:shadow-lg transition-all duration-200 cursor-pointer group"
                    onClick={() => router.push(`/boat-owner/boats/${boat.id}`)}
                  >
                    {/* Boat Image */}
                    <div className="relative aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-lg overflow-hidden">
                      <img
                        src={getImageUrl(boat.galleryImages?.[0] || boat.images?.[0])}
                        alt={boat.name}
                        className={`w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200 ${boat.status === 'PENDING_APPROVAL' ? 'filter grayscale' : ''
                          }`}
                      />

                      {/* Status Badge */}
                      <div className="absolute top-3 right-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(boat.status)}`}>
                          {t(boat.status === 'PENDING_APPROVAL' ? 'PENDING  APPROVAL' : boat.status || 'INACTIVE')}
                        </span>
                      </div>

                      {/* Capacity Badge */}
                      {/* <div className="absolute top-3 left-3">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-sky-500 text-white">
                          <Users className="h-3 w-3 mr-1" />
                          {boat.capacity}
                        </span>
                      </div> */}
                    </div>

                    {/* Boat Info */}
                    <div className="p-4">
                      {/* Boat Name */}
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-sky-600 transition-colors">
                          {boat.name}
                        </h3>
                      </div>

                      {/* Description */}
                      {boat.description && (
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                          {boat.description}
                        </p>
                      )}

                      {/* Boat Details */}
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-3 border-b pb-3">
                        <div className="flex items-center space-x-4">
                          {/* Registration Number */}
                          {boat.registrationNumber && (
                            <div className="flex items-center space-x-1">
                              <IdCard className="h-4 w-4 text-amber-500" />
                              <span>{boat.registrationNumber}</span>
                            </div>
                          )}

                          {/* Capacity */}
                          <div className="flex items-center space-x-1">
                            <Users className="h-4 w-4 text-amber-500" />
                            <span> {boat.capacity}</span>
                          </div>

                          {/* Location */}
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-4 w-4 text-amber-500" />
                            <span className="truncate">
                            {boat.location && typeof boat.location === 'object' 
                              ? boat.location.name 
                              : 'No location specified'}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Amenities */}
                      {boat.amenities && Array.isArray(boat.amenities) && boat.amenities.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-3 pb-3 border-b"> 
                          {boat.amenities.slice(0, 5).map((amenity, index) => (
                            <span 
                              key={index} 
                              className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600"
                            >
                              {amenity}
                            </span>
                          ))}
                          {boat.amenities.length > 5 && (
                            <div className="relative group/amenity">
                              <span 
                                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600 cursor-help"
                              >
                                +{boat.amenities.length - 5} more
                              </span>

                              {/* Hover Tooltip */}
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover/amenity:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap max-w-xs">
                                <div className="text-center">
                                  <div className="font-medium mb-1">Additional Amenities:</div>
                                  <div className="space-y-1 max-h-40 overflow-y-auto">
                                    {boat.amenities.slice(5).map((item, idx) => (
                                      <div key={idx} className="text-left">• {item}</div>
                                    ))}
                                  </div>
                                </div>
                                {/* Tooltip Arrow */}
                                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Location */}
                      {/* {boat.location && Array.isArray(boat.location) && boat.location.length > 0 && (
                        <div className="flex items-center space-x-1 text-sm text-gray-500 mb-3">
                          <MapPinIcon className="h-4 w-4 text-amber-500" />
                          <span className="truncate">
                            {boat.location.map(jetty => jetty.name).join(', ')}
                          </span>
                        </div>
                      )} */}

                      {/* Service Type */}
                      {boat.serviceType && (
                        <div className="mb-3">
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600">
                            {t(boat.serviceType)}
                          </span>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="pt-3 mt-3">
                        <div className="flex space-x-1 justify-end">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/boat-owner/boats/${boat.id}/edit`);
                            }}
                            className="w-16 px-3 py-2 text-white bg-sky-500 text-center rounded-lg hover:bg-sky-600 transition-colors text-xs font-medium"
                          >
                            {/* <Pencil className="h-4 w-4" /> */}
                            {t('edit')}
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteBoat(boat.id);
                            }}
                            className="w-16 px-3 py-2 bg-amber-500 rounded-lg hover:bg-amber-600 text-white transition-colors text-xs font-medium"
                          >
                            {/* <Trash2 className="h-4 w-4" /> */}
                            {t('delete')}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-gray-50">
                <div className="w-20 h-20 flex items-center justify-center rounded-full bg-amber-100 text-amber-600 mx-auto mb-4">
                  <Ship className="h-10 w-10" />
                </div>

                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {t('noBoatsFound')}
                </h3>
                <p className="text-gray-500 mb-2">
                  {searchTerm || statusFilter ? t('noBoatsMatchFilters') : t('startByAddingYourFirstBoat')}
                </p>
                <Link
                  href="/boat-owner/boats/create"
                  className="inline-flex items-center justify-center space-x-1 px-3 py-3 text-sky-500 hover:text-sky-600 active:text-sky-700 transition-colors font-medium text-md touch-manipulation"
                  style={{
                    WebkitTapHighlightColor: 'transparent',
                    touchAction: 'manipulation',
                    cursor: 'pointer',
                    userSelect: 'none'
                  }}

                >
                  {/* <PlusIcon className="h-4 w-4 mr-2" /> */}
                  {t('addYourFirstBoat')}
                </Link>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-8 flex justify-center">
                <nav className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t('previous')}
                  </button>

                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`px-3 py-2 border rounded-lg text-sm font-medium ${currentPage === page
                        ? 'border-sky-500 bg-sky-50 text-sky-600'
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                        }`}
                    >
                      {page}
                    </button>
                  ))}

                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t('next')}
                  </button>
                </nav>
              </div>
            )}
          </div>
        </main>

        <Footer />

        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={showConfirmationModal}
          onClose={() => {
            setShowConfirmationModal(false);
            setBoatToDelete(null);
          }}
          onConfirm={confirmDeleteBoat}
          title={t('confirmDeleteBoatTitle')}
          message={t('confirmDeleteBoatMessage')}
          confirmText={t('delete')}
          cancelText={t('cancel')}
          isSubmitting={isDeleting}
        />
      </div>
    </>
  );
};

export default BoatOwnerBoats;