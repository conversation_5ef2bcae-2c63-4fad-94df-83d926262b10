import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../../../../contexts/AuthContext';
import { useLanguage } from '../../../../contexts/LanguageContext';
import Navbar from '../../../../components/Navbar';
import Footer from '../../../../components/Footer';
import StandardModal from '../../../../components/StandardModal';
import CustomTimePicker from '../../../../components/CustomTimePicker';
import { CloudArrowUpIcon, XMarkIcon, PlusIcon, MinusIcon, ArrowLeftIcon, ArrowRightIcon, ClockIcon, TrashIcon, ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';
import { Info, Navigation, Check, Package, CircleDollarSign, ListChecks, CalendarClock, Image } from 'lucide-react';

const EditService = () => {
  const router = useRouter();
  const { id: serviceId } = router.query;
  const { user, isAuthenticated, isLoading, accessToken, getCurrentUser } = useAuth();
  const { t } = useLanguage();
  const fileInputRef = useRef(null);

  // Loading state for initial data fetch
  const [initialLoading, setInitialLoading] = useState(true);
  const [serviceNotFound, setServiceNotFound] = useState(false);

  // Wizard state
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 7; // Added step 7 for itinerary

  // Form data state - same structure as create page
  const [formData, setFormData] = useState({
    // Step 1: Basic Information
    name: '',
    description: '',
    serviceTypeId: '',
    duration: 480, // 8 hours default

    // Step 2: Routes & Boats
    routes: [],
    serviceAssignments: [
      {
        boatId: '',
        isPrimary: true,
        maxCapacityOverride: null
      }
    ],

    // Step 3: Inclusions & Exclusions (moved from step 6)
    includedItems: [
      '7 checkpoints',
      '2 ways boat transfer',
      'Snorkeling equipment',
      'Experienced guide',
      'Safety jackets',
      'Lunch box'
    ],
    excludedItems: [
      'Jetty parking fee',
      'Jetty access fee by Majlis Daerah (children under 5 years free)'
    ],
    specialInstruction: '',

    // Step 4: Pricing (moved from step 5)
    basePrice: 80.00,
    pricingModel: '', // 'basic', 'packageOnly', 'ageBased', 'packageAndAge' - now required
    agePricing: {}, // For age-based pricing

    // Step 5: Packages (moved from step 4, conditional)
    servicePackages: [
      {
        packageTypeId: 'pt_regular', // Regular package
        basePrice: 100.00,
        priceModifier: 1.0,
        includedItems: [
          '7 checkpoints',
          '2 ways boat transfer',
          'Snorkeling equipment',
          'Experienced guide',
          'Safety jackets',
          'Lunch box'
        ],
        excludedItems: [
          'Jetty parking fee',
          'Jetty access fee by Majlis Daerah (children under 5 years free)'
        ],
        agePricing: {
          adult: 100.00,
          child: 80.00,
          toddler: 0.00,
          senior: 80.00,
          pwd: 80.00
        }
      },
      {
        packageTypeId: 'pt_premium', // Premium package
        basePrice: 110.00,
        priceModifier: 1.5,
        includedItems: [
          '7 checkpoints',
          '2 ways boat transfer',
          'Snorkeling equipment & Insurance',
          'Experienced guide',
          'Safety jackets',
          'Breakfast at jetty',
          'Lunch buffet, unlimited drinks and hi-teas'
        ],
        excludedItems: [
          'Jetty parking fee',
          'Jetty access fee by Majlis Daerah (children under 5 years free)'
        ],
        agePricing: {
          adult: 110.00,
          child: 90.00,
          toddler: 0.00,
          senior: 90.00,
          pwd: 90.00
        }
      }
    ],

    // Step 6: Schedule
    serviceSchedules: [
      {
        dayOfWeek: null, // null means every day
        departureTime: '09:00',
        availableCapacity: 0 // Will be calculated from boat capacity
      }
    ],

    // Step 7: Itinerary
    itinerary: [
      {
        time: '09:00',
        activity: 'Departure from jetty',
        description: 'Meet at the jetty and board the boat',
        image: null
      },
      {
        time: '10:00',
        activity: 'First snorkeling spot',
        description: 'Explore the coral reefs and marine life',
        image: null
      },
      {
        time: '12:00',
        activity: 'Lunch break',
        description: 'Enjoy lunch on the boat or at a beach',
        image: null
      },
      {
        time: '14:00',
        activity: 'Second snorkeling spot',
        description: 'Visit another beautiful snorkeling location',
        image: null
      },
      {
        time: '16:00',
        activity: 'Return to jetty',
        description: 'Head back to the departure point',
        image: null
      }
    ],

    // Images
    images: []
  });

  // Loading and error states
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [isNavigating, setIsNavigating] = useState(false);

  // Image states
  const [selectedImages, setSelectedImages] = useState([]);
  const [imagePreviews, setImagePreviews] = useState([]);
  const [existingImages, setExistingImages] = useState([]);

  // Modal states
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: ''
  });

  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [confirmationModal, setConfirmationModal] = useState({
    title: '',
    message: '',
    details: '',
    onConfirm: null
  });

  // Data for dropdowns
  const [serviceTypes, setServiceTypes] = useState([]);
  const [routes, setRoutes] = useState([]);
  const [boats, setBoats] = useState([]);
  const [packageTypes, setPackageTypes] = useState([]);
  const [ageCategories, setAgeCategories] = useState([]);

  // Routes searchable dropdown state
  const [routesDropdownOpen, setRoutesDropdownOpen] = useState(false);
  const [routesSearchTerm, setRoutesSearchTerm] = useState('');

  // Fetch service data on component mount
  useEffect(() => {
    if (serviceId && accessToken) {
      fetchServiceData();
    }
  }, [serviceId, accessToken]);

  const fetchServiceData = async () => {
    try {
      setInitialLoading(true);
      const response = await fetch(`/api/boat-owner/services/${serviceId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 404) {
        setServiceNotFound(true);
        return;
      }

      if (!response.ok) {
        throw new Error('Failed to fetch service data');
      }

      const result = await response.json();
      if (result.success) {
        transformAndSetServiceData(result.data);
      } else {
        throw new Error(result.message || 'Failed to fetch service data');
      }
    } catch (error) {
      console.error('Error fetching service data:', error);
      setFeedbackModal({
        type: 'error',
        title: t('error') || 'Error',
        message: t('failedToLoadService') || 'Failed to load service data',
        details: error.message
      });
      setShowFeedbackModal(true);
    } finally {
      setInitialLoading(false);
    }
  };

  // Transform backend data to frontend format
  const transformAndSetServiceData = (serviceData) => {
    console.log('🔄 Transforming service data:', serviceData);

    // Backend to frontend pricing model mapping
    const pricingModelMapping = {
      'basic': 'basic',
      'package_only': 'packageOnly',
      'age_based': 'ageBased',
      'age_package_based': 'packageAndAge'
    };

    // Extract routes - the routeId is the actual route identifier
    const extractedRoutes = serviceData.serviceRoutes?.map(sr => {
      console.log('📍 Processing serviceRoute:', sr);
      return sr.routeId || sr.route_id || sr.route?.id;
    }).filter(Boolean) || [];

    console.log('📍 Extracted routes:', extractedRoutes);

    // Extract boat assignments
    const extractedBoats = serviceData.serviceAssignments?.length > 0
      ? serviceData.serviceAssignments.map(sa => {
          console.log('🚤 Processing serviceAssignment:', sa);
          return {
            boatId: sa.boatId || sa.boat_id || sa.boat?.id,
            isPrimary: sa.isPrimary || sa.is_primary || false,
            maxCapacityOverride: sa.maxCapacityOverride || sa.max_capacity_override || null
          };
        })
      : [{
          boatId: '',
          isPrimary: true,
          maxCapacityOverride: null
        }];

    console.log('🚤 Extracted boats:', extractedBoats);

    const transformedData = {
      name: serviceData.name || '',
      description: serviceData.description || '',
      serviceTypeId: serviceData.serviceTypeId || serviceData.serviceType?.id || serviceData.service_type_id || '',
      duration: serviceData.duration || 480,

      // Routes
      routes: extractedRoutes,

      // Service assignments
      serviceAssignments: extractedBoats,
      
      // Inclusions and exclusions
      includedItems: serviceData.includedItems || [],
      excludedItems: serviceData.excludedItems || [],
      specialInstruction: serviceData.specialInstruction || '',
      
      // Pricing
      basePrice: parseFloat(serviceData.basePrice) || 0,
      pricingModel: pricingModelMapping[serviceData.pricingModel] || 'basic',
      agePricing: transformAgePricing(serviceData.serviceAgePricing),
      
      // Packages
      servicePackages: transformPackages(serviceData.servicePackages),
      
      // Schedule
      serviceSchedules: transformSchedules(serviceData.serviceSchedules),
      
      // Itinerary
      itinerary: transformItinerary(serviceData.itinerary),
      
      // Images
      images: serviceData.images || []
    };

    // Set existing images for display
    if (serviceData.images && Array.isArray(serviceData.images)) {
      setExistingImages(serviceData.images);
    }

    console.log('📊 Final transformed data:', transformedData);
    
    setFormData(transformedData);
  };

  // Helper functions for data transformation
  const transformAgePricing = (serviceAgePricing) => {
    if (!serviceAgePricing || !Array.isArray(serviceAgePricing)) return {};
    
    const agePricing = {};
    serviceAgePricing.forEach(pricing => {
      if (pricing.ageCategory) {
        const categoryKey = getStandardizedCategoryKey(pricing.ageCategory.name);
        agePricing[categoryKey] = parseFloat(pricing.price) || 0;
      }
    });
    return agePricing;
  };

  const transformPackages = (servicePackages) => {
    if (!servicePackages || !Array.isArray(servicePackages) || servicePackages.length === 0) {
      return formData.servicePackages; // Return default packages
    }
    
    return servicePackages.map(pkg => ({
      packageTypeId: pkg.packageTypeId,
      basePrice: parseFloat(pkg.basePrice) || 0,
      priceModifier: parseFloat(pkg.priceModifier) || 1.0,
      includedItems: pkg.includedItems || [],
      excludedItems: pkg.excludedItems || [],
      agePricing: pkg.agePricing ? JSON.parse(pkg.agePricing) : {}
    }));
  };

  const transformSchedules = (serviceSchedules) => {
    if (!serviceSchedules || !Array.isArray(serviceSchedules) || serviceSchedules.length === 0) {
      return formData.serviceSchedules; // Return default schedule
    }
    
    return serviceSchedules.map(schedule => ({
      dayOfWeek: schedule.dayOfWeek,
      departureTime: schedule.departureTime,
      availableCapacity: schedule.availableCapacity || 0
    }));
  };

  const transformItinerary = (itineraryData) => {
    if (!itineraryData) return formData.itinerary; // Return default itinerary

    try {
      const parsed = typeof itineraryData === 'string' ? JSON.parse(itineraryData) : itineraryData;
      return Array.isArray(parsed) ? parsed : formData.itinerary;
    } catch (error) {
      console.error('Error parsing itinerary:', error);
      return formData.itinerary;
    }
  };

  // Helper function to get standardized category key
  const getStandardizedCategoryKey = (categoryName) => {
    const mapping = {
      'Adult': 'adult',
      'Adults': 'adult',
      'Child': 'child',
      'Children': 'child',
      'Toddler': 'toddler',
      'Toddlers': 'toddler',
      'Senior': 'senior',
      'Seniors': 'senior',
      'PWD': 'pwd',
      'Person with Disability': 'pwd'
    };
    return mapping[categoryName] || categoryName.toLowerCase();
  };

  // Authentication check
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, isLoading, router]);

  // Fetch dropdown data
  useEffect(() => {
    if (isAuthenticated && accessToken) {
      fetchDropdownData();
      fetchRoutesData();
    }
  }, [isAuthenticated, accessToken]);

  // Store the service type ID temporarily if service types aren't loaded yet
  const [pendingServiceTypeId, setPendingServiceTypeId] = useState('');

  // Available routes and boats data
  const [availableRoutes, setAvailableRoutes] = useState([]);
  const [availableBoats, setAvailableBoats] = useState([]);

  // Debug: Log when availableRoutes changes
  useEffect(() => {
    console.log('📊 availableRoutes updated:', availableRoutes.length, 'routes');
    if (availableRoutes.length > 0) {
      console.log('📊 First route:', availableRoutes[0]);
    }
  }, [availableRoutes]);

  // Filter routes based on search term
  const filteredRoutes = Array.isArray(availableRoutes) ? availableRoutes.filter(route => {
    const routeText = `${route.departureJetty?.name} → ${route.destination?.name}`.toLowerCase();
    return routeText.includes(routesSearchTerm.toLowerCase()) &&
           !formData.routes.includes(route.id);
  }) : [];

  // Handle route selection
  const handleRouteSelect = (routeId) => {
    if (!formData.routes.includes(routeId)) {
      setFormData(prev => ({
        ...prev,
        routes: [...prev.routes, routeId]
      }));
    }
    setRoutesSearchTerm('');
    setRoutesDropdownOpen(false);
  };

  // Handle route removal
  const handleRouteRemove = (routeIdToRemove) => {
    setFormData(prev => ({
      ...prev,
      routes: prev.routes.filter(routeId => routeId !== routeIdToRemove)
    }));
  };

  // Update boat assignment
  const updateBoatAssignment = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      serviceAssignments: prev.serviceAssignments.map((assignment, i) => 
        i === index ? { ...assignment, [field]: value } : assignment
      )
    }));
  };

  // Effect to apply pending service type ID once service types are loaded
  useEffect(() => {
    if (serviceTypes.length > 0 && pendingServiceTypeId && !formData.serviceTypeId) {
      console.log('🔄 Applying pending service type ID:', pendingServiceTypeId);
      setFormData(prev => ({
        ...prev,
        serviceTypeId: pendingServiceTypeId
      }));
      setPendingServiceTypeId('');
    }
  }, [serviceTypes, pendingServiceTypeId, formData.serviceTypeId]);

  // Close routes dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (routesDropdownOpen && !event.target.closest('.routes-dropdown')) {
        setRoutesDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [routesDropdownOpen]);

  const fetchDropdownData = async () => {
    try {
      const [serviceTypesRes, boatsRes, packageTypesRes, ageCategoriesRes] = await Promise.all([
        fetch('/api/services/types', {
          headers: { 'Authorization': `Bearer ${accessToken}` }
        }),
        fetch('/api/boat-owner/boats', {
          headers: { 'Authorization': `Bearer ${accessToken}` }
        }),
        fetch('/api/services/config/package-types', {
          headers: { 'Authorization': `Bearer ${accessToken}` }
        }),
        fetch('/api/services/config/age-categories', {
          headers: { 'Authorization': `Bearer ${accessToken}` }
        })
      ]);

      const [serviceTypesData, boatsData, packageTypesData, ageCategoriesData] = await Promise.all([
        serviceTypesRes.json(),
        boatsRes.json(),
        packageTypesRes.json(),
        ageCategoriesRes.json()
      ]);

      if (serviceTypesData.success !== false) {
        setServiceTypes(serviceTypesData.data || serviceTypesData);
      }
      if (boatsData.success && Array.isArray(boatsData.data)) {
        setBoats(boatsData.data);
        setAvailableBoats(boatsData.data);
      }
      if (packageTypesData.success) setPackageTypes(packageTypesData.data);
      if (ageCategoriesData.success) setAgeCategories(ageCategoriesData.data);
    } catch (error) {
      console.error('Error fetching dropdown data:', error);
    }
  };

  // Fetch routes data separately since it might come from jetties or search
  const fetchRoutesData = async () => {
    try {
      console.log('🚀 Fetching routes data...');
      // For now, let's fetch all jetties and then get their routes
      const jettiesRes = await fetch('/api/jetties', {
        headers: { 'Authorization': `Bearer ${accessToken}` }
      });

      if (jettiesRes.ok) {
        const jettiesData = await jettiesRes.json();
        console.log('🏝️ Fetched jetties:', jettiesData.data?.length);
        if (jettiesData.success && Array.isArray(jettiesData.data)) {
          // Get routes for each jetty and combine them
          const allRoutes = [];
          for (const jetty of jettiesData.data) {
            try {
              const routesRes = await fetch(`/api/jetties/${jetty.id}/destinations`, {
                headers: { 'Authorization': `Bearer ${accessToken}` }
              });
              if (routesRes.ok) {
                const routesData = await routesRes.json();
                if (routesData.success && Array.isArray(routesData.data)) {
                  console.log(`📍 Fetched ${routesData.data.length} routes for jetty ${jetty.name}`);
                  allRoutes.push(...routesData.data);
                }
              }
            } catch (err) {
              console.error(`Error fetching routes for jetty ${jetty.id}:`, err);
            }
          }
          console.log('✅ Total routes fetched:', allRoutes.length);
          console.log('📍 Route IDs:', allRoutes.map(r => r.id));
          setRoutes(allRoutes);
          setAvailableRoutes(allRoutes);
        }
      }
    } catch (error) {
      console.error('Error fetching routes data:', error);
    }
  };

  // Form validation
  const validateStep = (step) => {
    const newErrors = {};

    switch (step) {
      case 1: // Basic Information
        if (!formData.name.trim()) {
          newErrors.name = t('serviceNameRequired') || 'Service name is required';
        }
        if (!formData.serviceTypeId) {
          newErrors.serviceTypeId = t('serviceTypeRequired') || 'Service type is required';
        }
        if (!formData.duration || formData.duration <= 0) {
          newErrors.duration = t('durationRequired') || 'Duration is required';
        }
        break;

      case 2: // Routes & Boats
        if (formData.routes.length === 0) {
          newErrors.routes = t('routeRequired') || 'At least one route is required';
        }
        if (formData.serviceAssignments.length === 0 || !formData.serviceAssignments[0].boatId) {
          newErrors.serviceAssignments = t('boatRequired') || 'At least one boat is required';
        }
        break;

      case 4: // Pricing
        if (!formData.pricingModel) {
          newErrors.pricingModel = t('pricingModelRequired') || 'Pricing model is required';
        }

        if (formData.pricingModel === 'basic') {
          if (!formData.basePrice || formData.basePrice <= 0) {
            newErrors.basePrice = t('basePriceRequired') || 'Base price is required';
          }
        }

        if (formData.pricingModel === 'ageBased' || formData.pricingModel === 'packageAndAge') {
          const hasValidAgePricing = Object.values(formData.agePricing).some(price =>
            price && parseFloat(price) > 0
          );
          if (!hasValidAgePricing) {
            newErrors.agePricing = t('agePricingRequired') || 'At least one age category price is required';
          }
        }

        if (formData.pricingModel === 'packageOnly' || formData.pricingModel === 'packageAndAge') {
          const hasValidPackages = formData.servicePackages.some(pkg =>
            pkg.packageTypeId && pkg.basePrice > 0
          );
          if (!hasValidPackages) {
            newErrors.servicePackages = t('packageRequired') || 'At least one package is required';
          }
        }
        break;

      case 6: // Schedule
        if (formData.serviceSchedules.length === 0) {
          newErrors.serviceSchedules = t('scheduleRequired') || 'At least one schedule is required';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Navigation functions
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
      setErrors({});
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setErrors({});
    }
  };

  const goToStep = (step) => {
    if (step >= 1 && step <= totalSteps) {
      setCurrentStep(step);
      setErrors({});
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.back();
  };

  // Form input handlers
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleRouteChange = (routeId, isChecked) => {
    setFormData(prev => ({
      ...prev,
      routes: isChecked
        ? [...prev.routes, routeId]
        : prev.routes.filter(id => id !== routeId)
    }));
  };

  const handleBoatAssignmentChange = (index, field, value) => {
    updateBoatAssignment(index, field, value);
  };

  const addBoatAssignment = () => {
    setFormData(prev => ({
      ...prev,
      serviceAssignments: [
        ...prev.serviceAssignments,
        {
          boatId: '',
          isPrimary: false,
          maxCapacityOverride: null
        }
      ]
    }));
  };

  const removeBoatAssignment = (index) => {
    if (formData.serviceAssignments.length > 1) {
      setFormData(prev => ({
        ...prev,
        serviceAssignments: prev.serviceAssignments.filter((_, i) => i !== index)
      }));
    }
  };

  const handleIncludedItemChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      includedItems: prev.includedItems.map((item, i) => i === index ? value : item)
    }));
  };

  const addIncludedItem = () => {
    setFormData(prev => ({
      ...prev,
      includedItems: [...prev.includedItems, '']
    }));
  };

  const removeIncludedItem = (index) => {
    setFormData(prev => ({
      ...prev,
      includedItems: prev.includedItems.filter((_, i) => i !== index)
    }));
  };

  const handleExcludedItemChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      excludedItems: prev.excludedItems.map((item, i) => i === index ? value : item)
    }));
  };

  const addExcludedItem = () => {
    setFormData(prev => ({
      ...prev,
      excludedItems: [...prev.excludedItems, '']
    }));
  };

  const removeExcludedItem = (index) => {
    setFormData(prev => ({
      ...prev,
      excludedItems: prev.excludedItems.filter((_, i) => i !== index)
    }));
  };

  const handleAgePricingChange = (category, value) => {
    setFormData(prev => ({
      ...prev,
      agePricing: {
        ...prev.agePricing,
        [category]: parseFloat(value) || 0
      }
    }));
  };

  const handlePackageChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      servicePackages: prev.servicePackages.map((pkg, i) =>
        i === index ? { ...pkg, [field]: value } : pkg
      )
    }));
  };

  const handlePackageAgePricingChange = (packageIndex, category, value) => {
    setFormData(prev => ({
      ...prev,
      servicePackages: prev.servicePackages.map((pkg, i) =>
        i === packageIndex ? {
          ...pkg,
          agePricing: {
            ...pkg.agePricing,
            [category]: parseFloat(value) || 0
          }
        } : pkg
      )
    }));
  };

  const handlePackageIncludedItemChange = (packageIndex, itemIndex, value) => {
    setFormData(prev => ({
      ...prev,
      servicePackages: prev.servicePackages.map((pkg, i) =>
        i === packageIndex ? {
          ...pkg,
          includedItems: pkg.includedItems.map((item, j) => j === itemIndex ? value : item)
        } : pkg
      )
    }));
  };

  const addPackageIncludedItem = (packageIndex) => {
    setFormData(prev => ({
      ...prev,
      servicePackages: prev.servicePackages.map((pkg, i) =>
        i === packageIndex ? {
          ...pkg,
          includedItems: [...pkg.includedItems, '']
        } : pkg
      )
    }));
  };

  const removePackageIncludedItem = (packageIndex, itemIndex) => {
    setFormData(prev => ({
      ...prev,
      servicePackages: prev.servicePackages.map((pkg, i) =>
        i === packageIndex ? {
          ...pkg,
          includedItems: pkg.includedItems.filter((_, j) => j !== itemIndex)
        } : pkg
      )
    }));
  };

  const handleScheduleChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      serviceSchedules: prev.serviceSchedules.map((schedule, i) =>
        i === index ? { ...schedule, [field]: value } : schedule
      )
    }));
  };

  const addSchedule = () => {
    setFormData(prev => ({
      ...prev,
      serviceSchedules: [
        ...prev.serviceSchedules,
        {
          dayOfWeek: null,
          departureTime: '09:00',
          availableCapacity: 0
        }
      ]
    }));
  };

  const removeSchedule = (index) => {
    if (formData.serviceSchedules.length > 1) {
      setFormData(prev => ({
        ...prev,
        serviceSchedules: prev.serviceSchedules.filter((_, i) => i !== index)
      }));
    }
  };

  const handleItineraryChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      itinerary: prev.itinerary.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const addItineraryItem = () => {
    setFormData(prev => ({
      ...prev,
      itinerary: [
        ...prev.itinerary,
        {
          time: '09:00',
          activity: '',
          description: '',
          image: null
        }
      ]
    }));
  };

  const removeItineraryItem = (index) => {
    if (formData.itinerary.length > 1) {
      setFormData(prev => ({
        ...prev,
        itinerary: prev.itinerary.filter((_, i) => i !== index)
      }));
    }
  };

  const moveItineraryItem = (index, direction) => {
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex >= 0 && newIndex < formData.itinerary.length) {
      setFormData(prev => {
        const newItinerary = [...prev.itinerary];
        [newItinerary[index], newItinerary[newIndex]] = [newItinerary[newIndex], newItinerary[index]];
        return { ...prev, itinerary: newItinerary };
      });
    }
  };

  // Image handling functions
  const handleImageSelect = (e) => {
    const files = Array.from(e.target.files);
    if (files.length + selectedImages.length > 10) {
      alert(t('maxImagesAllowed', { max: 10 }));
      return;
    }

    setSelectedImages(prev => [...prev, ...files]);

    // Create previews
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, {
          file,
          url: e.target.result
        }]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingImage = (index) => {
    setExistingImages(prev => prev.filter((_, i) => i !== index));
  };

  const getImageUrl = (imagePath) => {
    if (!imagePath) return '';
    return imagePath.startsWith('http') ? imagePath : `/uploads/${imagePath}`;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateStep(currentStep)) {
      return;
    }

    // Check for zero-value pricing before submission
    const hasZeroValues = checkForZeroValues();
    if (hasZeroValues.hasZero && !hasZeroValues.confirmed) {
      setConfirmationModal({
        title: t('confirmZeroPricing') || 'Confirm Zero Pricing',
        message: t('zeroPricingWarning') || 'Some pricing values are set to zero. This means those categories will be free.',
        details: hasZeroValues.details,
        onConfirm: () => submitForm()
      });
      setShowConfirmationModal(true);
      return;
    }

    await submitForm();
  };

  const checkForZeroValues = () => {
    const zeroValues = [];

    if (formData.pricingModel === 'basic' && formData.basePrice === 0) {
      zeroValues.push('Base price is set to zero');
    }

    if (formData.pricingModel === 'ageBased' || formData.pricingModel === 'packageAndAge') {
      Object.entries(formData.agePricing).forEach(([category, price]) => {
        if (price === 0) {
          zeroValues.push(`${category} price is set to zero`);
        }
      });
    }

    if (formData.pricingModel === 'packageOnly' || formData.pricingModel === 'packageAndAge') {
      formData.servicePackages.forEach((pkg, index) => {
        if (pkg.basePrice === 0) {
          zeroValues.push(`Package ${index + 1} base price is set to zero`);
        }
        if (formData.pricingModel === 'packageAndAge') {
          Object.entries(pkg.agePricing || {}).forEach(([category, price]) => {
            if (price === 0) {
              zeroValues.push(`Package ${index + 1} ${category} price is set to zero`);
            }
          });
        }
      });
    }

    return {
      hasZero: zeroValues.length > 0,
      details: zeroValues.join('\n'),
      confirmed: false
    };
  };

  const submitForm = async () => {
    try {
      setLoading(true);

      // Transform frontend data to backend format
      const backendData = transformFormDataForBackend(formData);

      const response = await fetch(`/api/boat-owner/services/${serviceId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(backendData)
      });

      const result = await response.json();

      if (result.success) {
        setFeedbackModal({
          type: 'success',
          title: t('success') || 'Success',
          message: t('serviceUpdatedSuccessfully') || 'Service updated successfully',
          details: t('serviceUpdatedDetails') || 'Your service has been updated and is now available for booking.'
        });
        setShowFeedbackModal(true);

        // Navigate back to services list after a delay
        setTimeout(() => {
          router.push('/boat-owner/services');
        }, 2000);
      } else {
        throw new Error(result.message || 'Failed to update service');
      }
    } catch (error) {
      console.error('Error updating service:', error);
      setFeedbackModal({
        type: 'error',
        title: t('error') || 'Error',
        message: t('failedToUpdateService') || 'Failed to update service',
        details: error.message
      });
      setShowFeedbackModal(true);
    } finally {
      setLoading(false);
    }
  };

  // Transform frontend data to backend format
  const transformFormDataForBackend = (data) => {
    // Frontend to backend pricing model mapping
    const pricingModelMapping = {
      'basic': 'basic',
      'packageOnly': 'package_only',
      'ageBased': 'age_based',
      'packageAndAge': 'age_package_based'
    };

    return {
      name: data.name,
      description: data.description,
      serviceTypeId: data.serviceTypeId,
      duration: parseInt(data.duration),
      pricingModel: pricingModelMapping[data.pricingModel] || 'basic',
      basePrice: parseFloat(data.basePrice) || 0,
      includedItems: data.includedItems.filter(item => item.trim()),
      excludedItems: data.excludedItems.filter(item => item.trim()),
      specialInstruction: data.specialInstruction,

      // Routes
      routes: data.routes.map(routeId => ({ routeId })),

      // Service assignments
      serviceAssignments: data.serviceAssignments.filter(sa => sa.boatId).map(sa => ({
        boatId: sa.boatId,
        isPrimary: sa.isPrimary,
        maxCapacityOverride: sa.maxCapacityOverride ? parseInt(sa.maxCapacityOverride) : null
      })),

      // Age pricing
      serviceAgePricing: Object.entries(data.agePricing)
        .filter(([_, price]) => price > 0)
        .map(([category, price]) => ({
          ageCategoryId: `ac_${category}`,
          price: parseFloat(price)
        })),

      // Packages
      servicePackages: data.servicePackages
        .filter(pkg => pkg.packageTypeId && pkg.basePrice > 0)
        .map(pkg => ({
          packageTypeId: pkg.packageTypeId,
          basePrice: parseFloat(pkg.basePrice),
          priceModifier: parseFloat(pkg.priceModifier) || 1.0,
          includedItems: pkg.includedItems.filter(item => item.trim()),
          excludedItems: pkg.excludedItems.filter(item => item.trim()),
          agePricing: pkg.agePricing
        })),

      // Schedules
      serviceSchedules: data.serviceSchedules.map(schedule => ({
        dayOfWeek: schedule.dayOfWeek !== null ? parseInt(schedule.dayOfWeek) : null,
        departureTime: schedule.departureTime,
        availableCapacity: parseInt(schedule.availableCapacity) || 0
      })),

      // Itinerary
      itinerary: data.itinerary.filter(item => item.activity.trim())
    };
  };

  // Format category name for display
  const formatCategoryName = (categoryName) => {
    const mapping = {
      'adult': 'Adult',
      'child': 'Child',
      'toddler': 'Toddler',
      'senior': 'Senior',
      'pwd': 'PWD'
    };
    return mapping[categoryName] || categoryName;
  };

  // Show loading screen while fetching initial data
  if (initialLoading) {
    return (
      <>
        <Head>
          <title>Edit Service - GoSea</title>
          <meta name="description" content="Edit your boat service on GoSea" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-8 sm:px-0">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-500 mx-auto"></div>
                <p className="mt-4 text-gray-600">{t('loadingService') || 'Loading service data...'}</p>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  // Show error if service not found
  if (serviceNotFound) {
    return (
      <>
        <Head>
          <title>Service Not Found - GoSea</title>
          <meta name="description" content="Service not found" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-8 sm:px-0">
              <div className="text-center">
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                  {t('serviceNotFound') || 'Service Not Found'}
                </h1>
                <p className="text-gray-600 mb-6">
                  {t('serviceNotFoundMessage') || 'The service you are trying to edit could not be found.'}
                </p>
                <button
                  onClick={() => router.push('/boat-owner/services')}
                  className="px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
                >
                  {t('backToServices') || 'Back to Services'}
                </button>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  // Show loading if not authenticated
  if (isLoading || !isAuthenticated) {
    return (
      <>
        <Head>
          <title>Edit Service - GoSea</title>
          <meta name="description" content="Edit your boat service on GoSea" />
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-8 sm:px-0">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-500 mx-auto"></div>
                <p className="mt-4 text-gray-600">{t('loading') || 'Loading...'}</p>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  // Copy the renderStepContent function from create.js - this will be exactly the same
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-md lg:text-lg font-medium flex items-center text-gray-900">
                <Info className="w-5 h-5 mr-2" />
                {t('basicInformation')}
              </h3>
              <span className="text-xs text-red-500 italic">{t('requiredFields')}</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('serviceName')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder={t('enterServiceName')}
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="serviceTypeId" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('serviceType')} <span className="text-red-500">*</span>
                </label>
                <select
                  id="serviceTypeId"
                  name="serviceTypeId"
                  value={formData.serviceTypeId}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 bg-white ${
                    errors.serviceTypeId ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">{t('selectServiceType')}</option>
                  {serviceTypes.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
                {errors.serviceTypeId && (
                  <p className="mt-1 text-sm text-red-600">{errors.serviceTypeId}</p>
                )}
              </div>

              <div>
                <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('duration')} ({t('minutes')})
                </label>
                <input
                  type="number"
                  id="duration"
                  name="duration"
                  min="30"
                  step="30"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                  placeholder={t('serviceDuration')}
                />
              </div>

              <div className="md:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('description')} <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  value={formData.description}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                    errors.description ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder={t('describeYourService')}
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                )}
              </div>
              
              {/* Divider between basic info and images */}
              <div className="md:col-span-2">
                <div className="border-t border-dotted border-gray-200"></div>
              </div>
              
              {/* Images Section */}
              <div className="md:col-span-2">
                <h3 className="text-md font-medium mb-4 flex items-center text-gray-900">
                  <Image className="w-5 h-5 mr-2" />
                  {t('images')}
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('uploadServiceImages')}
                    </label>
                    <div className="flex items-center justify-center w-full">
                      <label className="flex flex-col items-center justify-center w-full h-24 border-2 border-dashed rounded-lg cursor-pointer border-gray-300 hover:border-gray-400">
                        <div className="flex flex-col items-center justify-center pt-3 pb-3">
                          <CloudArrowUpIcon className="w-6 h-6 mb-1 text-gray-400" />
                          <p className="text-xs text-gray-500">
                            <span className="font-semibold">{t('clickToUpload')}</span> {t('orDragAndDrop')}
                          </p>
                          <p className="text-[10px] text-gray-500">
                            {t('imageRequirements')}
                          </p>
                        </div>
                        <input
                          type="file"
                          ref={fileInputRef}
                          onChange={handleImageSelect}
                          multiple
                          accept="image/*"
                          className="hidden"
                        />
                      </label>
                    </div>
                    {errors.images && (
                      <p className="mt-1 text-sm text-red-600">{errors.images}</p>
                    )}
                  </div>
                  
                  {/* Existing Images */}
                  {existingImages.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('currentImages') || 'Current Images'}
                      </label>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                        {existingImages.map((image, index) => (
                          <div key={`existing-${index}`} className="relative">
                            <img
                              src={getImageUrl(image.imagePath || image.url || image)}
                              alt={`Existing ${index + 1}`}
                              className="w-full h-20 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeExistingImage(index)}
                              className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <XMarkIcon className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* New Image Previews */}
                  {imagePreviews.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('newImages') || 'New Images'}
                      </label>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                        {imagePreviews.map((preview, index) => (
                          <div key={`new-${index}`} className="relative">
                            <img
                              src={preview.url}
                              alt={`Preview ${index + 1}`}
                              className="w-full h-20 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeImage(index)}
                              className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <XMarkIcon className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        );

      case 2:
        return (
          <>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium flex items-center text-gray-900">
                <Navigation className="w-5 h-5 mr-2" />
                {t('routeAndBoat')}
              </h3>
              <span className="text-xs text-red-500 italic">{t('requiredFields')}</span>
            </div>
            <div className="space-y-8">
              {/* Routes Section */}
              <div>
                <h4 className="text-md font-medium mb-3 text-gray-800">{t('routes')}</h4>
                {/* Searchable Dropdown with side-by-side layout */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Select Routes Field */}
                  <div className="relative routes-dropdown">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('selectRoutes')} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={routesSearchTerm}
                        onChange={(e) => {
                          setRoutesSearchTerm(e.target.value);
                          setRoutesDropdownOpen(true);
                        }}
                        onFocus={() => setRoutesDropdownOpen(true)}
                        placeholder={t('searchForRoutes') || 'Search for routes...'}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.routes ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />

                      {routesDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                          {/* Available Routes */}
                          {filteredRoutes.length > 0 && (
                            <div>
                              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                                {t('availableRoutes') || 'Available Routes'}
                              </div>
                              {filteredRoutes.map((route) => (
                                <button
                                  key={route.id}
                                  type="button"
                                  onClick={() => handleRouteSelect(route.id)}
                                  className="w-full text-left px-4 py-2 hover:bg-sky-50 hover:text-sky-700 transition-colors text-sm"
                                >
                                  {route.departureJetty?.name} → {route.destination?.name}
                                </button>
                              ))}
                            </div>
                          )}

                          {/* No results */}
                          {filteredRoutes.length === 0 && !routesSearchTerm.trim() && (
                            <div className="px-4 py-3 text-sm text-gray-500">
                              {t('startTypingToSearchRoutes') || 'Start typing to search routes...'}
                            </div>
                          )}

                          {filteredRoutes.length === 0 && routesSearchTerm.trim() && (
                            <div className="px-4 py-3 text-sm text-gray-500">
                              {t('noRoutesFound') || 'No routes found'}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {errors.routes && (
                      <p className="mt-1 text-sm text-red-600">{errors.routes}</p>
                    )}
                  </div>

                  {/* Selected Routes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('selectedRoutes') || 'Selected Routes'}
                    </label>
                    {formData.routes.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {formData.routes.map((routeId) => {
                          console.log('🔍 Looking for route:', routeId, 'in', availableRoutes.length, 'available routes');
                          const route = availableRoutes.find(r => r.id === routeId);
                          console.log('🔍 Found route by ID:', route);

                          // If route not found by exact ID, try to find by any field that might match
                          const fallbackRoute = !route ? availableRoutes.find(r =>
                            r.id?.includes(routeId) ||
                            routeId?.includes(r.id) ||
                            r.routeId === routeId ||
                            r.route_id === routeId
                          ) : route;

                          console.log('🔍 Final route to display:', fallbackRoute);

                          return (route || fallbackRoute) ? (
                            <span
                              key={routeId}
                              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-sky-100 text-sky-800"
                            >
                              {(route || fallbackRoute).departureJetty?.name || 'Unknown'} → {(route || fallbackRoute).destination?.name || 'Unknown'}
                              <button
                                type="button"
                                onClick={() => handleRouteRemove(routeId)}
                                className="ml-2 text-sky-600 hover:text-sky-800"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </span>
                          ) : (
                            <span
                              key={routeId}
                              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-amber-100 text-amber-800"
                              title={`Route ID: ${routeId} not found in ${availableRoutes.length} available routes`}
                            >
                              {routeId} (Loading...)
                              <button
                                type="button"
                                onClick={() => handleRouteRemove(routeId)}
                                className="ml-2 text-amber-600 hover:text-amber-800"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </span>
                          );
                        })}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 italic">{t('noRoutesSelected') || 'No routes selected'}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Divider */}
              <div className="border-t border-dotted border-gray-200"></div>
              
              {/* Boats Section */}
              <div>
                <h4 className="text-md font-medium mb-3 text-gray-800">{t('boats')}</h4>
                <div className="space-y-4">
                  {formData.serviceAssignments.map((assignment, index) => (
                    <div key={index} className="border border-gray-100 bg-gray-50 rounded-lg p-4 relative">
                      <div className="grid grid-cols-1 md:grid-cols-[2fr_2fr_1fr] gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('boat')} <span className="text-red-500">*</span>
                          </label>
                          <select
                            value={assignment.boatId}
                            onChange={(e) => updateBoatAssignment(index, 'boatId', e.target.value)}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 bg-white ${
                              errors[`boat_${index}`] ? 'border-red-300' : 'border-gray-300'
                            }`}
                          >
                            <option value="">{t('selectBoat') || 'Select a boat'}</option>
                            {(availableBoats || []).map(boat => (
                              <option key={boat.id} value={boat.id}>
                                {boat.name} ({boat.capacity} {t('passengers') || 'passengers'})
                              </option>
                            ))}
                          </select>
                          {errors[`boat_${index}`] && (
                            <p className="mt-1 text-sm text-red-600">{errors[`boat_${index}`]}</p>
                          )}
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('capacityOverride') || 'Capacity Override'}
                          </label>
                          <input
                            type="number"
                            min="1"
                            value={assignment.maxCapacityOverride || ''}
                            onChange={(e) => updateBoatAssignment(index, 'maxCapacityOverride', e.target.value ? parseInt(e.target.value) : null)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                            placeholder={t('leaveEmptyForFullCapacity') || 'Leave empty for full capacity'}
                          />
                        </div>
                        
                        <div className="flex items-center">
                          <label className="flex items-center md:col-span-1">
                            <input
                              type="checkbox"
                              checked={assignment.isPrimary}
                              onChange={(e) => updateBoatAssignment(index, 'isPrimary', e.target.checked)}
                              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500 h-4 w-4"
                            />
                            <span className="ml-2 text-gray-700 text-sm">{t('primaryBoat') || 'Primary Boat'}</span>
                          </label>
                        </div>
                      </div>
                      
                      {formData.serviceAssignments.length > 1 && (
                        <div className="absolute top-2 right-2 group">
                          <button
                            type="button"
                            onClick={() => removeBoatAssignment(index)}
                            className="p-1 rounded text-red-500 hover:text-red-600 hover:bg-red-50 transition-colors"
                            title={t('removeBoat') || 'Remove boat'}
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  <button
                    type="button"
                    onClick={addBoatAssignment}
                    className="flex items-center text-sky-600 hover:text-sky-800 text-sm"
                  >
                    <PlusIcon className="h-4 w-4 mr-1" />
                    {t('addBoat') || 'Add Boat'}
                  </button>
                </div>
              </div>
            </div>
          </>
        );

      case 3:
        return (
          <div className="text-center py-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              <ListChecks className="w-5 h-5 mr-2 inline" />
              Inclusions & Exclusions
            </h3>
            <p className="text-gray-600">
              Inclusions and exclusions editing will be implemented
            </p>
          </div>
        );

      case 4:
        return (
          <div className="text-center py-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              <CircleDollarSign className="w-5 h-5 mr-2 inline" />
              Pricing
            </h3>
            <p className="text-gray-600">
              Pricing management will be implemented
            </p>
          </div>
        );

      case 5:
        return (
          <div className="text-center py-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              <Package className="w-5 h-5 mr-2 inline" />
              Packages
            </h3>
            <p className="text-gray-600">
              Package management will be implemented
            </p>
          </div>
        );

      case 6:
        return (
          <div className="text-center py-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              <CalendarClock className="w-5 h-5 mr-2 inline" />
              Schedule
            </h3>
            <p className="text-gray-600">
              Schedule management will be implemented
            </p>
          </div>
        );

      case 7:
        return (
          <div className="text-center py-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              <Image className="w-5 h-5 mr-2 inline" />
              Itinerary
            </h3>
            <p className="text-gray-600">
              Itinerary management will be implemented
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  // Main component render
  return (
    <>
      <Head>
        <title>Edit Service - GoSea</title>
        <meta name="description" content="Edit your boat service on GoSea" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Header */}
            <div className="mb-8">
              <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                <span onClick={() => router.push('/boat-owner/services')} className="hover:text-sky-600 cursor-pointer">
                  {t('myServices') || 'My Services'}
                </span>
                <span>/</span>
                <span onClick={() => router.push(`/boat-owner/services/${serviceId}`)} className="hover:text-sky-600 cursor-pointer">
                  {formData.name || t('serviceDetails') || 'Service Details'}
                </span>
                <span>/</span>
                <span className="text-gray-900">{t('edit') || 'Edit'}</span>
              </nav>
              <h1 className="text-2xl font-bold text-gray-900">Edit Service</h1>
            </div>

            {/* Progress Steps - copied exactly from create.js */}
            <div className="mb-4 lg:mb-8">
              <div className="flex justify-between relative">
                {/* Background line */}
                <div className="absolute top-3 md:top-4 left-0 right-0 h-0.5 bg-gray-200 -z-10"></div>
                {/* Progress line */}
                <div 
                  className="absolute top-3 md:top-4 left-0 h-0.5 bg-emerald-500 -z-10 transition-all duration-300"
                  style={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
                ></div>
                {[...Array(totalSteps)].map((_, i) => {
                  const stepNumber = i + 1;
                  const isCompleted = stepNumber < currentStep;
                  const isCurrent = stepNumber === currentStep;

                  // Define step icons
                  const getStepIcon = (step) => {
                    switch (step) {
                      case 1: return <Info className="w-3 h-3 md:w-4 md:h-4" />;
                      case 2: return <Navigation className="w-3 h-3 md:w-4 md:h-4" />;
                      case 3: return <ListChecks className="w-3 h-3 md:w-4 md:h-4" />;
                      case 4: return <CircleDollarSign className="w-3 h-3 md:w-4 md:h-4" />;
                      case 5: return <Package className="w-3 h-3 md:w-4 md:h-4" />;
                      case 6: return <CalendarClock className="w-3 h-3 md:w-4 md:h-4" />;
                      case 7: return <Image className="w-3 h-3 md:w-4 md:h-4" />;
                      default: return stepNumber;
                    }
                  };

                  return (
                    <div key={stepNumber} className="flex flex-col items-center relative" style={{ flex: 1 }}>
                      {/* Connecting lines between numbers */}
                      {stepNumber < totalSteps && (
                        <div className={`absolute top-3 md:top-4 h-0.5 -z-5 ${stepNumber < currentStep ? 'bg-emerald-500' : 'bg-gray-200'}`} 
                             style={{ 
                               left: '50%', 
                               width: '100%', 
                               marginLeft: '0.75rem', 
                               marginRight: '0.75rem' 
                             }}></div>
                      )}
                      <div className={`w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center relative z-10 cursor-pointer ${
                        isCurrent
                          ? 'bg-emerald-500 text-white'
                          : isCompleted
                            ? 'bg-emerald-500 text-white'
                            : 'bg-white border-2 border-gray-300 text-gray-500'
                      }`}
                      onClick={() => goToStep(stepNumber)}
                      >
                        {isCompleted ? (
                          <Check className="w-3 h-3 md:w-4 md:h-4 text-white" />
                        ) : (
                          getStepIcon(stepNumber)
                        )}
                      </div>
                      <div className="mt-1 md:mt-2 text-[10px] md:text-xs text-center text-gray-600 w-full px-1">
                        {i === 0 ? t('step1') || 'Basic Info' :
                         i === 1 ? t('routeAndBoat') || 'Routes' :
                         i === 2 ? t('inclusions') || 'Inclusions' :
                         i === 3 ? t('pricing') || 'Pricing' :
                         i === 4 ? t('packages') || 'Packages' :
                         i === 5 ? t('schedule') || 'Schedule' :
                         i === 6 ? t('itinerary') || 'Itinerary' : ''}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                  {renderStepContent()}
                </div>
              </div>

              {/* Navigation Buttons - copied exactly from create.js */}
              <div className="flex justify-between space-x-4">
                <div>
                  {currentStep > 1 && (
                    <button
                      type="button"
                      onClick={prevStep}
                      className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                    >
                      <ArrowLeftIcon className="h-4 w-4 mr-1" />
                      {t('previous')}
                    </button>
                  )}
                </div>

                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                  >
                    {t('cancel')}
                  </button>

                  {currentStep < totalSteps ? (
                    <button
                      type="button"
                      onClick={() => {
                        console.log('📱 Next button clicked - current step:', currentStep);
                        if (validateStep(currentStep)) {
                          console.log('✅ Step validation passed, calling nextStep()');
                          nextStep();
                        } else {
                          console.log('❌ Step validation failed');
                        }
                      }}
                      className="flex items-center px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors text-sm"
                    >
                      {t('next')}
                      <ArrowRightIcon className="h-4 w-4 ml-1" />
                    </button>
                  ) : (
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
                    >
                      {loading ? t('updating') : t('updateService')}
                    </button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </main>

        <Footer />
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />

      {/* Confirmation Modal for Zero-Value Pricing */}
      <StandardModal
        isOpen={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        type="warning"
        title={confirmationModal.title}
        message={confirmationModal.message}
        details={confirmationModal.details}
        primaryButton={
          <button
            onClick={() => {
              setShowConfirmationModal(false);
              if (confirmationModal.onConfirm) {
                confirmationModal.onConfirm();
              }
            }}
            className="px-4 py-2 text-sm font-medium rounded-lg transition-colors bg-amber-600 hover:bg-amber-700 text-white"
          >
            {t('continue')}
          </button>
        }
        secondaryButton={
          <button
            onClick={() => {
              setShowConfirmationModal(false);
              // Reset navigation flag when user cancels
              setIsNavigating(false);
            }}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
          >
            {t('cancel')}
          </button>
        }
        showCloseButton={false}
      />
    </>
  );
};

export default EditService;
