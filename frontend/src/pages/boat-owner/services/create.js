import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import StandardModal from '../../../components/StandardModal';
import CustomTimePicker from '../../../components/CustomTimePicker';
import { CloudArrowUpIcon, XMarkIcon, PlusIcon, MinusIcon, ArrowLeftIcon, ArrowRightIcon, ClockIcon, TrashIcon, ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';
import { Info, Navigation, Check, Package, CircleDollarSign, ListChecks, CalendarClock, Image } from 'lucide-react';

const CreateService = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, accessToken, getCurrentUser } = useAuth();
  const { t } = useLanguage();
  const fileInputRef = useRef(null);

  // Wizard state
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 7; // Added step 7 for itinerary

  // Form data state
  const [formData, setFormData] = useState({
    // Step 1: Basic Information
    name: '',
    description: '',
    serviceTypeId: '',
    duration: 480, // 8 hours default

    // Step 2: Routes & Boats
    routes: [],
    serviceAssignments: [
      {
        boatId: '',
        isPrimary: true,
        maxCapacityOverride: null
      }
    ],

    // Step 3: Inclusions & Exclusions (moved from step 6)
    includedItems: [
      '7 checkpoints',
      '2 ways boat transfer',
      'Snorkeling equipment',
      'Experienced guide',
      'Safety jackets',
      'Lunch box'
    ],
    excludedItems: [
      'Jetty parking fee',
      'Jetty access fee by Majlis Daerah (children under 5 years free)'
    ],
    specialInstruction: '',

    // Step 4: Pricing (moved from step 5)
    basePrice: 80.00,
    pricingModel: '', // 'basic', 'packageOnly', 'ageBased', 'packageAndAge' - now required
    agePricing: {}, // For age-based pricing

    // Step 5: Packages (moved from step 4, conditional)
    servicePackages: [
      {
        packageTypeId: 'pt_regular', // Regular package
        basePrice: 100.00,
        priceModifier: 1.0,
        includedItems: [
          '7 checkpoints',
          '2 ways boat transfer',
          'Snorkeling equipment',
          'Experienced guide',
          'Safety jackets',
          'Lunch box'
        ],
        excludedItems: [
          'Jetty parking fee',
          'Jetty access fee by Majlis Daerah (children under 5 years free)'
        ],
        agePricing: {
          adult: 100.00,
          child: 80.00,
          toddler: 0.00,
          senior: 80.00,
          pwd: 80.00
        }
      },
      {
        packageTypeId: 'pt_premium', // Premium package
        basePrice: 110.00,
        priceModifier: 1.5,
        includedItems: [
          '7 checkpoints',
          '2 ways boat transfer',
          'Snorkeling equipment & Insurance',
          'Experienced guide',
          'Safety jackets',
          'Breakfast at jetty',
          'Lunch buffet, unlimited drinks and hi-teas'
        ],
        excludedItems: [
          'Jetty parking fee',
          'Jetty access fee by Majlis Daerah (children under 5 years free)'
        ],
        agePricing: {
          adult: 130.00,
          child: 110.00,
          toddler: 0.00,
          senior: 110.00,
          pwd: 110.00
        }
      }
    ],

    // Step 6: Schedule (moved from step 7)
    serviceSchedules: [
      {
        dayOfWeek: null, // Daily
        departureTime: '08:00',
        availableCapacity: 18
      }
    ],

    // Step 7: Itinerary (optional)
    itinerary: [],

    // Images
    images: []
  });

  const [selectedImages, setSelectedImages] = useState([]);
  const [imagePreviews, setImagePreviews] = useState([]);
  const [availableBoats, setAvailableBoats] = useState([]);
  const [availableJetties, setAvailableJetties] = useState([]);
  const [availableDestinations, setAvailableDestinations] = useState([]);
  const [availableRoutes, setAvailableRoutes] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [packageTypes, setPackageTypes] = useState([]);
  const [ageCategories, setAgeCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [itineraryTimeErrors, setItineraryTimeErrors] = useState({});

  // Routes searchable dropdown state
  const [routesDropdownOpen, setRoutesDropdownOpen] = useState(false);
  const [routesSearchTerm, setRoutesSearchTerm] = useState('');

  // Inclusions searchable dropdown state
  const [inclusionsDropdownOpen, setInclusionsDropdownOpen] = useState(false);
  const [inclusionsSearchTerm, setInclusionsSearchTerm] = useState('');

  // Exclusions searchable dropdown state
  const [exclusionsDropdownOpen, setExclusionsDropdownOpen] = useState(false);
  const [exclusionsSearchTerm, setExclusionsSearchTerm] = useState('');

  // Package inclusions/exclusions searchable dropdown state
  const [packageInclusionsDropdownOpen, setPackageInclusionsDropdownOpen] = useState({});
  const [packageInclusionsSearchTerm, setPackageInclusionsSearchTerm] = useState({});
  const [packageExclusionsDropdownOpen, setPackageExclusionsDropdownOpen] = useState({});
  const [packageExclusionsSearchTerm, setPackageExclusionsSearchTerm] = useState({});

  // Feedback modal state
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState({
    type: 'success',
    title: '',
    message: '',
    details: null
  });

  // Confirmation modal state
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [confirmationModal, setConfirmationModal] = useState({
    title: '',
    message: '',
    details: '',
    onConfirm: null
  });

  // Navigation state to prevent auto-submission during step changes
  const [isNavigating, setIsNavigating] = useState(false);

  // Helper function to format category names with proper capitalization
  const formatCategoryName = (categoryName) => {
    if (!categoryName || typeof categoryName !== 'string') return categoryName;
    
    // Special case: PWD should always be uppercase
    if (categoryName.toLowerCase() === 'pwd') {
      return 'PWD';
    }
    
    // Capitalize first letter and make rest lowercase
    return categoryName.charAt(0).toUpperCase() + categoryName.slice(1).toLowerCase();
  };

  // Helper function to get standardized category key to prevent duplicates
  const getStandardizedCategoryKey = (categoryKey) => {
    if (!categoryKey || typeof categoryKey !== 'string') return categoryKey;
    // Convert to lowercase and remove trailing 's' to standardize keys like 'toddler/toddlers'
    return categoryKey.toLowerCase().replace(/s$/, '');
  };

  // Helper functions for feedback modals
  const showSuccessModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'success',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const showErrorModal = (title, message, details = null) => {
    setFeedbackModal({
      type: 'error',
      title,
      message,
      details
    });
    setShowFeedbackModal(true);
  };

  const displayConfirmationModal = (title, message, onConfirm, details = '') => {
    setConfirmationModal({
      title,
      message,
      details,
      onConfirm
    });
    setShowConfirmationModal(true);
  };

  // Redirect if not authenticated or not a boat owner
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== 'BOAT_OWNER' || !user?.isApproved)) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, isLoading, router]);

  // Refresh user data to ensure provider relationship is loaded
  useEffect(() => {
    const refreshUserData = async () => {
      if (isAuthenticated && accessToken && !user?.provider) {
        console.log('🔄 Refreshing user data to load provider relationship...');
        try {
          const updatedUser = await getCurrentUser();
          if (updatedUser?.provider) {
            console.log('✅ User data refreshed with provider:', updatedUser.provider.id);
          } else {
            console.log('⚠️ User data refreshed but no provider found');
          }
        } catch (error) {
          console.error('❌ Failed to refresh user data:', error);
        }
      }
    };

    refreshUserData();
  }, [isAuthenticated, accessToken, user?.provider, getCurrentUser]);

  // Fetch available data
  useEffect(() => {
    const fetchData = async () => {
      if (!accessToken) return;

      try {
        // Fetch boats
        const boatsResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/boats?limit=100`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (boatsResponse.ok) {
          const boatsData = await boatsResponse.json();
          setAvailableBoats(boatsData.data);
        }

        // Fetch jetties
        const jettiesResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/jetties`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (jettiesResponse.ok) {
          const jettiesData = await jettiesResponse.json();
          setAvailableJetties(jettiesData.data);
        }

        // Fetch destinations
        const destinationsResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/search/destinations`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (destinationsResponse.ok) {
          const destinationsData = await destinationsResponse.json();
          setAvailableDestinations(destinationsData.data);
        }

        // Fetch routes
        const routesResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/search/routes`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (routesResponse.ok) {
          const routesData = await routesResponse.json();
          setAvailableRoutes(routesData.data);
        }

        // Fetch service types
        const serviceTypesResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/services/types`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (serviceTypesResponse.ok) {
          const serviceTypesData = await serviceTypesResponse.json();
          setServiceTypes(serviceTypesData.data);
        }

        // Fetch package types
        const packageTypesResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/services/config/package-types`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (packageTypesResponse.ok) {
          const packageTypesData = await packageTypesResponse.json();
          setPackageTypes(packageTypesData.data);
        }

        // Fetch age categories
        const ageCategoriesResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/services/config/age-categories`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (ageCategoriesResponse.ok) {
          const ageCategoriesData = await ageCategoriesResponse.json();
          setAgeCategories(ageCategoriesData.data);
        } else {
          // Fallback to hardcoded categories if API fails
          setAgeCategories([
            { id: 'age_adult', name: 'Adult', code: 'ADULT', minAge: 18, maxAge: null, sortOrder: 1 },
            { id: 'age_child', name: 'Child', code: 'CHILD', minAge: 3, maxAge: 17, sortOrder: 2 },
            { id: 'age_toddler', name: 'Toddler', code: 'TODDLER', minAge: 0, maxAge: 2, sortOrder: 3 },
            { id: 'age_senior', name: 'Senior', code: 'SENIOR', minAge: 65, maxAge: null, sortOrder: 4 },
            { id: 'age_pwd', name: 'PWD', code: 'PWD', minAge: null, maxAge: null, sortOrder: 5 }
          ]);
        }
      } catch (error) {
        console.error('Fetch data error:', error);
        // Set fallback age categories if fetch fails
        setAgeCategories([
          { id: 'age_adult', name: 'Adult', code: 'ADULT', minAge: 18, maxAge: null, sortOrder: 1 },
          { id: 'age_child', name: 'Child', code: 'CHILD', minAge: 3, maxAge: 17, sortOrder: 2 },
          { id: 'age_toddler', name: 'Toddler', code: 'TODDLER', minAge: 0, maxAge: 2, sortOrder: 3 },
          { id: 'age_senior', name: 'Senior', code: 'SENIOR', minAge: 65, maxAge: null, sortOrder: 4 },
          { id: 'age_pwd', name: 'PWD', code: 'PWD', minAge: null, maxAge: null, sortOrder: 5 }
        ]);
      }
    };

    if (isAuthenticated && user?.role === 'BOAT_OWNER' && accessToken) {
      fetchData();
    }
  }, [isAuthenticated, user, accessToken]);

  // Sync age categories with existing packages when age categories are loaded
  useEffect(() => {
    if (ageCategories.length > 0) {
      setFormData(prev => ({
        ...prev,
        servicePackages: prev.servicePackages.map(pkg => {
          const updatedAgePricing = { ...pkg.agePricing };
          
          // Add missing age categories with default value 0
          ageCategories.forEach(category => {
            const categoryKey = category.code.toLowerCase();
            if (!updatedAgePricing.hasOwnProperty(categoryKey)) {
              updatedAgePricing[categoryKey] = 0;
            }
          });
          
          return {
            ...pkg,
            agePricing: updatedAgePricing
          };
        })
      }));
    }
  }, [ageCategories]);

  // Filter routes based on search term
  const filteredRoutes = availableRoutes.filter(route => {
    const routeText = `${route.departureJetty?.name} → ${route.destination?.name}`.toLowerCase();
    return routeText.includes(routesSearchTerm.toLowerCase()) &&
           !formData.routes.includes(route.id);
  });

  // Handle route selection
  const handleRouteSelect = (routeId) => {
    if (!formData.routes.includes(routeId)) {
      setFormData(prev => ({
        ...prev,
        routes: [...prev.routes, routeId]
      }));
    }
    setRoutesSearchTerm('');
    setRoutesDropdownOpen(false);
  };

  // Handle route removal
  const handleRouteRemove = (routeIdToRemove) => {
    setFormData(prev => ({
      ...prev,
      routes: prev.routes.filter(routeId => routeId !== routeIdToRemove)
    }));
  };

  // Predefined inclusions list
  const predefinedInclusions = [
    'Boat transfer',
    '2-way boat transfer',
    'Snorkeling equipment',
    'Life jackets',
    'Safety equipment',
    'Experienced guide',
    'Professional guide',
    'Lunch box',
    'Lunch buffet',
    'Breakfast',
    'Refreshments',
    'Unlimited drinks',
    'Hi-tea',
    'Fresh towels',
    'Underwater camera',
    'Waterproof bag',
    'First aid kit',
    'Insurance coverage',
    'Multiple checkpoints',
    '7 checkpoints',
    'Island hopping',
    'Beach activities',
    'Swimming time',
    'Free time on island',
    'Restroom facilities'
  ];

  // Predefined exclusions list
  const predefinedExclusions = [
    'Jetty parking fee',
    'Jetty access fee',
    'Marine park fee',
    'Conservation fee',
    'Personal expenses',
    'Alcoholic beverages',
    'Additional meals',
    'Accommodation',
    'Transportation to jetty',
    'Gratuities/tips',
    'Travel insurance',
    'Underwater photos',
    'Video recording',
    'Personal snorkeling gear',
    'Wetsuit rental',
    'Locker rental',
    'Shower facilities',
    'Extra activities',
    'Souvenir purchases',
    'Medical expenses'
  ];

  // Filter inclusions based on search term
  const filteredInclusions = predefinedInclusions.filter(inclusion =>
    inclusion.toLowerCase().includes(inclusionsSearchTerm.toLowerCase()) &&
    !formData.includedItems.includes(inclusion)
  );

  // Filter exclusions based on search term
  const filteredExclusions = predefinedExclusions.filter(exclusion =>
    exclusion.toLowerCase().includes(exclusionsSearchTerm.toLowerCase()) &&
    !formData.excludedItems.includes(exclusion)
  );

  // Handle inclusion selection
  const handleInclusionSelect = (inclusion) => {
    if (!formData.includedItems.includes(inclusion)) {
      setFormData(prev => ({
        ...prev,
        includedItems: [...prev.includedItems, inclusion]
      }));

      // Clear error when user adds an inclusion
      if (errors.includedItems) {
        setErrors(prev => ({
          ...prev,
          includedItems: ''
        }));
      }
    }
    setInclusionsSearchTerm('');
    setInclusionsDropdownOpen(false);
  };

  // Handle inclusion removal
  const handleInclusionRemove = (inclusionToRemove) => {
    setFormData(prev => {
      const newIncludedItems = prev.includedItems.filter(item => item !== inclusionToRemove);
      
      // If removing this item would make the list empty and there's no current error, 
      // we don't automatically add an error here since validation happens on step navigation
      return {
        ...prev,
        includedItems: newIncludedItems
      };
    });
  };

  // Handle exclusion selection
  const handleExclusionSelect = (exclusion) => {
    if (!formData.excludedItems.includes(exclusion)) {
      setFormData(prev => ({
        ...prev,
        excludedItems: [...prev.excludedItems, exclusion]
      }));
    }
    setExclusionsSearchTerm('');
    setExclusionsDropdownOpen(false);
  };

  // Handle exclusion removal
  const handleExclusionRemove = (exclusionToRemove) => {
    setFormData(prev => ({
      ...prev,
      excludedItems: prev.excludedItems.filter(item => item !== exclusionToRemove)
    }));
  };

  // Package inclusion handlers
  const handlePackageInclusionSelect = (packageIndex, inclusion) => {
    setFormData(prev => {
      const newPackages = [...prev.servicePackages];
      if (!newPackages[packageIndex].includedItems.includes(inclusion)) {
        newPackages[packageIndex] = {
          ...newPackages[packageIndex],
          includedItems: [...newPackages[packageIndex].includedItems, inclusion]
        };
      }
      return {
        ...prev,
        servicePackages: newPackages
      };
    });

    // Clear search term and close dropdown
    setPackageInclusionsSearchTerm(prev => ({ ...prev, [packageIndex]: '' }));
    setPackageInclusionsDropdownOpen(prev => ({ ...prev, [packageIndex]: false }));
  };

  const handlePackageInclusionRemove = (packageIndex, inclusionToRemove) => {
    setFormData(prev => {
      const newPackages = [...prev.servicePackages];
      newPackages[packageIndex] = {
        ...newPackages[packageIndex],
        includedItems: newPackages[packageIndex].includedItems.filter(item => item !== inclusionToRemove)
      };
      return {
        ...prev,
        servicePackages: newPackages
      };
    });
  };

  // Package exclusion handlers
  const handlePackageExclusionSelect = (packageIndex, exclusion) => {
    setFormData(prev => {
      const newPackages = [...prev.servicePackages];
      if (!newPackages[packageIndex].excludedItems.includes(exclusion)) {
        newPackages[packageIndex] = {
          ...newPackages[packageIndex],
          excludedItems: [...newPackages[packageIndex].excludedItems, exclusion]
        };
      }
      return {
        ...prev,
        servicePackages: newPackages
      };
    });

    // Clear search term and close dropdown
    setPackageExclusionsSearchTerm(prev => ({ ...prev, [packageIndex]: '' }));
    setPackageExclusionsDropdownOpen(prev => ({ ...prev, [packageIndex]: false }));
  };

  const handlePackageExclusionRemove = (packageIndex, exclusionToRemove) => {
    setFormData(prev => {
      const newPackages = [...prev.servicePackages];
      newPackages[packageIndex] = {
        ...newPackages[packageIndex],
        excludedItems: newPackages[packageIndex].excludedItems.filter(item => item !== exclusionToRemove)
      };
      return {
        ...prev,
        servicePackages: newPackages
      };
    });
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => {
        const newFormData = {
          ...prev,
          [name]: type === 'checkbox' ? checked : value
        };

        // Special handling for pricing model changes
        if (name === 'pricingModel') {
          // If switching to 'packageOnly', we don't need to clear age pricing from packages
          // because they might switch back to 'packageAndAge' later
          // The UI will just hide the age pricing section
        }

        return newFormData;
      });
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleNestedInputChange = (path, value) => {
    setFormData(prev => {
      const newData = { ...prev };
      const keys = path.split('.');
      let current = newData;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newData;
    });
  };

  const handleImageSelect = (e) => {
    const files = Array.from(e.target.files);
    if (files.length + selectedImages.length > 10) {
      alert(t('maxImagesAllowed', { max: 10 }));
      return;
    }

    setSelectedImages(prev => [...prev, ...files]);

    // Create previews
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, {
          file,
          url: e.target.result
        }]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  // Wizard navigation with conditional logic
  const nextStep = () => {
    // Debug logging
    console.log('nextStep called - current step:', currentStep);
    console.log('user provider id:', user?.provider?.id);

    // Set navigation flag to prevent auto-submission
    setIsNavigating(true);

    // Check for zero-value pricing in step 4 (Pricing step)
    if (currentStep === 4) {
      const pricingModel = formData.pricingModel;
      
      // Check for zero values in age-based pricing
      if (pricingModel === 'ageBased' && formData.agePricing) {
        const zeroValueCategories = Object.entries(formData.agePricing)
          .filter(([categoryId, price]) => price === 0 || price === '0')
          .map(([categoryId]) => {
            const category = ageCategories.find(cat => cat.id === categoryId);
            const categoryName = category ? category.name : categoryId;
            return formatCategoryName(categoryName);
          });

        if (zeroValueCategories.length > 0) {
          const categoryNames = zeroValueCategories.join(', ');
          displayConfirmationModal(
            t('zeroValuePricingTitle') || 'Confirm Free Pricing',
            `${t('zeroValuePricingMessage') || 'The following age categories have 0 value and will be regarded as FREE'}: ${categoryNames}. ${t('zeroValuePricingConfirm') || 'Do you want to continue?'}`,
            () => {
              // Continue with navigation after confirmation
              proceedToNextStep();
            },
            ''
          );
          // Don't proceed immediately, wait for user confirmation
          setTimeout(() => setIsNavigating(false), 100);
          return;
        }
      }

      // Check for zero values in basic pricing
      if (pricingModel === 'basic' && (formData.pricePerPerson === 0 || formData.pricePerPerson === '0')) {
        displayConfirmationModal(
          t('zeroValuePricingTitle') || 'Confirm Free Pricing',
          t('zeroValuePricingBasicMessage') || 'The price per person is set to 0 and will be regarded as FREE. Do you want to continue?',
          () => {
            // Continue with navigation after confirmation
            proceedToNextStep();
          },
          ''
        );
        // Don't proceed immediately, wait for user confirmation
        setTimeout(() => setIsNavigating(false), 100);
        return;
      }

      // Continue with normal pricing step logic
      proceedToNextStep();
      return;
    }

    // Check for zero-value pricing in step 5 (Packages step) for 'packageAndAge' model
    if (currentStep === 5) {
      const pricingModel = formData.pricingModel;
      
      // Check for zero values in package age-based pricing
      if (pricingModel === 'packageAndAge') {
        const allZeroValueCategories = [];
        
        // Check all packages for zero-value age categories
        formData.servicePackages.forEach((pkg, packageIndex) => {
          if (pkg.agePricing) {
            // Get all zero-value entries
            const zeroValueEntries = Object.entries(pkg.agePricing)
              .filter(([categoryKey, price]) => price === 0 || price === '0');
            
            // Map to unique category names using standardized keys to prevent duplicates
            const categoryNames = new Set();
            const standardizedKeys = new Set();
            
            zeroValueEntries.forEach(([categoryKey]) => {
              const standardizedKey = getStandardizedCategoryKey(categoryKey);
              
              // Only add if we haven't seen this standardized key before
              if (!standardizedKeys.has(standardizedKey)) {
                standardizedKeys.add(standardizedKey);
                
                // Find category by matching different possible formats
                const category = ageCategories.find(cat => 
                  cat.code.toLowerCase() === categoryKey.toLowerCase() ||
                  cat.id === categoryKey ||
                  getStandardizedCategoryKey(cat.code) === standardizedKey ||
                  getStandardizedCategoryKey(cat.name) === standardizedKey
                );
                
                const categoryName = category ? category.name : categoryKey;
                const formattedName = formatCategoryName(categoryName);
                
                categoryNames.add(formattedName);
              }
            });
            
            const uniqueCategories = Array.from(categoryNames);
            
            if (uniqueCategories.length > 0) {
              const packageType = packageTypes.find(type => type.id === pkg.packageTypeId);
              const packageName = packageType ? packageType.name : `Package ${packageIndex + 1}`;
              allZeroValueCategories.push({
                packageName,
                categories: uniqueCategories
              });
            }
          }
        });

        if (allZeroValueCategories.length > 0) {
          // Create a detailed message showing which packages and categories have zero values
          const detailMessages = allZeroValueCategories.map(item => 
            `${item.packageName}: ${item.categories.join(', ')}`
          ).join(' | ');
          
          displayConfirmationModal(
            t('zeroValuePricingTitle') || 'Confirm Free Pricing',
            `${t('zeroValuePricingMessage') || 'The following age categories have 0 value and will be regarded as FREE'}: ${detailMessages}. ${t('zeroValuePricingConfirm') || 'Do you want to continue?'}`,
            () => {
              // Continue with navigation after confirmation
              proceedToNextStep();
            },
            ''
          );
          
          // Don't proceed immediately, wait for user confirmation
          setTimeout(() => setIsNavigating(false), 100);
          return;
        }
      }

      // Continue with normal packages step logic
      proceedToNextStep();
      return;
    }

    // Normal navigation for other steps
    if (currentStep < totalSteps) {
      console.log('Moving to step:', currentStep + 1);
      setCurrentStep(currentStep + 1);
    }

    // Clear navigation flag after a short delay to allow React to re-render
    setTimeout(() => setIsNavigating(false), 100);
  };

  const proceedToNextStep = () => {
    // Conditional navigation based on pricing model (from step 4)
    if (currentStep === 4) {
      const pricingModel = formData.pricingModel;
      if (pricingModel === 'basic' || pricingModel === 'ageBased') {
        // Skip Packages step (5) and go directly to Schedule step (6)
        console.log('Skipping to step 6');
        setCurrentStep(6);
        // Clear navigation flag after a short delay to allow React to re-render
        setTimeout(() => setIsNavigating(false), 100);
        return;
      }
    }

    // Normal navigation
    if (currentStep < totalSteps) {
      console.log('Moving to step:', currentStep + 1);
      setCurrentStep(currentStep + 1);
    }

    // Clear navigation flag after a short delay to allow React to re-render
    setTimeout(() => setIsNavigating(false), 100);
  };

  const prevStep = () => {
    // Conditional navigation when going back
    if (currentStep === 7) { // From Itinerary step, always go back to Schedule step
      setCurrentStep(6);
      return;
    }
    
    if (currentStep === 6) { // From Schedule step
      const pricingModel = formData.pricingModel;
      if (pricingModel === 'basic' || pricingModel === 'ageBased') {
        // Skip Packages step (5) and go back to Pricing step (4)
        setCurrentStep(4);
        return;
      }
    }

    // Normal navigation
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Route management - removed old functions since we're using multiple select approach

  // Age-based pricing management
  const addAgeCategory = () => {
    if (ageCategories.length > 0) {
      const availableCategory = ageCategories.find(cat => !formData.agePricing.hasOwnProperty(cat.id));
      if (availableCategory) {
        setFormData(prev => ({
          ...prev,
          agePricing: {
            ...prev.agePricing,
            [availableCategory.id]: 0
          }
        }));
      }
    }
  };

  const removeAgeCategory = (categoryId) => {
    setFormData(prev => {
      const newAgePricing = { ...prev.agePricing };
      delete newAgePricing[categoryId];
      return {
        ...prev,
        agePricing: newAgePricing
      };
    });
  };

  const updateAgeCategoryPrice = (categoryId, price) => {
    setFormData(prev => ({
      ...prev,
      agePricing: {
        ...prev.agePricing,
        [categoryId]: price === '' ? '' : (typeof price === 'string' && price.indexOf('.') !== -1 ? price : (parseFloat(price) || 0))
      }
    }));
  };

  // Boat assignment management
  const addBoatAssignment = () => {
    setFormData(prev => ({
      ...prev,
      serviceAssignments: [...prev.serviceAssignments, {
        boatId: '',
        isPrimary: false,
        maxCapacityOverride: null
      }]
    }));
  };

  const removeBoatAssignment = (index) => {
    if (formData.serviceAssignments.length > 1) {
      setFormData(prev => ({
        ...prev,
        serviceAssignments: prev.serviceAssignments.filter((_, i) => i !== index)
      }));
    }
  };

  const updateBoatAssignment = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      serviceAssignments: prev.serviceAssignments.map((assignment, i) => 
        i === index ? { ...assignment, [field]: value } : assignment
      )
    }));
  };

  // Package management
  const addPackage = () => {
    // Create agePricing object based on available age categories
    const initialAgePricing = {};
    ageCategories.forEach(category => {
      initialAgePricing[category.code.toLowerCase()] = 0;
    });

    // Fallback to hardcoded categories if ageCategories is empty
    if (ageCategories.length === 0) {
      initialAgePricing.adult = 0;
      initialAgePricing.child = 0;
      initialAgePricing.toddler = 0;
      initialAgePricing.senior = 0;
      initialAgePricing.pwd = 0;
    }

    setFormData(prev => ({
      ...prev,
      servicePackages: [...prev.servicePackages, {
        packageTypeId: '',
        basePrice: 0,
        priceModifier: 1.0,
        includedItems: [],
        excludedItems: [],
        agePricing: initialAgePricing
      }]
    }));
  };

  const removePackage = (index) => {
    if (formData.servicePackages.length > 1) {
      setFormData(prev => ({
        ...prev,
        servicePackages: prev.servicePackages.filter((_, i) => i !== index)
      }));
    }
  };

  const updatePackage = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      servicePackages: prev.servicePackages.map((pkg, i) => 
        i === index ? { ...pkg, [field]: value } : pkg
      )
    }));
  };

  const updatePackageAgePricing = (packageIndex, ageCategory, value) => {
    setFormData(prev => {
      const newPackages = [...prev.servicePackages];
      newPackages[packageIndex] = {
        ...newPackages[packageIndex],
        agePricing: {
          ...newPackages[packageIndex].agePricing,
          [ageCategory]: value
        }
      };
      return {
        ...prev,
        servicePackages: newPackages
      };
    });
  };

  const removePackageAgeCategory = (packageIndex, ageCategory) => {
    setFormData(prev => {
      const newPackages = [...prev.servicePackages];
      const currentAgePricing = { ...newPackages[packageIndex].agePricing };
      
      // Don't allow removing if it's the last category
      const activeCategoryCount = Object.keys(currentAgePricing).length;
      if (activeCategoryCount <= 1) {
        return prev;
      }
      
      // Remove the age category
      delete currentAgePricing[ageCategory];
      
      newPackages[packageIndex] = {
        ...newPackages[packageIndex],
        agePricing: currentAgePricing
      };
      
      return {
        ...prev,
        servicePackages: newPackages
      };
    });
  };

  // Schedule management
  const addSchedule = () => {
    setFormData(prev => ({
      ...prev,
      serviceSchedules: [...prev.serviceSchedules, {
        dayOfWeek: null,
        departureTime: '08:00',
        availableCapacity: 10
      }]
    }));
  };

  const removeSchedule = (index) => {
    if (formData.serviceSchedules.length > 1) {
      setFormData(prev => ({
        ...prev,
        serviceSchedules: prev.serviceSchedules.filter((_, i) => i !== index)
      }));
    }
  };

  const updateSchedule = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      serviceSchedules: prev.serviceSchedules.map((schedule, i) => 
        i === index ? { ...schedule, [field]: value } : schedule
      )
    }));
  };

  // Itinerary management
  const addItineraryItem = () => {
    setFormData(prev => ({
      ...prev,
      itinerary: [...prev.itinerary, {
        time: '08:00 AM',
        activity: '',
        location: ''
      }]
    }));
  };

  const removeItineraryItem = (index) => {
    setFormData(prev => ({
      ...prev,
      itinerary: prev.itinerary.filter((_, i) => i !== index)
    }));
    
    // Clear time errors after removal since indices change
    setItineraryTimeErrors({});
  };

  const updateItineraryItem = (index, field, value) => {
    // Check for duplicate times if updating the time field
    if (field === 'time' && value) {
      const isDuplicateTime = formData.itinerary.some((item, i) => 
        i !== index && item.time === value
      );
      
      if (isDuplicateTime) {
        // Set error state for this specific itinerary item
        setItineraryTimeErrors(prev => ({
          ...prev,
          [index]: t('duplicateTimeError')
        }));
        return; // Don't update if duplicate
      } else {
        // Clear error state for this item if no duplicate
        setItineraryTimeErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[index];
          return newErrors;
        });
      }
    }
    
    setFormData(prev => ({
      ...prev,
      itinerary: prev.itinerary.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  // Move itinerary item up
  const moveItineraryItemUp = (index) => {
    if (index === 0) return; // Can't move first item up
    
    setFormData(prev => {
      const newItinerary = [...prev.itinerary];
      // Swap items
      [newItinerary[index], newItinerary[index - 1]] = [newItinerary[index - 1], newItinerary[index]];
      
      return {
        ...prev,
        itinerary: newItinerary
      };
    });
    
    // Clear time errors after reordering since indices change
    setItineraryTimeErrors({});
  };

  // Move itinerary item down
  const moveItineraryItemDown = (index) => {
    setFormData(prev => {
      if (index === prev.itinerary.length - 1) return prev; // Can't move last item down
      
      const newItinerary = [...prev.itinerary];
      // Swap items
      [newItinerary[index], newItinerary[index + 1]] = [newItinerary[index + 1], newItinerary[index]];
      
      return {
        ...prev,
        itinerary: newItinerary
      };
    });
    
    // Clear time errors after reordering since indices change
    setItineraryTimeErrors({});
  };

  // Validation
  const validateStep = (step) => {
    const newErrors = {};

    switch (step) {
      case 1: // Basic Information
        if (!formData.name.trim()) {
          newErrors.name = t('serviceNameRequired');
        }
        if (!formData.description.trim()) {
          newErrors.description = t('descriptionRequired');
        }
        if (!formData.serviceTypeId) {
          newErrors.serviceTypeId = t('serviceTypeRequired');
        }
        break;

      case 2: // Route & Boat (combined step)
        // Validate routes
        if (formData.routes.length === 0) {
          newErrors.routes = t('atLeastOneRouteRequired');
        }

        // Validate boats
        const boatIds = formData.serviceAssignments.map(assignment => assignment.boatId).filter(id => id);
        const duplicateBoats = boatIds.filter((id, index) => boatIds.indexOf(id) !== index);

        formData.serviceAssignments.forEach((assignment, index) => {
          if (!assignment.boatId) {
            newErrors[`boat_${index}`] = t('boatRequired');
          } else if (duplicateBoats.includes(assignment.boatId)) {
            newErrors[`boat_${index}`] = t('duplicateBoatNotAllowed');
          }
        });
        break;

      case 3: // Inclusions & Exclusions (moved from step 6)
        // Validate included items
        if (formData.includedItems.length === 0) {
          newErrors.includedItems = t('atLeastOneInclusionRequired');
        }
        break;

      case 4: // Pricing (moved from step 5)
        if (!formData.pricingModel) {
          newErrors.pricingModel = 'Pricing model is required';
        }

        if (formData.pricingModel === 'basic') {
          if (!formData.basePrice || formData.basePrice <= 0) {
            newErrors.basePrice = t('basePriceRequired');
          }
        } else if (formData.pricingModel === 'ageBased') {
          // Validate age-based pricing
          const hasValidPricing = Object.values(formData.agePricing).some(price => price > 0);
          if (!hasValidPricing) {
            newErrors.agePricing = t('atLeastOneAgeCategoryRequired');
          }
        }
        break;

      case 5: // Packages (moved from step 4, conditional)
        // Only validate if we're actually on this step (not skipped)
        if (formData.pricingModel === 'packageOnly' || formData.pricingModel === 'packageAndAge') {
          // Validate package basics
          formData.servicePackages.forEach((pkg, index) => {
            if (!pkg.packageTypeId) {
              newErrors[`package_${index}_type`] = t('packageTypeRequired');
            }
            if (!pkg.basePrice || pkg.basePrice <= 0) {
              newErrors[`package_${index}_price`] = t('packagePriceRequired');
            }
            
            // Only validate age pricing for 'packageAndAge' model
            if (formData.pricingModel === 'packageAndAge') {
              const hasAnyAgePrice = Object.values(pkg.agePricing || {}).some(price => price && price > 0);
              if (!hasAnyAgePrice) {
                newErrors[`package_${index}_age_pricing`] = t('atLeastOneAgePriceRequired');
              }
            }
          });
        }
        break;

      case 6: // Schedule (moved from step 7)
        // Validate schedules
        formData.serviceSchedules.forEach((schedule, index) => {
          if (!schedule.departureTime) {
            newErrors[`schedule_${index}`] = t('departureTimeRequired');
          }
        });
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log('🚨 FORM SUBMISSION TRIGGERED!');
    console.log('Current step:', currentStep);
    console.log('Total steps:', totalSteps);
    console.log('Is navigating:', isNavigating);
    console.log('Should only submit on step 7, current is:', currentStep);

    // Prevent submission during navigation
    if (isNavigating) {
      console.log('❌ Currently navigating, preventing auto-submission');
      return;
    }

    // Only submit when we're on the final step (step 7)
    if (currentStep !== totalSteps) {
      console.log('❌ Not on final step, preventing submission');
      return;
    }

    console.log('handleSubmit called - current step:', currentStep);
    console.log('user object:', user);
    console.log('user provider:', user?.provider);
    console.log('user provider id:', user?.provider?.id);

    if (!validateStep(currentStep)) {
      console.log('❌ Step validation failed');
      return;
    }

    console.log('✅ Step validation passed, proceeding with submission...');

    // Check if accessToken is available
    if (!accessToken) {
      console.log('❌ No access token available');
      showErrorModal(
        t('error'),
        'No token provided',
        'Please sign in again to continue.'
      );
      return;
    }

    console.log('✅ Access token available, proceeding with API call');

    setLoading(true);

    try {
      // Map frontend pricing model values to backend enum values
      const pricingModelMapping = {
        'basic': 'basic',
        'packageOnly': 'package_only',
        'ageBased': 'age_based',
        'packageAndAge': 'age_package_based'
      };

      // Prepare data for submission - convert routes array to expected format
      const serviceData = {
        ...formData,
        pricingModel: pricingModelMapping[formData.pricingModel] || 'basic',
        routes: formData.routes.map(routeId => ({
          routeId: routeId,
          priceModifier: 1.0 // Default price modifier
        })),
        providerId: user?.provider?.id // Add provider ID from authenticated user
      };

      console.log('Service data being sent:', serviceData);
      console.log('Provider ID being sent:', user?.provider?.id);

      // For now, we'll send the service data as JSON
      // Images will be handled separately by the backend or in a different step
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/boat-owner/services/wizard`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(serviceData)
      });

      if (response.ok) {
        showSuccessModal(
          t('success'),
          t('serviceCreatedSuccessfully')
        );

        setTimeout(() => {
          router.push('/boat-owner/services');
        }, 2000);
      } else {
        const errorData = await response.json();
        showErrorModal(
          t('error'),
          errorData.message || t('failedToCreateService'),
          errorData.details || null
        );
      }
    } catch (error) {
      console.error('Create service error:', error);
      showErrorModal(
        t('error'),
        t('failedToCreateService'),
        error.message
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <>
        <Head>
          <title>Create New Service - GoSea</title>
        </Head>
        <div className="min-h-screen bg-slate-50">
          <Navbar currentPage="boat-owner" />
          <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-4 sm:py-8 sm:px-0">
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </>
    );
  }

  // Wizard step components
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-md lg:text-lg font-medium flex items-center text-gray-900">
                <Info className="w-5 h-5 mr-2" />
                {t('basicInformation')}
              </h3>
              <span className="text-xs text-red-500 italic">{t('requiredFields')}</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('serviceName')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder={t('enterServiceName')}
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="serviceTypeId" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('serviceType')} <span className="text-red-500">*</span>
                </label>
                <select
                  id="serviceTypeId"
                  name="serviceTypeId"
                  value={formData.serviceTypeId}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 bg-white ${
                    errors.serviceTypeId ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">{t('selectServiceType')}</option>
                  {serviceTypes.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
                {errors.serviceTypeId && (
                  <p className="mt-1 text-sm text-red-600">{errors.serviceTypeId}</p>
                )}
              </div>

              <div>
                <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('duration')} ({t('minutes')})
                </label>
                <input
                  type="number"
                  id="duration"
                  name="duration"
                  min="30"
                  step="30"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                  placeholder={t('serviceDuration')}
                />
              </div>

              <div className="md:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('description')} <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  value={formData.description}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                    errors.description ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder={t('describeYourService')}
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                )}
              </div>
              
              {/* Divider between basic info and images */}
              <div className="md:col-span-2">
                <div className="border-t border-dotted border-gray-200"></div>
              </div>
              
              {/* Images Section */}
              <div className="md:col-span-2">
                <h3 className="text-md font-medium mb-4 flex items-center text-gray-900">
                  <Image className="w-5 h-5 mr-2" />
                  {t('images')}
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('uploadServiceImages')}
                    </label>
                    <div className="flex items-center justify-center w-full">
                      <label className="flex flex-col items-center justify-center w-full h-24 border-2 border-dashed rounded-lg cursor-pointer border-gray-300 hover:border-gray-400">
                        <div className="flex flex-col items-center justify-center pt-3 pb-3">
                          <CloudArrowUpIcon className="w-6 h-6 mb-1 text-gray-400" />
                          <p className="text-xs text-gray-500">
                            <span className="font-semibold">{t('clickToUpload')}</span> {t('orDragAndDrop')}
                          </p>
                          <p className="text-[10px] text-gray-500">
                            {t('imageRequirements')}
                          </p>
                        </div>
                        <input
                          type="file"
                          ref={fileInputRef}
                          onChange={handleImageSelect}
                          multiple
                          accept="image/*"
                          className="hidden"
                        />
                      </label>
                    </div>
                    {errors.images && (
                      <p className="mt-1 text-sm text-red-600">{errors.images}</p>
                    )}
                  </div>
                  
                  {imagePreviews.length > 0 && (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                      {imagePreviews.map((preview, index) => (
                        <div key={index} className="relative">
                          <img
                            src={preview.url}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-20 object-cover rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                          >
                            <XMarkIcon className="h-3 w-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        );
        
      case 2: // Combined Route & Boat step
        return (
          <>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium flex items-center text-gray-900">
                <Navigation className="w-5 h-5 mr-2" />
                {t('routeAndBoat')}
              </h3>
              <span className="text-xs text-red-500 italic">{t('requiredFields')}</span>
            </div>
            <div className="space-y-8">
              {/* Routes Section */}
              <div>
                <h4 className="text-md font-medium mb-3 text-gray-800">{t('routes')}</h4>
                {/* Searchable Dropdown with side-by-side layout */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Select Routes Field - Made smaller */}
                  <div className="relative">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('selectRoutes')} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={routesSearchTerm}
                        onChange={(e) => {
                          setRoutesSearchTerm(e.target.value);
                          setRoutesDropdownOpen(true);
                        }}
                        onFocus={() => setRoutesDropdownOpen(true)}
                        placeholder={t('searchForRoutes')}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                          errors.routes ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />

                      {routesDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                          {/* Available Routes */}
                          {filteredRoutes.length > 0 && (
                            <div>
                              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                                {t('availableRoutes')}
                              </div>
                              {filteredRoutes.map((route) => (
                                <button
                                  key={route.id}
                                  type="button"
                                  onClick={() => handleRouteSelect(route.id)}
                                  className="w-full text-left px-4 py-2 hover:bg-sky-50 hover:text-sky-700 transition-colors text-sm"
                                >
                                  {route.departureJetty?.name} → {route.destination?.name}
                                </button>
                              ))}
                            </div>
                          )}

                          {/* No results */}
                          {filteredRoutes.length === 0 && !routesSearchTerm.trim() && (
                            <div className="px-4 py-3 text-sm text-gray-500">
                              {t('startTypingToSearchRoutes')}
                            </div>
                          )}

                          {filteredRoutes.length === 0 && routesSearchTerm.trim() && (
                            <div className="px-4 py-3 text-sm text-gray-500">
                              {t('noRoutesFound')}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {errors.routes && (
                      <p className="mt-1 text-sm text-red-600">{errors.routes}</p>
                    )}
                  </div>

                  {/* Selected Routes - Displayed side by side */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('selectedRoutes')}
                    </label>
                    {formData.routes.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {formData.routes.map((routeId) => {
                          const route = availableRoutes.find(r => r.id === routeId);
                          return route ? (
                            <span
                              key={routeId}
                              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-sky-100 text-sky-800"
                            >
                              {route.departureJetty?.name} → {route.destination?.name}
                              <button
                                type="button"
                                onClick={() => handleRouteRemove(routeId)}
                                className="ml-2 text-sky-600 hover:text-sky-800"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </span>
                          ) : null;
                        })}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 italic">{t('noRoutesSelected')}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Divider between basic info and images */}
              <div className="md:col-span-2">
                <div className="border-t border-dotted border-gray-200"></div>
              </div>
              
              {/* Boats Section */}
              <div>
                <h4 className="text-md font-medium mb-3 text-gray-800">{t('boats')}</h4>
                <div className="space-y-4">
                  {formData.serviceAssignments.map((assignment, index) => (
                    <div key={index} className="border border-gray-100 bg-gray-50 rounded-lg p-4 relative">
                      <div className="grid grid-cols-1 md:grid-cols-[2fr_2fr_1fr] gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('boat')} <span className="text-red-500">*</span>
                          </label>
                          <select
                            value={assignment.boatId}
                            onChange={(e) => updateBoatAssignment(index, 'boatId', e.target.value)}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 bg-white ${
                              errors[`boat_${index}`] ? 'border-red-300' : 'border-gray-300'
                            }`}
                          >
                            <option value="">{t('selectBoat')}</option>
                            {availableBoats.map(boat => (
                              <option key={boat.id} value={boat.id}>
                                {boat.name} ({boat.capacity} {t('passengers')})
                              </option>
                            ))}
                          </select>
                          {errors[`boat_${index}`] && (
                            <p className="mt-1 text-sm text-red-600">{errors[`boat_${index}`]}</p>
                          )}
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('capacityOverride')}
                          </label>
                          <input
                            type="number"
                            min="1"
                            value={assignment.maxCapacityOverride || ''}
                            onChange={(e) => updateBoatAssignment(index, 'maxCapacityOverride', e.target.value ? parseInt(e.target.value) : null)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                            placeholder={t('leaveEmptyForFullCapacity')}
                          />
                        </div>
                        
                        <div className="flex items-center">
                          <label className="flex items-center md:col-span-1">
                            <input
                              type="checkbox"
                              checked={assignment.isPrimary}
                              onChange={(e) => updateBoatAssignment(index, 'isPrimary', e.target.checked)}
                              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500 h-4 w-4"
                            />
                            <span className="ml-2 text-gray-700 text-sm">{t('primaryBoat')}</span>
                          </label>
                        </div>
                      </div>
                      
                      {formData.serviceAssignments.length > 1 && (
                        <div className="absolute top-2 right-2 group">
                          <button
                            type="button"
                            onClick={() => removeBoatAssignment(index)}
                            className="p-1 rounded text-red-500 hover:text-red-600 hover:bg-red-50 transition-colors"
                            title={t('removeBoat')}
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  <button
                    type="button"
                    onClick={addBoatAssignment}
                    className="flex items-center text-sky-600 hover:text-sky-800 text-sm"
                  >
                    <PlusIcon className="h-4 w-4 mr-1" />
                    {t('addBoat')}
                  </button>
                </div>
              </div>
            </div>
          </>
        );
        
      case 3: // Inclusions & Exclusions (moved from step 5)
        return (
          <>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium flex items-center text-gray-900">
                <ListChecks className="w-5 h-5 mr-2" />
                {t('inclusionsExclusions')}
              </h3>
              <span className="text-xs text-red-500 italic">{t('requiredFields')}</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

              <div>
                <div className="relative">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('includedItems')} <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={inclusionsSearchTerm}
                      onChange={(e) => {
                        setInclusionsSearchTerm(e.target.value);
                        setInclusionsDropdownOpen(true);
                      }}
                      onFocus={() => setInclusionsDropdownOpen(true)}
                      placeholder={t('searchInclusions')}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors ${
                        errors.includedItems ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />

                    {inclusionsDropdownOpen && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                        {/* Predefined Inclusions */}
                        {filteredInclusions.length > 0 && (
                          <div>
                            <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                              {t('suggestedInclusions')}
                            </div>
                            {filteredInclusions.map((inclusion, index) => (
                              <button
                                key={index}
                                type="button"
                                onClick={() => handleInclusionSelect(inclusion)}
                                className="w-full text-left px-4 py-2 hover:bg-sky-50 hover:text-sky-700 transition-colors"
                              >
                                {inclusion}
                              </button>
                            ))}
                          </div>
                        )}

                        {/* Custom Inclusion Option */}
                        {inclusionsSearchTerm.trim() && !predefinedInclusions.includes(inclusionsSearchTerm.trim()) && (
                          <div>
                            {filteredInclusions.length > 0 && <div className="border-t border-gray-200"></div>}
                            <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                              {t('addCustom')}
                            </div>
                            <button
                              type="button"
                              onClick={() => handleInclusionSelect(inclusionsSearchTerm.trim())}
                              className="w-full text-left px-4 py-2 hover:bg-green-50 hover:text-green-700 transition-colors"
                            >
                              {t('addItem')} "{inclusionsSearchTerm.trim()}"
                            </button>
                          </div>
                        )}

                        {/* No results */}
                        {filteredInclusions.length === 0 && !inclusionsSearchTerm.trim() && (
                          <div className="px-4 py-3 text-sm text-gray-500">
                            {t('startTypingToSearch')}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Close dropdown when clicking outside */}
                  {inclusionsDropdownOpen && (
                    <div
                      className="fixed inset-0 z-0"
                      onClick={() => setInclusionsDropdownOpen(false)}
                    ></div>
                  )}

                  {/* Error message */}
                  {errors.includedItems && (
                    <p className="mt-1 text-sm text-red-600">{errors.includedItems}</p>
                  )}

                  {/* Selected Inclusions */}
                  {formData.includedItems.length > 0 && (
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">{t('selectedInclusions')}:</h3>
                      <div className="flex flex-wrap gap-2">
                        {formData.includedItems.map((item, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-sky-100 text-sky-800"
                          >
                            {item}
                            <button
                              type="button"
                              onClick={() => handleInclusionRemove(item)}
                              className="ml-2 text-sky-600 hover:text-sky-800"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <div className="relative">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('excludedItems')}
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={exclusionsSearchTerm}
                      onChange={(e) => {
                        setExclusionsSearchTerm(e.target.value);
                        setExclusionsDropdownOpen(true);
                      }}
                      onFocus={() => setExclusionsDropdownOpen(true)}
                      placeholder={t('searchExclusions')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                    />

                    {exclusionsDropdownOpen && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                        {/* Predefined Exclusions */}
                        {filteredExclusions.length > 0 && (
                          <div>
                            <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                              {t('suggestedExclusions')}
                            </div>
                            {filteredExclusions.map((exclusion, index) => (
                              <button
                                key={index}
                                type="button"
                                onClick={() => handleExclusionSelect(exclusion)}
                                className="w-full text-left px-4 py-2 hover:bg-sky-50 hover:text-sky-700 transition-colors"
                              >
                                {exclusion}
                              </button>
                            ))}
                          </div>
                        )}

                        {/* Custom Exclusion Option */}
                        {exclusionsSearchTerm.trim() && !predefinedExclusions.includes(exclusionsSearchTerm.trim()) && (
                          <div>
                            {filteredExclusions.length > 0 && <div className="border-t border-gray-200"></div>}
                            <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                              {t('addCustom')}
                            </div>
                            <button
                              type="button"
                              onClick={() => handleExclusionSelect(exclusionsSearchTerm.trim())}
                              className="w-full text-left px-4 py-2 hover:bg-green-50 hover:text-green-700 transition-colors"
                            >
                              {t('addItem')} "{exclusionsSearchTerm.trim()}"
                            </button>
                          </div>
                        )}

                        {/* No results */}
                        {filteredExclusions.length === 0 && !exclusionsSearchTerm.trim() && (
                          <div className="px-4 py-3 text-sm text-gray-500">
                            {t('startTypingToSearch')}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Close dropdown when clicking outside */}
                  {exclusionsDropdownOpen && (
                    <div
                      className="fixed inset-0 z-0"
                      onClick={() => setExclusionsDropdownOpen(false)}
                    ></div>
                  )}

                  {/* Selected Exclusions */}
                  {formData.excludedItems.length > 0 && (
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">{t('selectedExclusions')}:</h3>
                      <div className="flex flex-wrap gap-2">
                        {formData.excludedItems.map((item, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-red-100 text-red-800"
                          >
                            {item}
                            <button
                              type="button"
                              onClick={() => handleExclusionRemove(item)}
                              className="ml-2 text-red-600 hover:text-red-800"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="md:col-span-2">
                <div className="border-t border-dotted border-gray-200"></div>
              </div>

              <div className="md:col-span-2">
                <label htmlFor="specialInstruction" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('specialInstructions')}
                </label>
                <textarea
                  id="specialInstruction"
                  name="specialInstruction"
                  rows={3}
                  value={formData.specialInstruction}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                  placeholder={t('enterSpecialInstructions')}
                />
              </div>
            </div>
          </>
        );

      case 4: // Pricing (moved from step 5, with new two-column layout)
        return (
          <>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium flex items-center text-gray-900">
                <CircleDollarSign className="w-5 h-5 mr-2" />
                {t('pricing')}
              </h3>
              <span className="text-xs text-red-500 italic">{t('requiredFields')}</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left column: Pricing Model */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('pricingModel')} <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.pricingModel}
                  onChange={(e) => {
                    const newPricingModel = e.target.value;
                    
                    // Create initial age pricing when selecting age-based models
                    let initialAgePricing = {};
                    if (newPricingModel === 'ageBased' || newPricingModel === 'packageAndAge') {
                      // Populate all available age categories with default price 0
                      ageCategories.forEach(category => {
                        initialAgePricing[category.code.toLowerCase()] = 0;
                      });
                      
                      // Fallback if ageCategories is empty
                      if (ageCategories.length === 0) {
                        initialAgePricing = {
                          'adult': 0,
                          'child': 0,
                          'toddler': 0,
                          'senior': 0,
                          'pwd': 0
                        };
                      }
                    }
                    
                    setFormData(prev => ({
                      ...prev,
                      pricingModel: newPricingModel,
                      // Set all age categories when selecting age-based models, empty otherwise
                      agePricing: (newPricingModel === 'ageBased' || newPricingModel === 'packageAndAge') ? initialAgePricing : {},
                      // Set basePrice to 0 when package-based pricing is selected (packageOnly or packageAndAge)
                      basePrice: (newPricingModel === 'packageOnly' || newPricingModel === 'packageAndAge') ? 0 : prev.basePrice
                    }));

                    // Clear errors when user makes a selection
                    if (errors.pricingModel) {
                      setErrors(prev => ({
                        ...prev,
                        pricingModel: ''
                      }));
                    }
                    
                    // Clear basePrice error if switching away from basic pricing or to package-based pricing
                    if ((newPricingModel !== 'basic' || newPricingModel === 'packageOnly' || newPricingModel === 'packageAndAge') && errors.basePrice) {
                      setErrors(prev => ({
                        ...prev,
                        basePrice: ''
                      }));
                    }
                    
                    // Clear age pricing errors when switching away from age-based pricing
                    if (newPricingModel !== 'ageBased' && newPricingModel !== 'packageAndAge' && errors.agePricing) {
                      setErrors(prev => ({
                        ...prev,
                        agePricing: ''
                      }));
                    }
                  }}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 bg-white text-sm ${
                    errors.pricingModel ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select pricing model</option>
                  <option value="basic">{t('basicPricingOption')}</option>
                  <option value="packageOnly">{t('packageOnlyOption')}</option>
                  <option value="ageBased">{t('ageBasedOption')}</option>
                  <option value="packageAndAge">{t('packageAndAgeOption')}</option>
                </select>
                {errors.pricingModel && (
                  <p className="mt-1 text-sm text-red-600">{errors.pricingModel}</p>
                )}
              </div>

              {/* Right column: Base Price or Age-Based Pricing */}
              <div>
                {formData.pricingModel === 'ageBased' ? (
                  <>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('ageBasedPrice')} (RM)
                    </label>
                    <div className="space-y-3">
                      {Object.keys(formData.agePricing).length === 0 && (
                        <p className="text-sm text-gray-500 italic">
                          {t('clickAddAgeCategoryText')}
                        </p>
                      )}
                      {Object.entries(formData.agePricing).map(([categoryId, price]) => {
                        const category = ageCategories.find(cat => cat.id === categoryId);
                        return (
                          <div key={categoryId} className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-700 min-w-[80px]">
                              {formatCategoryName(category?.name || categoryId)}:
                            </span>
                            <div className="relative flex-1">
                              <input
                                type="number"
                                min="0"
                                step="0.01"
                                value={price === 0 ? '' : price}
                                onChange={(e) => updateAgeCategoryPrice(categoryId, e.target.value)}
                                onBlur={(e) => {
                                  // Format to proper number when user leaves the field
                                  const numericValue = parseFloat(e.target.value) || 0;
                                  updateAgeCategoryPrice(categoryId, numericValue);
                                }}
                                onFocus={(e) => {
                                  // Select all text when focusing for easy replacement
                                  e.target.select();
                                }}
                                className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-sm"
                                placeholder="0.00"
                              />
                              <div className="absolute top-2 right-2 group">
                                <button
                                  type="button"
                                  onClick={() => removeAgeCategory(categoryId)}
                                  className="p-1 rounded text-red-500 hover:text-red-600 hover:bg-red-50 transition-colors"
                                  title={t('removeAgeCategory')}
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                      {Object.keys(formData.agePricing).length < ageCategories.length ? (
                        <button
                          type="button"
                          onClick={addAgeCategory}
                          className="flex items-center text-sky-600 hover:text-sky-800 text-sm"
                        >
                          <PlusIcon className="h-4 w-4 mr-1" />
                          {t('addAgeCategory')} ({Object.keys(formData.agePricing).length}/{ageCategories.length})
                        </button>
                      ) : (
                        Object.keys(formData.agePricing).length > 0 && (
                          <p className="text-sm text-gray-500">
                            {t('allAgeCategoriesAdded')} ({Object.keys(formData.agePricing).length}/{ageCategories.length})
                          </p>
                        )
                      )}
                    </div>
                    {errors.agePricing && (
                      <p className="mt-1 text-sm text-red-600">{errors.agePricing}</p>
                    )}
                  </>
                ) : (
                  <>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('basePrice')} (RM) {(formData.pricingModel !== 'packageOnly' && formData.pricingModel !== 'packageAndAge') && <span className="text-red-500">*</span>}
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={(formData.pricingModel === 'packageOnly' || formData.pricingModel === 'packageAndAge') ? 0 : formData.basePrice}
                      disabled={formData.pricingModel === 'packageOnly' || formData.pricingModel === 'packageAndAge'}
                      onChange={(e) => {
                        if (formData.pricingModel === 'packageOnly' || formData.pricingModel === 'packageAndAge') return;
                        
                        setFormData(prev => ({ ...prev, basePrice: e.target.value === '' ? '' : e.target.value }));
                        
                        // Clear error when user starts typing
                        if (errors.basePrice) {
                          setErrors(prev => ({
                            ...prev,
                            basePrice: ''
                          }));
                        }
                      }}
                      onBlur={(e) => {
                        if (formData.pricingModel === 'packageOnly' || formData.pricingModel === 'packageAndAge') return;
                        
                        // Convert to proper number on blur
                        const numValue = parseFloat(e.target.value) || 0;
                        setFormData(prev => ({ ...prev, basePrice: numValue }));
                      }}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors text-sm ${
                        (formData.pricingModel === 'packageOnly' || formData.pricingModel === 'packageAndAge')
                          ? 'bg-gray-100 cursor-not-allowed' 
                          : errors.basePrice ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="0.00"
                    />
                    {errors.basePrice && (
                      <p className="mt-1 text-sm text-red-600">{errors.basePrice}</p>
                    )}
                    {(formData.pricingModel === 'packageOnly' || formData.pricingModel === 'packageAndAge') && (
                      <p className="mt-2 text-sm text-gray-600 italic">
                        {t('packageOnlyNote')}
                      </p>
                    )}
                  </>
                )}
              </div>
            </div>
          </>
        );

      case 5: // Packages (moved from step 3, conditional)
        return (
          <>
            <h3 className="text-lg font-medium mb-4 flex items-center text-gray-900">
              <Package className="w-5 h-5 mr-2" />
              {t('packages')}
            </h3>
            <div className="space-y-6">
              {formData.servicePackages.map((pkg, packageIndex) => (
                <div key={packageIndex} className="border border-gray-100 bg-gray-50 rounded-lg p-4 relative">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('packageType')}
                      </label>
                      <select
                        value={pkg.packageTypeId}
                        onChange={(e) => updatePackage(packageIndex, 'packageTypeId', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 bg-white ${
                          errors[`package_${packageIndex}_type`] ? 'border-red-300' : 'border-gray-300'
                        }`}
                      >
                        <option value="">{t('selectPackageType')}</option>
                        {packageTypes.map(type => (
                          <option key={type.id} value={type.id}>
                            {type.name}
                          </option>
                        ))}
                      </select>
                      {errors[`package_${packageIndex}_type`] && (
                        <p className="mt-1 text-sm text-red-600">{errors[`package_${packageIndex}_type`]}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('basePrice')} (RM)
                      </label>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={pkg.basePrice}
                        onChange={(e) => updatePackage(packageIndex, 'basePrice', e.target.value === '' ? '' : e.target.value)}
                        onBlur={(e) => updatePackage(packageIndex, 'basePrice', parseFloat(e.target.value) || 0)}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 ${
                          errors[`package_${packageIndex}_price`] ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                      {errors[`package_${packageIndex}_price`] && (
                        <p className="mt-1 text-sm text-red-600">{errors[`package_${packageIndex}_price`]}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="relative">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {t('includedItems')}
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            value={packageInclusionsSearchTerm[packageIndex] || ''}
                            onChange={(e) => {
                              setPackageInclusionsSearchTerm(prev => ({ ...prev, [packageIndex]: e.target.value }));
                              setPackageInclusionsDropdownOpen(prev => ({ ...prev, [packageIndex]: true }));
                            }}
                            onFocus={() => setPackageInclusionsDropdownOpen(prev => ({ ...prev, [packageIndex]: true }))}
                            placeholder={t('searchInclusions')}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                          />

                          {packageInclusionsDropdownOpen[packageIndex] && (
                            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                              {/* Predefined Inclusions */}
                              {predefinedInclusions.filter(inclusion =>
                                inclusion.toLowerCase().includes((packageInclusionsSearchTerm[packageIndex] || '').toLowerCase()) &&
                                !pkg.includedItems.includes(inclusion)
                              ).length > 0 && (
                                <div>
                                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                                    {t('suggestedInclusions')}
                                  </div>
                                  {predefinedInclusions.filter(inclusion =>
                                    inclusion.toLowerCase().includes((packageInclusionsSearchTerm[packageIndex] || '').toLowerCase()) &&
                                    !pkg.includedItems.includes(inclusion)
                                  ).map((inclusion, index) => (
                                    <button
                                      key={index}
                                      type="button"
                                      onClick={() => handlePackageInclusionSelect(packageIndex, inclusion)}
                                      className="w-full text-left px-4 py-2 hover:bg-sky-50 hover:text-sky-700 transition-colors"
                                    >
                                      {inclusion}
                                    </button>
                                  ))}
                                </div>
                              )}

                              {/* Custom Inclusion */}
                              {(packageInclusionsSearchTerm[packageIndex] || '').trim() && 
                               !predefinedInclusions.includes((packageInclusionsSearchTerm[packageIndex] || '').trim()) && (
                                <div>
                                  {predefinedInclusions.filter(inclusion =>
                                    inclusion.toLowerCase().includes((packageInclusionsSearchTerm[packageIndex] || '').toLowerCase()) &&
                                    !pkg.includedItems.includes(inclusion)
                                  ).length > 0 && <div className="border-t border-gray-200"></div>}
                                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                                    {t('addCustom')}
                                  </div>
                                  <button
                                    type="button"
                                    onClick={() => handlePackageInclusionSelect(packageIndex, (packageInclusionsSearchTerm[packageIndex] || '').trim())}
                                    className="w-full text-left px-4 py-2 hover:bg-green-50 hover:text-green-700 transition-colors"
                                  >
                                    {t('addItem')} "{(packageInclusionsSearchTerm[packageIndex] || '').trim()}"
                                  </button>
                                </div>
                              )}

                              {/* No results */}
                              {predefinedInclusions.filter(inclusion =>
                                inclusion.toLowerCase().includes((packageInclusionsSearchTerm[packageIndex] || '').toLowerCase()) &&
                                !pkg.includedItems.includes(inclusion)
                              ).length === 0 && !(packageInclusionsSearchTerm[packageIndex] || '').trim() && (
                                <div className="px-4 py-3 text-sm text-gray-500">
                                  {t('startTypingToSearch')}
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Dropdown backdrop */}
                        {packageInclusionsDropdownOpen[packageIndex] && (
                          <div
                            className="fixed inset-0 z-0"
                            onClick={() => setPackageInclusionsDropdownOpen(prev => ({ ...prev, [packageIndex]: false }))}
                          ></div>
                        )}

                        {/* Selected Inclusions */}
                        {pkg.includedItems.length > 0 && (
                          <div className="mt-4">
                            <h3 className="text-sm font-medium text-gray-700 mb-2">{t('selectedInclusions')}:</h3>
                            <div className="flex flex-wrap gap-2">
                              {pkg.includedItems.map((item, itemIndex) => (
                                <span
                                  key={itemIndex}
                                  className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-sky-100 text-sky-800"
                                >
                                  {item}
                                  <button
                                    type="button"
                                    onClick={() => handlePackageInclusionRemove(packageIndex, item)}
                                    className="ml-2 text-sky-600 hover:text-sky-800"
                                  >
                                    <XMarkIcon className="h-3 w-3" />
                                  </button>
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <div className="relative">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {t('excludedItems')}
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            value={packageExclusionsSearchTerm[packageIndex] || ''}
                            onChange={(e) => {
                              setPackageExclusionsSearchTerm(prev => ({ ...prev, [packageIndex]: e.target.value }));
                              setPackageExclusionsDropdownOpen(prev => ({ ...prev, [packageIndex]: true }));
                            }}
                            onFocus={() => setPackageExclusionsDropdownOpen(prev => ({ ...prev, [packageIndex]: true }))}
                            placeholder={t('searchExclusions')}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-colors"
                          />

                          {packageExclusionsDropdownOpen[packageIndex] && (
                            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                              {/* Predefined Exclusions */}
                              {predefinedExclusions.filter(exclusion =>
                                exclusion.toLowerCase().includes((packageExclusionsSearchTerm[packageIndex] || '').toLowerCase()) &&
                                !pkg.excludedItems.includes(exclusion)
                              ).length > 0 && (
                                <div>
                                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                                    {t('suggestedExclusions')}
                                  </div>
                                  {predefinedExclusions.filter(exclusion =>
                                    exclusion.toLowerCase().includes((packageExclusionsSearchTerm[packageIndex] || '').toLowerCase()) &&
                                    !pkg.excludedItems.includes(exclusion)
                                  ).map((exclusion, index) => (
                                    <button
                                      key={index}
                                      type="button"
                                      onClick={() => handlePackageExclusionSelect(packageIndex, exclusion)}
                                      className="w-full text-left px-4 py-2 hover:bg-sky-50 hover:text-sky-700 transition-colors"
                                    >
                                      {exclusion}
                                    </button>
                                  ))}
                                </div>
                              )}

                              {/* Custom Exclusion */}
                              {(packageExclusionsSearchTerm[packageIndex] || '').trim() && 
                               !predefinedExclusions.includes((packageExclusionsSearchTerm[packageIndex] || '').trim()) && (
                                <div>
                                  {predefinedExclusions.filter(exclusion =>
                                    exclusion.toLowerCase().includes((packageExclusionsSearchTerm[packageIndex] || '').toLowerCase()) &&
                                    !pkg.excludedItems.includes(exclusion)
                                  ).length > 0 && <div className="border-t border-gray-200"></div>}
                                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">
                                    {t('addCustom')}
                                  </div>
                                  <button
                                    type="button"
                                    onClick={() => handlePackageExclusionSelect(packageIndex, (packageExclusionsSearchTerm[packageIndex] || '').trim())}
                                    className="w-full text-left px-4 py-2 hover:bg-green-50 hover:text-green-700 transition-colors"
                                  >
                                    {t('addItem')} "{(packageExclusionsSearchTerm[packageIndex] || '').trim()}"
                                  </button>
                                </div>
                              )}

                              {/* No results */}
                              {predefinedExclusions.filter(exclusion =>
                                exclusion.toLowerCase().includes((packageExclusionsSearchTerm[packageIndex] || '').toLowerCase()) &&
                                !pkg.excludedItems.includes(exclusion)
                              ).length === 0 && !(packageExclusionsSearchTerm[packageIndex] || '').trim() && (
                                <div className="px-4 py-3 text-sm text-gray-500">
                                  {t('startTypingToSearch')}
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Dropdown backdrop */}
                        {packageExclusionsDropdownOpen[packageIndex] && (
                          <div
                            className="fixed inset-0 z-0"
                            onClick={() => setPackageExclusionsDropdownOpen(prev => ({ ...prev, [packageIndex]: false }))}
                          ></div>
                        )}

                        {/* Selected Exclusions */}
                        {pkg.excludedItems.length > 0 && (
                          <div className="mt-4">
                            <h3 className="text-sm font-medium text-gray-700 mb-2">{t('selectedExclusions')}:</h3>
                            <div className="flex flex-wrap gap-2">
                              {pkg.excludedItems.map((item, itemIndex) => (
                                <span
                                  key={itemIndex}
                                  className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-red-100 text-red-800"
                                >
                                  {item}
                                  <button
                                    type="button"
                                    onClick={() => handlePackageExclusionRemove(packageIndex, item)}
                                    className="ml-2 text-red-600 hover:text-red-800"
                                  >
                                    <XMarkIcon className="h-3 w-3" />
                                  </button>
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Age-based pricing section - only show for 'packageAndAge' pricing model */}
                  {formData.pricingModel === 'packageAndAge' && (
                    <div className="border-t border-gray-200 pt-4">
                      <h3 className="text-md font-medium text-gray-900 mb-1">{t('ageBasedPricing')}</h3>
                      <p className="text-xs text-gray-500 mb-3 italic">{t('ageCategoryRemovalNote')}</p>
                      <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                        {ageCategories
                          .filter(category => pkg.agePricing.hasOwnProperty(category.code.toLowerCase()))
                          .map(category => {
                          const categoryKey = category.code.toLowerCase();
                          return (
                            <div key={category.id}>
                              <div className="flex items-center justify-between mb-1">
                                <label className="block text-xs font-medium text-gray-700">
                                  {formatCategoryName(category.name)} (RM)
                                </label>
                                {Object.keys(pkg.agePricing).length > 1 && (
                                  <div className="group relative">
                                    <button
                                      type="button"
                                      onClick={() => removePackageAgeCategory(packageIndex, categoryKey)}
                                      className="text-red-500 hover:text-red-600 transition-colors p-0.5"
                                    >
                                      <MinusIcon className="h-3 w-3 font-bold stroke-2" />
                                    </button>
                                    <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 text-xs text-white bg-black rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-50">
                                      {t('removeAgeCategory')}
                                    </span>
                                  </div>
                                )}
                              </div>
                              <input
                                type="number"
                                min="0"
                                step="0.01"
                                value={pkg.agePricing[categoryKey] || 0}
                                onChange={(e) => updatePackageAgePricing(packageIndex, categoryKey, e.target.value === '' ? '' : e.target.value)}
                                onBlur={(e) => updatePackageAgePricing(packageIndex, categoryKey, parseFloat(e.target.value) || 0)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                              />
                            </div>
                          );
                        })}
                      </div>
                      {errors[`package_${packageIndex}_age_pricing`] && (
                        <p className="mt-1 text-sm text-red-600">{errors[`package_${packageIndex}_age_pricing`]}</p>
                      )}
                    </div>
                  )}
                  
                  {formData.servicePackages.length > 1 && (
                    <div className="absolute top-2 right-2 group">
                      <button
                        type="button"
                        onClick={() => removePackage(packageIndex)}
                        className="p-1 rounded text-red-500 hover:text-red-600 hover:bg-red-50 transition-colors"
                        title={t('removePackage')}
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              ))}
              
              <button
                type="button"
                onClick={addPackage}
                className="flex items-center text-sky-600 hover:text-sky-800 text-sm"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                {t('addPackage')}
              </button>
            </div>
          </>
        );
        
      case 6: // Schedule (moved from step 7)
        console.log('Rendering step 6 - Schedule');
        console.log('Current user provider id:', user?.provider?.id);
        return (
          <>
            <h3 className="text-lg font-medium mb-4 flex items-center text-gray-900">
              <CalendarClock className="w-5 h-5 mr-2" />
              {t('schedules')}
            </h3>
            <div className="space-y-4">
              {formData.serviceSchedules.map((schedule, index) => (
                <div key={index} className="border border-gray-100 bg-gray-50 rounded-lg p-4 relative">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('dayOfWeek')}
                      </label>
                      <select
                        value={schedule.dayOfWeek === null ? '' : schedule.dayOfWeek}
                        onChange={(e) => updateSchedule(index, 'dayOfWeek', e.target.value === '' ? null : parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 bg-white"
                      >
                        <option value="">{t('daily')}</option>
                        <option value="0">{t('sunday')}</option>
                        <option value="1">{t('monday')}</option>
                        <option value="2">{t('tuesday')}</option>
                        <option value="3">{t('wednesday')}</option>
                        <option value="4">{t('thursday')}</option>
                        <option value="5">{t('friday')}</option>
                        <option value="6">{t('saturday')}</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('departureTime')}
                      </label>
                      <CustomTimePicker
                        value={schedule.departureTime}
                        onChange={(e) => updateSchedule(index, 'departureTime', e.target.value)}
                        name="departureTime"
                        placeholder={t('selectTime')}
                        className="w-full"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('availableCapacity')}
                      </label>
                      <input
                        type="number"
                        min="1"
                        value={schedule.availableCapacity}
                        onChange={(e) => updateSchedule(index, 'availableCapacity', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                      />
                    </div>
                  </div>
                  
                  {formData.serviceSchedules.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeSchedule(index)}
                      className="absolute top-2 right-2 p-1 rounded text-red-500 hover:text-red-600 hover:bg-red-50 transition-colors"
                      title={t('removeSchedule')}
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
              
              <button
                type="button"
                onClick={addSchedule}
                className="flex items-center text-sky-600 hover:text-sky-800 text-sm"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                {t('addSchedule')}
              </button>
            </div>
          </>
        );

      case 7: // Itinerary (optional)
        return (
          <>
            <h3 className="text-lg font-medium mb-4 flex items-center text-gray-900">
              <ClockIcon className="w-5 h-5 mr-2" />
              {t('itinerary')} <span className="text-sm text-gray-500 ml-2">({t('optional')})</span>
            </h3>
            <p className="text-sm text-gray-600 mb-6">
              {t('itineraryDescription') || 'Create a detailed schedule for your service to help customers understand what to expect during their trip.'}
            </p>
            <div className="space-y-4">
              {formData.itinerary.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <ClockIcon className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p>{t('noItineraryItems') || 'No itinerary items added yet. Click "Add Itinerary Item" to get started.'}</p>
                </div>
              ) : (
                formData.itinerary.map((item, index) => (
                  <div key={index} className="border border-gray-100 bg-gray-50 rounded-lg p-4 relative">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {t('time')}
                        </label>
                        <CustomTimePicker
                          value={item.time}
                          onChange={(e) => updateItineraryItem(index, 'time', e.target.value)}
                          name="time"
                          placeholder="08:30 AM"
                          className="w-full"
                          format12Hour={true}
                          error={!!itineraryTimeErrors[index]}
                        />
                        {itineraryTimeErrors[index] && (
                          <p className="mt-1 text-sm text-red-600">
                            {itineraryTimeErrors[index]}
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {t('activity')}
                        </label>
                        <input
                          type="text"
                          value={item.activity}
                          onChange={(e) => updateItineraryItem(index, 'activity', e.target.value)}
                          placeholder={t('activityPlaceholder') || 'Snorkeling at Coral Point'}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {t('location')}
                        </label>
                        <input
                          type="text"
                          value={item.location}
                          onChange={(e) => updateItineraryItem(index, 'location', e.target.value)}
                          placeholder={t('locationPlaceholder') || 'Coral Point'}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                        />
                      </div>
                    </div>
                    
                    {/* Control buttons */}
                    <div className="absolute top-2 right-2 flex items-center space-x-1">
                      {/* Move Up Button */}
                      <button
                        type="button"
                        onClick={() => moveItineraryItemUp(index)}
                        disabled={index === 0}
                        className={`p-1 rounded transition-colors ${
                          index === 0 
                            ? 'text-gray-300 cursor-not-allowed' 
                            : 'text-blue-500 hover:text-blue-600 hover:bg-blue-50'
                        }`}
                        title={t('moveItemUp')}
                      >
                        <ArrowUpIcon className="h-4 w-4" />
                      </button>
                      
                      {/* Move Down Button */}
                      <button
                        type="button"
                        onClick={() => moveItineraryItemDown(index)}
                        disabled={index === formData.itinerary.length - 1}
                        className={`p-1 rounded transition-colors ${
                          index === formData.itinerary.length - 1 
                            ? 'text-gray-300 cursor-not-allowed' 
                            : 'text-blue-500 hover:text-blue-600 hover:bg-blue-50'
                        }`}
                        title={t('moveItemDown')}
                      >
                        <ArrowDownIcon className="h-4 w-4" />
                      </button>
                      
                      {/* Remove Button */}
                      <button
                        type="button"
                        onClick={() => removeItineraryItem(index)}
                        className="p-1 rounded text-red-500 hover:text-red-600 hover:bg-red-50 transition-colors"
                        title={t('removeItineraryItem')}
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))
              )}
              
              <button
                type="button"
                onClick={addItineraryItem}
                className="flex items-center text-sky-600 hover:text-sky-800 text-sm"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                {t('addItineraryItem') || 'Add Itinerary Item'}
              </button>
            </div>
          </>
        );
        
      default:
        return null;
    }
  };

  const pageName = "Create New Service";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-slate-50">
        <Navbar currentPage="boat-owner" />

        <main className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          <div className="px-4 py-4 sm:py-8 sm:px-0">
            {/* Page Header */}
            <div className="mb-4 lg:mb-6">
              <h1 className="text-lg lg:text-2xl font-bold text-gray-900">
                {t('createNewService')}
              </h1>
            </div>

            {/* Wizard Progress */}
            <div className="mb-4 lg:mb-8">
              <div className="flex justify-between relative">
                {/* Background line */}
                <div className="absolute top-3 md:top-4 left-0 right-0 h-0.5 bg-gray-200 -z-10"></div>
                {/* Progress line */}
                <div 
                  className="absolute top-3 md:top-4 left-0 h-0.5 bg-emerald-500 -z-10 transition-all duration-300"
                  style={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
                ></div>
                {[...Array(totalSteps)].map((_, i) => (
                  <div key={i} className="flex flex-col items-center relative" style={{ flex: 1 }}>
                    {/* Connecting lines between numbers */}
                    {i < totalSteps - 1 && (
                      <div className={`absolute top-3 md:top-4 h-0.5 -z-5 ${i + 1 < currentStep ? 'bg-emerald-500' : 'bg-gray-200'}`} 
                           style={{ 
                             left: '50%', 
                             width: '100%', 
                             marginLeft: '0.75rem', 
                             marginRight: '0.75rem' 
                           }}></div>
                    )}
                    <div className={`w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center relative z-10 ${
                      i + 1 < currentStep 
                        ? 'bg-emerald-500 text-white' 
                        : i + 1 === currentStep
                          ? 'bg-emerald-500 text-white'
                          : 'bg-white border-2 border-gray-300 text-gray-500'
                    }`}>
                      {i + 1 < currentStep ? (
                        <Check className="w-4 h-4 md:w-5 md:h-5 text-white" />
                      ) : (
                        <span className="text-xs md:text-sm">{i + 1}</span>
                      )}
                    </div>
                    <div className="mt-1 md:mt-2 text-[10px] md:text-xs text-center text-gray-600 w-full px-1">
                      {i === 0 ? t('step1') :
                       i === 1 ? t('routeAndBoat') :
                       i === 2 ? t('inclusions') :
                       i === 3 ? t('pricing') :
                       i === 4 ? t('packages') :
                       i === 5 ? t('schedule') :
                       i === 6 ? t('itinerary') : ''}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                  {renderStepContent()}
                </div>
              </div>
              
              {/* Navigation Buttons */}
              <div className="flex justify-between space-x-4">
                <div>
                  {currentStep > 1 && (
                    <button
                      type="button"
                      onClick={prevStep}
                      className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                    >
                      <ArrowLeftIcon className="h-4 w-4 mr-1" />
                      {t('previous')}
                    </button>
                  )}
                </div>
                
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                  >
                    {t('cancel')}
                  </button>
                  
                  {currentStep < totalSteps ? (
                    <button
                      type="button"
                      onClick={() => {
                        console.log('📱 Next button clicked - current step:', currentStep);
                        if (validateStep(currentStep)) {
                          console.log('✅ Step validation passed, calling nextStep()');
                          nextStep();
                        } else {
                          console.log('❌ Step validation failed');
                        }
                      }}
                      className="flex items-center px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors text-sm"
                    >
                      {t('next')}
                      <ArrowRightIcon className="h-4 w-4 ml-1" />
                    </button>
                  ) : (
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-4 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
                    >
                      {loading ? t('creating') : t('createService')}
                    </button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </main>

        <Footer />
      </div>

      {/* Feedback Modal */}
      <StandardModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        type={feedbackModal.type}
        title={feedbackModal.title}
        message={feedbackModal.message}
        details={feedbackModal.details}
        autoClose={feedbackModal.type === 'success'}
        autoCloseDelay={2000}
      />

      {/* Confirmation Modal for Zero-Value Pricing */}
      <StandardModal
        isOpen={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        type="warning"
        title={confirmationModal.title}
        message={confirmationModal.message}
        details={confirmationModal.details}
        primaryButton={
          <button
            onClick={() => {
              setShowConfirmationModal(false);
              if (confirmationModal.onConfirm) {
                confirmationModal.onConfirm();
              }
            }}
            className="px-4 py-2 text-sm font-medium rounded-lg transition-colors bg-amber-600 hover:bg-amber-700 text-white"
          >
            {t('continue')}
          </button>
        }
        secondaryButton={
          <button
            onClick={() => {
              setShowConfirmationModal(false);
              // Reset navigation flag when user cancels
              setIsNavigating(false);
            }}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
          >
            {t('cancel')}
          </button>
        }
        showCloseButton={false}
      />
    </>
  );
};

export default CreateService;
