import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import Navbar from '../components/Navbar';
import StandardModal from '../components/StandardModal';
import SignInModal from '../components/SignInModal';
import SignUpModal from '../components/SignUpModal';
import ProfileCompletionModal from '../components/ProfileCompletionModal';
import Footer from '../components/Footer';
import {
  ArrowLeftIcon,
  MapPinIcon,
  UsersIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ClockIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChatBubbleLeftEllipsisIcon
} from '@heroicons/react/24/outline';
import Head from 'next/head';
import { Goal } from 'lucide-react';

export default function BookingPage() {
  const { t } = useLanguage();
  const { user } = useAuth();
  const router = useRouter();
  const { providerId, serviceId, jettyId, destinationId, date, selectedPackage } = router.query;

  const [provider, setProvider] = useState(null);
  const [selectedService, setSelectedService] = useState(null);
  const [availableRoutes, setAvailableRoutes] = useState([]);
  const [selectedRoute, setSelectedRoute] = useState(null);
  const [bookingDetails, setBookingDetails] = useState({
    date: date || '',
    time: '',
    adults: 1,
    children: 0,
    toddlers: 0,
    seniors: 0,
    pwd: 0,
    passengers: 1, // For scenarios 1 and 2
    specialRequests: '',
    selectedPackage: selectedPackage || 'standard' // Use package from query or default to standard
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});

  // Calendar and availability state
  const [availabilityData, setAvailabilityData] = useState({});
  const [availableTimeSlots, setAvailableTimeSlots] = useState([]);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [showCalendar, setShowCalendar] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [ageRanges, setAgeRanges] = useState([]);

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [showSignInModal, setShowSignInModal] = useState(false);
  const [showSignUpModal, setShowSignUpModal] = useState(false);
  const [showProfileCompletion, setShowProfileCompletion] = useState(false);
  // Track if this is a mandatory profile completion (from Google OAuth)
  const [isMandatoryProfileCompletion, setIsMandatoryProfileCompletion] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    type: 'info',
    title: '',
    message: '',
    details: null
  });



  // Fetch age ranges for the service
  const fetchAgeRanges = async (serviceId) => {
    try {
      const response = await fetch(`/api/services/${serviceId}/age-ranges`);
      const data = await response.json();

      if (data.success) {
        setAgeRanges(data.data.ageRanges);
        console.log('✅ Loaded age ranges:', data.data.ageRanges);
      } else {
        console.error('Failed to fetch age ranges:', data.message);
        // Fallback to default age ranges
        setAgeRanges([
          { code: 'ADULT', name: 'Adult', minAge: 18, maxAge: 64 },
          { code: 'CHILD', name: 'Child', minAge: 3, maxAge: 17 },
          { code: 'TODDLER', name: 'Toddler', minAge: 0, maxAge: 2 },
          { code: 'SENIOR', name: 'Senior', minAge: 65, maxAge: null },
          { code: 'PWD', name: 'PWD', minAge: null, maxAge: null }
        ]);
      }
    } catch (error) {
      console.error('Error fetching age ranges:', error);
      // Fallback to default age ranges
      setAgeRanges([
        { code: 'ADULT', name: 'Adult', minAge: 18, maxAge: 64 },
        { code: 'CHILD', name: 'Child', minAge: 3, maxAge: 17 },
        { code: 'TODDLER', name: 'Toddler', minAge: 0, maxAge: 2 },
        { code: 'SENIOR', name: 'Senior', minAge: 65, maxAge: null },
        { code: 'PWD', name: 'PWD', minAge: null, maxAge: null }
      ]);
    }
  };

  // Load provider and service details
  useEffect(() => {
    // Wait for router to be ready before checking query parameters
    if (!router.isReady) {
      return;
    }

    if (!providerId || !serviceId) {
      setError(t('missingProviderService'));
      setIsLoading(false);
      return;
    }

    loadBookingData();
  }, [router.isReady, providerId, serviceId]);



  // Fetch availability data when service is loaded
  useEffect(() => {
    if (selectedService) {
      fetchAvailabilityData();
    }
  }, [selectedService, currentMonth]);

  // Update available time slots when date is selected
  useEffect(() => {
    if (bookingDetails.date && availabilityData[bookingDetails.date]) {
      setAvailableTimeSlots(availabilityData[bookingDetails.date].timeSlots || []);
    } else {
      setAvailableTimeSlots([]);
    }
  }, [bookingDetails.date, availabilityData]);

  // Handle profile completion for first-time Google users
  useEffect(() => {
    if (router.isReady && router.query.show_profile_completion === 'true' && user) {
      // Create a more reliable localStorage key that works for both manual and OAuth users
      // For Google OAuth users, we'll use email as a fallback if ID is not consistent
      const localStorageKey = user.id 
        ? `gosea_profile_completion_${user.id}`
        : user.email 
          ? `gosea_profile_completion_${btoa(user.email)}` // base64 encode email to avoid special characters
          : `gosea_profile_completion_unknown`;
      
      // Check if this is the first time the user is accessing the dashboard
      const hasSeenProfileCompletion = localStorage.getItem(localStorageKey);
      
      // Check if user has incomplete profile (only phone number is required)
      const hasIncompleteProfile = !user.profile?.phone;

      console.log('Booking page profile completion check:', {
        userId: user.id,
        userEmail: user.email,
        hasSeenProfileCompletion,
        hasIncompleteProfile,
        hasPhone: !!user.profile?.phone
      });

      // Only show profile completion modal if user has incomplete profile and hasn't seen the modal before
      if (hasIncompleteProfile && !hasSeenProfileCompletion) {
        console.log('Showing profile completion modal in booking page');
        // Show mandatory profile completion modal for first-time Google users
        setShowProfileCompletion(true);
        setIsMandatoryProfileCompletion(true);
      }

      // Remove the query parameter from URL in all cases
      const newQuery = { ...router.query };
      delete newQuery.show_profile_completion;
      router.replace({
        pathname: router.pathname,
        query: newQuery
      }, undefined, { shallow: true });
    }
  }, [router.isReady, router.query.show_profile_completion, user]);

  // Handle email verification success - auto-open sign-in modal
  useEffect(() => {
    if (router.isReady && router.query.verified === 'true' && router.query.openSignIn === 'true') {
      setShowSignInModal(true);
      // Clean up URL parameters
      const newQuery = { ...router.query };
      delete newQuery.verified;
      delete newQuery.openSignIn;
      router.replace({
        pathname: router.pathname,
        query: newQuery
      }, undefined, { shallow: true });
    }
  }, [router.isReady, router.query.verified, router.query.openSignIn]);

  // Auto-scroll to first error field
  const scrollToErrorField = (errors) => {
    const errorFieldOrder = ['service', 'route', 'date', 'time', 'passengers', 'adults', 'package'];
    const firstErrorField = errorFieldOrder.find(field => errors[field]);

    if (firstErrorField) {
      // Define field selectors for scrolling
      const fieldSelectors = {
        service: '[data-field="service"]',
        route: '[data-field="route"]',
        date: '[data-field="date"]',
        time: '[data-field="time"]',
        passengers: '[data-field="passengers"]',
        adults: '[data-field="adults"]',
        package: '[data-field="package"]'
      };

      const selector = fieldSelectors[firstErrorField];
      if (selector) {
        const element = document.querySelector(selector);
        if (element) {
          // Smooth scroll to element with offset for mobile header
          const yOffset = isMobile ? -100 : -50;
          const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;

          window.scrollTo({ top: y, behavior: 'smooth' });

          // Focus the element if it's focusable
          setTimeout(() => {
            if (element.focus) {
              element.focus();
            } else {
              // If the element itself isn't focusable, try to find a focusable child
              const focusableChild = element.querySelector('input, select, button, textarea');
              if (focusableChild) {
                focusableChild.focus();
              }
            }
          }, 300); // Wait for scroll to complete
        }
      }
    }
  };

  // Mobile detection useEffect
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Calendar helper functions
  const getDaysInMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  // Helper function to format date without timezone issues
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const isDateAvailable = (dateStr) => {
    return availabilityData[dateStr]?.available || false;
  };

  const isDateSelected = (dateStr) => {
    return bookingDetails.date === dateStr;
  };

  // Get available seats for a specific date
  const getAvailableSeats = (dateStr) => {
    return availabilityData[dateStr]?.availableSeats || 0;
  };

  // Get total capacity for a specific date
  const getTotalCapacity = (dateStr) => {
    return availabilityData[dateStr]?.totalCapacity || 0;
  };

  // Determine seat availability level for color coding
  const getSeatAvailabilityLevel = (dateStr) => {
    const available = getAvailableSeats(dateStr);
    const total = getTotalCapacity(dateStr);
    
    if (total === 0) return 'unavailable';
    
    const percentage = (available / total) * 100;
    
    if (percentage === 0) return 'full';
    if (percentage <= 25) return 'low';
    if (percentage <= 50) return 'medium';
    return 'high';
  };

  // Get seat availability color classes
  const getSeatAvailabilityColors = (level, isSelected, isPast) => {
    if (isPast) return 'text-gray-300 cursor-not-allowed';
    if (isSelected) return 'bg-amber-500 text-white';
    
    switch (level) {
      case 'unavailable':
      case 'full':
        return 'text-gray-300 cursor-not-allowed bg-gray-100';
      case 'low':
        return 'hover:bg-red-100 text-gray-900 bg-red-200 border-red-300';
      case 'medium':
        return 'hover:bg-yellow-100 text-gray-900 bg-yellow-200 border-yellow-300';
      case 'high':
        return 'hover:bg-green-100 text-gray-900 bg-green-200 border-green-300';
      default:
        return 'hover:bg-amber-100 text-gray-900';
    }
  };

  const handleDateSelect = (dateStr) => {
    if (isDateAvailable(dateStr)) {
      setBookingDetails(prev => ({ ...prev, date: dateStr, time: '' }));
      setShowCalendar(false);
      clearValidationError('date');
    }
  };

  const navigateMonth = (direction) => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(prev.getMonth() + direction);
      return newMonth;
    });
  };

  const fetchAvailabilityData = async () => {
    try {
      const year = currentMonth.getFullYear();
      const month = currentMonth.getMonth() + 1;

      // Generate availability data based on service schedules
      const availability = await generateAvailabilityFromSchedules(year, month);
      setAvailabilityData(availability);
    } catch (error) {
      console.error('Error fetching availability:', error);
      // Fallback to mock data if API fails
      const mockAvailability = generateMockAvailability(year, month);
      setAvailabilityData(mockAvailability);
    }
  };

  const generateAvailabilityFromSchedules = async (year, month) => {
    console.log('🔍 generateAvailabilityFromSchedules called for:', year, month, 'service:', selectedService?.id);
    const availability = {};
    const daysInMonth = new Date(year, month, 0).getDate();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const dateStr = formatDate(date);

      // Skip past dates
      if (date < today) continue;

      try {
        // Fetch timeslots and available capacity from API
        const url = `/api/services/${selectedService?.id}/timeslots?date=${dateStr}`;
        console.log('🔍 Fetching timeslots from:', url);
        const response = await fetch(url);
        console.log('🔍 Response status:', response.status, 'ok:', response.ok);
        if (response.ok) {
          const data = await response.json();
          console.log('🔍 API Response data for', dateStr, ':', data);
          
          // Check if date has available timeslots (not blocked)
          const hasAvailableSlots = data.success && data.data.timeSlots.length > 0;
          const isBlocked = data.data?.blockedCount > 0 && data.data.timeSlots.length === 0;
          
          if (hasAvailableSlots) {
            // Use new enhanced capacity data from the API response
            const totalCapacity = data.data.totalCapacity || 0;
            const timeslotDetails = data.data.timeslotDetails || [];
            
            // Calculate overall availability for the day based on timeslot details
            const totalAvailableSeats = timeslotDetails.reduce((sum, slot) => sum + slot.availableCapacity, 0);
            const totalBookedSeats = timeslotDetails.reduce((sum, slot) => sum + slot.bookedCapacity, 0);
            
            availability[dateStr] = {
              available: true,
              timeSlots: data.data.timeSlots,
              timeslotDetails: timeslotDetails, // New: detailed capacity per timeslot
              availableSeats: totalAvailableSeats,
              totalCapacity: totalCapacity,
              bookedSeats: totalBookedSeats,
              source: data.data.source,
              blockedCount: data.data.blockedCount || 0
            };
            console.log('✅ Date', dateStr, 'marked as available with', totalAvailableSeats, 'total available seats');
          } else {
            // Date is either blocked or has no schedule
            const blockReason = isBlocked ? 'blocked' : 'no_schedule';
            console.log('❌ Date', dateStr, 'marked as unavailable -', blockReason);
            availability[dateStr] = {
              available: false,
              timeSlots: [],
              timeslotDetails: [],
              availableSeats: 0,
              totalCapacity: data.data?.totalCapacity || 0,
              bookedSeats: 0,
              source: data.data?.source || blockReason,
              blockedCount: data.data?.blockedCount || 0,
              isBlocked: isBlocked
            };
          }
        } else {
          // If API fails, mark as unavailable
          availability[dateStr] = {
            available: false,
            timeSlots: [],
            timeslotDetails: [],
            availableSeats: 0,
            totalCapacity: 0,
            bookedSeats: 0,
            source: 'api_error'
          };
        }
      } catch (error) {
        console.error(`Error fetching timeslots for ${dateStr}:`, error);
        // If API fails, mark as unavailable
        availability[dateStr] = {
          available: false,
          timeSlots: [],
          timeslotDetails: [],
          availableSeats: 0,
          totalCapacity: 0,
          bookedSeats: 0,
          source: 'api_error'
        };
      }
    }

    return availability;
  };

  const generateMockAvailability = (year, month) => {
    const availability = {};
    const daysInMonth = new Date(year, month, 0).getDate();
    const today = new Date();

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const dateStr = formatDate(date);

      // Skip past dates
      if (date < today) continue;

      // Mock availability: available on weekdays and some weekends
      const isWeekend = date.getDay() === 0 || date.getDay() === 6;
      const isAvailable = !isWeekend || Math.random() > 0.3;

      if (isAvailable) {
        // Mock capacity data
        const totalCapacity = Math.floor(Math.random() * 20) + 10; // 10-30 capacity
        const bookedSeats = Math.floor(Math.random() * (totalCapacity * 0.8)); // Up to 80% booked
        const availableSeats = Math.max(0, totalCapacity - bookedSeats);
        
        availability[dateStr] = {
          available: true,
          timeSlots: [
            '09:00', '10:00', '11:00', '14:00', '15:00', '16:00'
          ].filter(() => Math.random() > 0.2), // Randomly remove some slots
          availableSeats: availableSeats,
          totalCapacity: totalCapacity,
          bookedSeats: bookedSeats
        };
      } else {
        availability[dateStr] = {
          available: false,
          timeSlots: [],
          availableSeats: 0,
          totalCapacity: 0,
          bookedSeats: 0
        };
      }
    }

    return availability;
  };

  const loadBookingData = async () => {
    try {
      setIsLoading(true);

      // Load provider details
      const providerResponse = await fetch(`/api/providers/${providerId}`);
      const providerData = await providerResponse.json();

      if (!providerData.success) {
        throw new Error(providerData.message || t('failedLoadProvider'));
      }

      setProvider(providerData.data);

      // Load individual service details with packages (instead of using provider's services list)
      const serviceResponse = await fetch(`/api/services/${serviceId}`);
      const serviceData = await serviceResponse.json();

      if (!serviceData.success) {
        throw new Error(serviceData.message || t('failedLoadService'));
      }

      setSelectedService(serviceData.service);

      // Load age ranges for the service
      await fetchAgeRanges(serviceId);

      // console.log('Service Data:', serviceData.service);
      // console.log('Service Route:', serviceData.service.routes);

      // Load available routes for this service
      if (serviceData.service.routes && serviceData.service.routes.length > 0) {
        setAvailableRoutes(serviceData.service.routes);

        // console.log('Available Routes:', availableRoutes);

        // Auto-select route if destination is specified
        if (destinationId) {
          const matchingRoute = serviceData.service.routes.find(
            sr => sr.destination?.id === destinationId
          );
          if (matchingRoute) {
            setSelectedRoute(matchingRoute);
          }
        }
        // Auto-select route if only one destination option is available
        else if (serviceData.service.routes.length === 1) {
          const singleRoute = serviceData.service.routes[0];
          setSelectedRoute(singleRoute);
          console.log('✅ Auto-selected single destination:', singleRoute.destination?.name || 'Unknown destination');
        }
      }

      // Auto-select package for package-based services
      if (serviceData.service.packages && serviceData.service.packages.length > 0 && !selectedPackage) {
        // First, try to find the most popular package (PREMIUM)
        const popularPackage = serviceData.service.packages.find(pkg =>
          pkg.packageType?.code === 'PREMIUM'
        );

        if (popularPackage) {
          setBookingDetails(prev => ({
            ...prev,
            selectedPackage: popularPackage.id
          }));
          console.log('✅ Auto-selected most popular package:', popularPackage.packageType.name);
        } else {
          // Fallback to first package
          const firstPackage = serviceData.service.packages[0];
          setBookingDetails(prev => ({
            ...prev,
            selectedPackage: firstPackage.id
          }));
          console.log('✅ Auto-selected first package:', firstPackage.packageType.name);
        }
      }

    } catch (error) {
      console.error('Error loading booking data:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Validate form fields (without authentication check)
  const validateFormFields = () => {
    const errors = {};

    if (!selectedService) {
      errors.service = t('pleaseSelectService');
    }

    if (selectedService?.serviceType.category.requiresDestination && !selectedRoute) {
      console.log('🚨 Destination validation failed:', {
        requiresDestination: selectedService?.serviceType.category.requiresDestination,
        selectedRoute: selectedRoute,
        serviceName: selectedService?.name
      });
      errors.route = t('pleaseSelectDestination');
    }

    if (!bookingDetails.date) {
      errors.date = t('pleaseSelectDate');
    }

    if (!bookingDetails.time) {
      errors.time = t('pleaseSelectTime');
    }

    // Validate passenger count
    const scenario = getPricingScenario();
    if (scenario === 'basic' || scenario === 'packages-only') {
      if (!bookingDetails.passengers || parseInt(bookingDetails.passengers) === 0) {
        errors.passengers = t('pleaseSelectPassengers');
      }
    } else {
      if (!bookingDetails.adults || parseInt(bookingDetails.adults) === 0) {
        errors.adults = t('atLeastOneAdult');
      }
    }

    // Validate package selection for scenarios that require it
    if ((scenario === 'packages-only' || scenario === 'full-variation') && !bookingDetails.selectedPackage) {
      errors.package = t('pleaseSelectPackage');
    }

    setValidationErrors(errors);

    // Auto-scroll to first error field on mobile when there are validation errors
    if (Object.keys(errors).length > 0 && isMobile) {
      setTimeout(() => scrollToErrorField(errors), 100); // Small delay to ensure error messages are rendered
    }

    return Object.keys(errors).length === 0;
  };

  // Complete validation including authentication
  const validateForm = () => {
    // First validate all form fields
    const fieldsValid = validateFormFields();

    // If fields are not valid, don't proceed with authentication check
    if (!fieldsValid) {
      return false;
    }

    // Only check authentication after all fields are valid
    if (!user) {
      setShowSignInModal(true);
      return false;
    }

    return true;
  };

  // Handle booking submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      const bookingData = {
        serviceId: selectedService.id,
        routeId: selectedRoute?.id,
        bookingDate: `${bookingDetails.date}T${bookingDetails.time}`,
        bookingTime: bookingDetails.time,
        passengers: {
          adults: parseInt(bookingDetails.adults),
          children: parseInt(bookingDetails.children),
          toddlers: parseInt(bookingDetails.toddlers),
          seniors: parseInt(bookingDetails.seniors),
          pwd: parseInt(bookingDetails.pwd)
        },
        selectedPackage: bookingDetails.selectedPackage,
        totalAmount: calculateTotalAmount(),
        specialRequests: bookingDetails.specialRequests || null
      };

      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.token}`
        },
        body: JSON.stringify(bookingData)
      });

      const data = await response.json();

      if (data.success) {
        showSuccessModal(
          t('bookingConfirmed'),
          `${t('bookingConfirmedMessage')} ${data.data.id}`,
          () => router.push('/dashboard')
        );
      } else {
        showErrorModal(t('bookingFailed'), data.message || t('failedCreateBooking'));
      }

    } catch (error) {
      console.error('Error creating booking:', error);
      showErrorModal(t('bookingError'), t('bookingErrorMessage'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Use selected service directly (now includes packages from individual service API)
  const serviceForScenario = selectedService;

  // Clear validation error for a specific field
  const clearValidationError = (field) => {
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Get age range display text
  const getAgeRangeText = (code) => {
    const ageRange = ageRanges.find(range => range.code === code);
    if (!ageRange) return '';

    if (ageRange.minAge === null && ageRange.maxAge === null) {
      return ''; // No age restriction (like PWD)
    }

    if (ageRange.maxAge === null) {
      return `${ageRange.minAge}+`;
    }

    if (ageRange.minAge === null) {
      return `0-${ageRange.maxAge}`;
    }

    return `${ageRange.minAge}-${ageRange.maxAge}`;
  };

  // Determine pricing scenario based on service data (matches DynamicPricingSection.js logic)
  const getPricingScenario = () => {
    if (!serviceForScenario) return 'basic';

    const hasPackages = serviceForScenario.packages && serviceForScenario.packages.length > 0;
    const hasAgePricing = serviceForScenario.agePricing || (hasPackages && serviceForScenario.packages.some(pkg => pkg.agePricing));

    if (!hasPackages && !hasAgePricing) return 'basic';           // Scenario 1
    if (hasPackages && !hasAgePricing) return 'packages-only';    // Scenario 2
    if (!hasPackages && hasAgePricing) return 'age-only';         // Scenario 3
    return 'full-variation';                                      // Scenario 4
  };

  // Get total passenger count based on scenario
  const getTotalPassengers = () => {
    const scenario = getPricingScenario();

    if (scenario === 'basic' || scenario === 'packages-only') {
      // For scenarios 1 and 2, use the single passengers field
      return parseInt(bookingDetails.passengers || 0);
    } else {
      // For scenarios 3 and 4, sum up age-based fields
      return parseInt(bookingDetails.adults || 0) +
        parseInt(bookingDetails.children || 0) +
        parseInt(bookingDetails.toddlers || 0) +
        parseInt(bookingDetails.seniors || 0) +
        parseInt(bookingDetails.pwd || 0);
    }
  };

  // Get price for specific age group based on current scenario and selected package
  const getPriceForAge = (ageGroup) => {
    if (!serviceForScenario) return 0;

    const scenario = getPricingScenario();
    const basePrice = parseFloat(serviceForScenario.basePrice);

    switch (scenario) {
      case 'basic':
        // Scenario 1: Uniform pricing for all
        return ageGroup === 'toddler' ? 0 : basePrice;

      case 'packages-only':
        // Scenario 2: Package-based pricing, uniform within package
        if (serviceForScenario.packages && bookingDetails.selectedPackage) {
          const selectedPackage = serviceForScenario.packages.find(pkg =>
            pkg.id === bookingDetails.selectedPackage ||
            pkg.packageType?.code?.toLowerCase() === bookingDetails.selectedPackage
          );
          if (selectedPackage) {
            return ageGroup === 'toddler' ? 0 : parseFloat(selectedPackage.basePrice);
          }
        }
        return ageGroup === 'toddler' ? 0 : basePrice;

      case 'age-only':
        // Scenario 3: Age-based pricing only
        if (serviceForScenario.agePricing && serviceForScenario.agePricing[ageGroup] !== undefined) {
          return parseFloat(serviceForScenario.agePricing[ageGroup]);
        }
        return ageGroup === 'toddler' ? 0 : basePrice;

      case 'full-variation':
        // Scenario 4: Both package and age-based pricing
        if (serviceForScenario.packages && bookingDetails.selectedPackage) {
          const selectedPackage = serviceForScenario.packages.find(pkg =>
            pkg.id === bookingDetails.selectedPackage ||
            pkg.packageType?.code?.toLowerCase() === bookingDetails.selectedPackage
          );
          if (selectedPackage && selectedPackage.agePricing && selectedPackage.agePricing[ageGroup] !== undefined) {
            return parseFloat(selectedPackage.agePricing[ageGroup]);
          }
          if (selectedPackage) {
            return ageGroup === 'toddler' ? 0 : parseFloat(selectedPackage.basePrice);
          }
        }
        // Fallback to service age pricing
        if (serviceForScenario.agePricing && serviceForScenario.agePricing[ageGroup] !== undefined) {
          return parseFloat(serviceForScenario.agePricing[ageGroup]);
        }
        return ageGroup === 'toddler' ? 0 : basePrice;

      default:
        return ageGroup === 'toddler' ? 0 : basePrice;
    }
  };

  // Calculate total amount based on scenario
  const calculateTotalAmount = () => {
    if (!serviceForScenario) return 0;

    const scenario = getPricingScenario();
    const adults = parseInt(bookingDetails.adults || 0);
    const children = parseInt(bookingDetails.children || 0);
    const toddlers = parseInt(bookingDetails.toddlers || 0);
    const seniors = parseInt(bookingDetails.seniors || 0);
    const pwd = parseInt(bookingDetails.pwd || 0);

    if (scenario === 'basic' || scenario === 'packages-only') {
      // For basic and package-only scenarios, use total passengers with uniform pricing
      const totalPassengers = getTotalPassengers();
      const pricePerPerson = getPriceForAge('adult'); // Use adult price as base
      return totalPassengers * pricePerPerson;
    } else {
      // For age-based scenarios, calculate per age group
      return (adults * getPriceForAge('adult')) +
        (children * getPriceForAge('child')) +
        (toddlers * getPriceForAge('toddler')) +
        (seniors * getPriceForAge('senior')) +
        (pwd * getPriceForAge('pwd'));
    }
  };

  // Get package details for display (dynamic from database)
  const getPackageDetails = (packageIdentifier) => {
    if (!serviceForScenario || !serviceForScenario.packages) {
      // Fallback for services without packages
      return {
        name: t('standardService'),
        description: t('basicServiceOffering'),
        includes: [t('transportation'), t('professionalGuide'), t('safetyEquipment')]
      };
    }

    // Find package by ID or packageType code
    const packageData = serviceForScenario.packages.find(pkg =>
      pkg.id === packageIdentifier ||
      pkg.packageType?.code?.toLowerCase() === packageIdentifier
    );

    if (packageData) {
      return {
        name: packageData.packageType?.name || packageData.name || 'Package',
        description: packageData.description || packageData.packageType?.description || t('packageOffering'),
        includes: packageData.includedItems || packageData.includes || packageData.packageType?.includes || [t('serviceIncluded')],
        basePrice: packageData.basePrice
      };
    }

    // Fallback
    return {
      name: t('standardService'),
      description: t('basicServiceOffering'),
      includes: [t('transportation'), t('professionalGuide'), t('safetyEquipment')]
    };
  };

  // Get selected package details for booking summary
  const getSelectedPackageDetails = () => {
    return getPackageDetails(bookingDetails.selectedPackage);
  };

  // Modal helpers
  const showErrorModal = (title, message, details = null) => {
    setModalConfig({ type: 'error', title, message, details });
    setShowModal(true);
  };



  const showSuccessModal = (title, message, onClose = null) => {
    setModalConfig({
      type: 'success',
      title,
      message,
      onClose: onClose || (() => setShowModal(false))
    });
    setShowModal(true);
  };

  console.log('Selected Service:', selectedService);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar currentPage="boats" />

        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading booking details...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !provider || !selectedService) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar currentPage="boats" />

        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="text-6xl mb-4">❌</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Booking Not Available
            </h3>
            <p className="text-gray-600 mb-6">
              {error || 'The booking information could not be loaded.'}
            </p>
            <button
              onClick={() => router.push('/search')}
              className="px-6 py-3 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors"
            >
              Back to Search
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const pageName = "Book Your Trip";

  return (
    <>
      <Head>
        <title>{pageName} - GoSea</title>
        <meta name="description" content="Book your boat service with GoSea. Discover and book amazing boat experiences in Malaysia. Snorkeling trips, island hopping, and passenger transportation services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <div className="min-h-screen bg-gray-50">
        <Navbar currentPage="boats" />


        <div className="max-w-7xl mx-auto px-4 py-4 md:py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-4 md:mb-6">
            <h1 className="text-lg lg:text-2xl font-bold text-gray-900">
              {t('bookService')}
            </h1>
            <button
              onClick={() => router.back()}
              className="hidden sm:flex items-center text-gray-600 hover:text-gray-900 mr-4"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-1" />
              {t('back')}
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Booking Form */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  {t('bookingDetails')}
                </h2>

                <div className="space-y-6">
                  {/* Service Info */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">
                      {selectedService.serviceType.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      Provider: {provider.displayName || provider.companyName}
                    </p>
                    {selectedService.description && (
                      <p className="text-sm text-gray-600">
                        {selectedService.description}
                      </p>
                    )}
                  </div>

                  {/* Route Selection (for passenger transport) */}
                  {availableRoutes.length > 0 && (
                    <div data-field="route">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <span className="flex items-center">
                          <MapPinIcon className="w-4 h-4 mr-2" />
                          {t('selectDestination')} <span className="text-red-500 ml-1">*</span>
                        </span>
                      </label>
                      <select
                        value={selectedRoute?.id || ''}
                        onChange={(e) => {
                          const route = availableRoutes.find(r => r.id === e.target.value);
                          setSelectedRoute(route);
                          if (route) {
                            clearValidationError('route');
                          }
                        }}
                        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent ${validationErrors.route ? 'border-red-500' : ''}`}
                        required
                      >
                        <option value="">{t('selectDestination')}</option>
                        {availableRoutes.map((route) => (
                          <option key={route.id} value={route.id}>
                            {route.departureJetty?.name || 'Unknown departure'} → {route.destination?.name || 'Unknown destination'}
                          </option>
                        ))}
                      </select>
                      {validationErrors.route && (
                        <p className="mt-1 text-sm text-red-600">{validationErrors.route}</p>
                      )}
                    </div>
                  )}

                  {/* Enhanced Date and Time Selection */}
                  <div className="space-y-4">
                    {/* Date Selection with Calendar */}
                    <div className="relative" data-field="date">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <span className="flex items-center">
                          <CalendarIcon className="w-4 h-4 mr-2" />
                          {t('selectDate')} <span className="text-red-500 ml-1">*</span>
                        </span>
                      </label>

                      {/* Selected Date Display */}
                      <div
                        tabIndex="0"
                        role="button"
                        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent cursor-pointer ${validationErrors.date ? 'border-red-500' : 'border-gray-300'}`}
                        onClick={() => setShowCalendar(!showCalendar)}
                      >
                        {bookingDetails.date ? (
                          <span className="text-gray-900">
                            {new Date(bookingDetails.date).toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                        ) : (
                          <span className="text-gray-500">{t('clickToSelectDate')}</span>
                        )}
                      </div>

                      {/* Calendar Dropdown/Modal */}
                      {showCalendar && (
                        <>
                          {/* Mobile Modal Backdrop */}
                          {isMobile && (
                            <div
                              className="fixed inset-0 bg-black bg-opacity-50 z-40"
                              onClick={() => setShowCalendar(false)}
                            />
                          )}

                          {/* Calendar Container */}
                          <div className={`${
                            isMobile
                              ? 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-80 max-w-[90vw]'
                              : 'absolute z-50 mt-1 w-full max-w-sm'
                          } bg-white border border-gray-300 rounded-lg shadow-lg p-4`}>

                            {/* Mobile Close Button */}
                            {isMobile && (
                              <div className="flex justify-end mb-2">
                                <button
                                  type="button"
                                  onClick={() => setShowCalendar(false)}
                                  className="text-gray-400 hover:text-gray-600"
                                >
                                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              </div>
                            )}

                            {/* Calendar Header */}
                            <div className="flex items-center justify-between mb-4">
                              <button
                                type="button"
                                onClick={() => navigateMonth(-1)}
                                className="p-1 hover:bg-gray-100 rounded"
                              >
                                <ChevronLeftIcon className="w-5 h-5" />
                              </button>
                              <h3 className="text-lg font-semibold">
                                {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                              </h3>
                              <button
                                type="button"
                                onClick={() => navigateMonth(1)}
                                className="p-1 hover:bg-gray-100 rounded"
                              >
                                <ChevronRightIcon className="w-5 h-5" />
                              </button>
                            </div>

                            {/* Calendar Grid */}
                            <div className="grid grid-cols-7 gap-1 mb-2">
                              {[t('sun'), t('mon'), t('tue'), t('wed'), t('thu'), t('fri'), t('sat')].map(day => (
                                <div key={day} className="text-center text-xs font-medium text-gray-500 py-2">
                                  {day}
                                </div>
                              ))}
                            </div>

                            <div className="grid grid-cols-7 gap-1">
                              {/* Empty cells for days before month starts */}
                              {Array.from({ length: getFirstDayOfMonth(currentMonth) }).map((_, index) => (
                                <div key={`empty-${index}`} className="h-8"></div>
                              ))}

                              {/* Calendar days */}
                              {Array.from({ length: getDaysInMonth(currentMonth) }).map((_, index) => {
                                const day = index + 1;
                                const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
                                const dateStr = formatDate(date);
                                const isAvailable = isDateAvailable(dateStr);
                                const isSelected = isDateSelected(dateStr);
                                const today = new Date();
                                today.setHours(0, 0, 0, 0);
                                const isPast = date < today;
                                const availableSeats = getAvailableSeats(dateStr);
                                const totalCapacity = getTotalCapacity(dateStr);
                                const availabilityLevel = getSeatAvailabilityLevel(dateStr);
                                const colorClasses = getSeatAvailabilityColors(availabilityLevel, isSelected, isPast);

                                return (
                                  <button
                                    key={day}
                                    type="button"
                                    onClick={() => handleDateSelect(dateStr)}
                                    disabled={!isAvailable || isPast || availabilityLevel === 'full' || availabilityLevel === 'unavailable'}
                                    className={`relative h-12 text-sm rounded transition-colors border ${colorClasses}`}
                                    title={isAvailable && !isPast ? `${availableSeats}/${totalCapacity} seats available` : 'Unavailable'}
                                  >
                                    <div className="flex flex-col items-center justify-center h-full">
                                      <span className="font-medium">{day}</span>
                                      {isAvailable && !isPast && totalCapacity > 0 && (
                                        <span className="text-[10px] leading-none mt-0.5">
                                          {availableSeats}/{totalCapacity}
                                        </span>
                                      )}
                                    </div>
                                  </button>
                                );
                              })}
                            </div>

                            {/* Enhanced Legend */}
                            <div className="mt-4">
                              {/* Availability Colors Legend */}
                              <div className="grid grid-cols-2 gap-2 text-xs">
                                <div className="flex items-center">
                                  <div className="w-3 h-3 bg-green-200 border border-green-300 rounded mr-1"></div>
                                  <span>{t('highAvailability') || 'High availability'}</span>
                                </div>
                                <div className="flex items-center">
                                  <div className="w-3 h-3 bg-yellow-200 border border-yellow-300 rounded mr-1"></div>
                                  <span>{t('mediumAvailability') || 'Medium availability'}</span>
                                </div>
                                <div className="flex items-center">
                                  <div className="w-3 h-3 bg-red-200 border border-red-300 rounded mr-1"></div>
                                  <span>{t('lowAvailability') || 'Low availability'}</span>
                                </div>
                                <div className="flex items-center">
                                  <div className="w-3 h-3 bg-gray-100 rounded mr-1"></div>
                                  <span>{t('unavailable')}</span>
                                </div>
                              </div>
                              
                              {/* Seat Availability Format Legend */}
                              <div className="mt-2 border-gray-200">
                                <div className="flex items-center text-xs">
                                  <div className="flex items-center justify-center w-6 h-6 bg-gray-50 border border-gray-200 rounded text-[10px] mr-2">
                                    <div className="text-center">
                                      {/* <div className="font-medium leading-none">15</div> */}
                                      <div className="leading-none">8/15</div>
                                    </div>
                                  </div>
                                  <span>{t('seatAvailabilityLegend') || 'Numbers show available/total seats'}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </>
                      )}

                      {validationErrors.date && (
                        <p className="mt-1 text-sm text-red-600">{validationErrors.date}</p>
                      )}
                    </div>

                    {/* Time Selection */}
                    {bookingDetails.date && (
                      <div data-field="time">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <span className="flex items-center">
                            <ClockIcon className="w-4 h-4 mr-2" />
                            {t('availableTimeSlots')} <span className="text-red-500 ml-1">*</span>
                          </span>
                        </label>

                        {availableTimeSlots.length > 0 ? (
                          <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
                            {availableTimeSlots.map((timeSlot) => (
                              <button
                                key={timeSlot}
                                type="button"
                                onClick={() => {
                                  setBookingDetails(prev => ({ ...prev, time: timeSlot }));
                                  clearValidationError('time');
                                }}
                                className={`px-3 py-2 text-sm border rounded-md transition-colors ${bookingDetails.time === timeSlot
                                  ? 'bg-amber-500 text-white'
                                  : 'hover:bg-amber-500 hover:text-white'
                                  }`}
                              >
                                {timeSlot}
                              </button>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500 italic">
                            {t('noTimeSlotsAvailable')}
                          </p>
                        )}
                        {validationErrors.time && (
                          <p className="mt-1 text-sm text-red-600">{validationErrors.time}</p>
                        )}
                      </div>
                    )}


                  </div>

                  {/* Package Selection (conditional) */}
                  {(getPricingScenario() === 'packages-only' || getPricingScenario() === 'full-variation') && (
                    <div data-field="package">
                      <label className="block text-sm font-medium text-gray-700 mb-4">
                        <span className="flex items-center">
                          <CurrencyDollarIcon className="w-4 h-4 mr-2" />
                          {t('selectPackage')} <span className="text-red-500 ml-1">*</span>
                        </span>
                      </label>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {serviceForScenario.packages && serviceForScenario.packages.length > 0 ? (
                          (() => {
                            // Find package with highest base price for "Most Popular" tag
                            const getHighestPricePackage = (packages) => {
                              return packages.reduce((highest, current) => {
                                const currentPrice = parseFloat(current.basePrice) || 0;
                                const highestPrice = parseFloat(highest.basePrice) || 0;
                                return currentPrice > highestPrice ? current : highest;
                              });
                            };
                            
                            const mostPopularPackage = getHighestPricePackage(serviceForScenario.packages);
                            
                            return serviceForScenario.packages.map((packageData) => {
                            const isSelected = bookingDetails.selectedPackage === packageData.id ||
                              bookingDetails.selectedPackage === packageData.packageType?.code?.toLowerCase();

                            return (
                              <div
                                key={packageData.id}
                                className={`border-2 rounded-lg p-3 relative transition-colors cursor-pointer ${isSelected
                                  ? 'border-amber-500 bg-amber-50'
                                  : 'border-gray-200 hover:border-amber-300 hover:bg-amber-50'
                                  }`}
                                onClick={() => {
                                  setBookingDetails(prev => ({
                                    ...prev,
                                    selectedPackage: packageData.id
                                  }));
                                  clearValidationError('package');
                                }}
                              >
                                {/* Popular Badge for Highest Price Package */}
                                {packageData.id === mostPopularPackage.id && (
                                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                    <span className="bg-amber-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                                      {t('mostPopular')}
                                    </span>
                                  </div>
                                )}

                                <div className="flex items-center justify-between mb-3 mt-2">
                                  <div className={`${!packageData.agePricing ? 'max-w-[60%] sm:max-w-[80%]' : 'max-w-full'}`}>
                                    <h3 className="text-base font-semibold text-gray-900">
                                      {packageData.packageType.name}
                                    </h3>
                                    <p className="text-xs text-gray-600">
                                      {packageData.description || packageData.packageType.description}
                                    </p>
                                  </div>

                                  {/* Only display if age pricing is not available */}
                                  {!packageData.agePricing && (
                                    <div className="text-right">
                                      <div className="text-lg font-bold text-amber-600">
                                        RM {packageData.basePrice}
                                      </div>
                                      <div className="text-xs text-gray-500">{t('perPerson')}</div>
                                    </div>
                                  )}
                                </div>

                                {/* Dynamic Pricing Grid */}
                                {packageData.agePricing && (
                                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-md mb-3">
                                    {packageData.agePricing.adult && (
                                      <div className="flex flex-col items-center justify-center text-center p-2 bg-gray-50 rounded border border-gray-200">
                                        <div className="font-medium text-xs text-gray-900">{t('adults')}</div>
                                        <div className="text-amber-600 text-sm font-bold">RM {packageData.agePricing.adult}</div>
                                      </div>
                                    )}
                                    {packageData.agePricing.child && (
                                      <div className="flex flex-col items-center justify-center text-center p-2 bg-gray-50 rounded border border-gray-200">
                                        <div className="font-medium text-xs text-gray-900">{t('children')}</div>
                                        <div className="text-amber-600 text-sm font-bold">RM {packageData.agePricing.child}</div>
                                      </div>
                                    )}
                                    {packageData.agePricing.toddler !== undefined && (
                                      <div className="flex flex-col items-center justify-center text-center p-2 bg-gray-50 rounded border border-gray-200">
                                        <div className="font-medium text-xs text-gray-900">{t('toddlers')}</div>
                                        <div className="text-amber-600 text-sm font-bold">
                                          {packageData.agePricing.toddler === 0 ? t('free') : `RM ${packageData.agePricing.toddler}`}
                                        </div>
                                      </div>
                                    )}
                                    {packageData.agePricing.senior && (
                                      <div className="flex flex-col items-center justify-center text-center p-2 bg-gray-50 rounded border border-gray-200">
                                        <div className="font-medium text-xs text-gray-900">{t('seniors')}</div>
                                        <div className="text-amber-600 text-sm font-bold">RM {packageData.agePricing.senior}</div>
                                      </div>
                                    )}
                                    {packageData.agePricing.pwd && (
                                      <div className="flex flex-col items-center justify-center text-center p-2 bg-gray-50 rounded border border-gray-200">
                                        <div className="font-medium text-xs text-gray-900">{t('pwd')}</div>
                                        <div className="text-amber-600 text-sm font-bold">RM {packageData.agePricing.pwd}</div>
                                      </div>
                                    )}
                                  </div>
                                )}

                                {/* Dynamic Includes with Overflow Handling */}
                                <div className="mt-3">
                                  <div className="flex flex-wrap gap-1">
                                    {packageData.includedItems.slice(0, 100).map((item, idx) => (
                                      <span
                                        key={idx}
                                        className="inline-flex items-center px-2 py-1 rounded-full text-[0.7rem] font-medium bg-sky-50 text-sky-600"
                                      >
                                        ✓ {item}
                                      </span>
                                    ))}
                                    {packageData.includedItems.length > 100 && (
                                      <div className="relative group">
                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sky-100 text-sky-600 cursor-help">
                                          +{packageData.includedItems.length - 100} more
                                        </span>

                                        {/* Hover Tooltip */}
                                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap max-w-xs">
                                          <div className="text-center">
                                            <div className="mb-1">{t('additionalItems')}:</div>
                                            <div className="space-y-1">
                                              {packageData.includedItems.slice(100).map((item, idx) => (
                                                <div key={idx}>✓ {item}</div>
                                              ))}
                                            </div>
                                          </div>
                                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          });
                        })()
                        ) : (
                          // Fallback for services without packages (shouldn't show in package scenarios)
                          <div className="text-center text-gray-500 py-8">
                            {t('noPackagesAvailable')}
                          </div>
                        )}
                      </div>
                      {validationErrors.package && (
                        <p className="mt-1 text-sm text-red-600">{validationErrors.package}</p>
                      )}
                    </div>
                  )}

                  {/* Passenger Selection - Conditional based on scenario */}
                  <div data-field="passengers">
                    {(() => {
                      const scenario = getPricingScenario();

                      if (scenario === 'basic' || scenario === 'packages-only') {
                        // Scenarios 1 & 2: Single passenger field
                        return (
                          <>
                            <label className="block text-sm font-medium text-gray-700 mb-4">
                              <span className="flex items-center">
                                <UsersIcon className="w-4 h-4 mr-2" />
                                {t('selectPassengers')} <span className="text-red-500 ml-1">*</span>
                              </span>
                            </label>

                            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                              <div>
                                <div className="font-medium text-gray-900">{t('passengers').charAt(0).toUpperCase() + t('passengers').slice(1)}</div>
                                <div className="text-sm text-gray-500">
                                  RM {getPriceForAge('adult')} {t('perPerson')}
                                </div>
                              </div>
                              <div className="flex items-center space-x-3">
                                <button
                                  type="button"
                                  onClick={() => setBookingDetails(prev => ({
                                    ...prev,
                                    passengers: Math.max(1, parseInt(prev.passengers) - 1)
                                  }))}
                                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                  disabled={bookingDetails.passengers <= 1}
                                >
                                  -
                                </button>
                                <span className="w-8 text-center font-medium">{bookingDetails.passengers}</span>
                                <button
                                  type="button"
                                  onClick={() => setBookingDetails(prev => ({
                                    ...prev,
                                    passengers: Math.min(((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50), parseInt(prev.passengers) + 1)
                                  }))}
                                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                  disabled={getTotalPassengers() >= ((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50)}
                                >
                                  +
                                </button>
                              </div>
                            </div>
                          </>
                        );
                      } else {
                        // Scenarios 3 & 4: Age-based breakdown
                        return (
                          <>
                            <label className="block text-sm font-medium text-gray-700 mb-4">
                              <span className="flex items-center">
                                <UsersIcon className="w-4 h-4 mr-2" />
                                {t('numberOfPassengers')} <span className="text-red-500 ml-1">*</span>
                              </span>
                            </label>

                            <div className="space-y-4" data-field="adults">
                              {/* Adults */}
                              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div>
                                  <div className="text-sm font-normal sm:text-md sm:font-medium text-gray-900">
                                    {t('adults')} {getAgeRangeText('ADULTS') ? `(${getAgeRangeText('ADULTS')} ${t('yearsOld')})` : ''}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    RM {getPriceForAge('adult')} per person
                                  </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      adults: Math.max(1, parseInt(prev.adults) - 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={bookingDetails.adults <= 1}
                                  >
                                    -
                                  </button>
                                  <span className="w-8 text-center font-medium">{bookingDetails.adults}</span>
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      adults: Math.min(((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50), parseInt(prev.adults) + 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={getTotalPassengers() >= ((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50)}
                                  >
                                    +
                                  </button>
                                </div>
                              </div>

                              {/* Children */}
                              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div>
                                  <div className="text-sm font-normal sm:text-md  sm:font-medium text-gray-900">
                                    {t('children')} {getAgeRangeText('CHILDREN') ? `(${getAgeRangeText('CHILDREN')} ${t('yearsOld')})` : ''}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    RM {getPriceForAge('child')} per person
                                  </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      children: Math.max(0, parseInt(prev.children) - 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={bookingDetails.children <= 0}
                                  >
                                    -
                                  </button>
                                  <span className="w-8 text-center font-medium">{bookingDetails.children}</span>
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      children: Math.min(((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50), parseInt(prev.children) + 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={getTotalPassengers() >= ((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50)}
                                  >
                                    +
                                  </button>
                                </div>
                              </div>

                              {/* Toddlers */}
                              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div>
                                  <div className="text-sm font-normal sm:text-md  sm:font-medium text-gray-900">
                                    {t('toddlers')} {getAgeRangeText('TODDLERS') ? `(${getAgeRangeText('TODDLERS')} ${t('yearsOld')})` : '' }
                                  </div>
                                  <div className="text-sm text-emerald-600 font-medium">
                                    {getPriceForAge('toddler') === 0 ? t('free') : `RM ${getPriceForAge('toddler')}`}
                                  </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      toddlers: Math.max(0, parseInt(prev.toddlers) - 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={bookingDetails.toddlers <= 0}
                                  >
                                    -
                                  </button>
                                  <span className="w-8 text-center font-medium">{bookingDetails.toddlers}</span>
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      toddlers: Math.min(((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50), parseInt(prev.toddlers) + 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={getTotalPassengers() >= ((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50)}
                                  >
                                    +
                                  </button>
                                </div>
                              </div>

                              {/* Seniors */}
                              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div>
                                  <div className="text-sm font-normal sm:text-md  sm:font-medium text-gray-900">
                                    {t('seniors')} {getAgeRangeText('SENIORS') ? `(${getAgeRangeText('SENIORS')} ${t('yearsOld')})` : ''}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    RM {getPriceForAge('senior')} per person
                                  </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      seniors: Math.max(0, parseInt(prev.seniors) - 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={bookingDetails.seniors <= 0}
                                  >
                                    -
                                  </button>
                                  <span className="w-8 text-center font-medium">{bookingDetails.seniors}</span>
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      seniors: Math.min(((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50), parseInt(prev.seniors) + 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={getTotalPassengers() >= ((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50)}
                                  >
                                    +
                                  </button>
                                </div>
                              </div>

                              {/* PWD */}
                              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div>
                                  <div className="text-sm font-normal sm:text-md  sm:font-medium text-gray-900">
                                    {t('pwd')} {getAgeRangeText('PWD') ? `(${getAgeRangeText('PWD')} ${t('yearsOld')})` : ''}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    RM {getPriceForAge('pwd')} per person
                                  </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      pwd: Math.max(0, parseInt(prev.pwd) - 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={bookingDetails.pwd <= 0}
                                  >
                                    -
                                  </button>
                                  <span className="w-8 text-center font-medium">{bookingDetails.pwd}</span>
                                  <button
                                    type="button"
                                    onClick={() => setBookingDetails(prev => ({
                                      ...prev,
                                      pwd: Math.min(((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50), parseInt(prev.pwd) + 1)
                                    }))}
                                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={getTotalPassengers() >= ((selectedService.calculatedCapacity || selectedService.maxCapacity) || 50)}
                                  >
                                    +
                                  </button>
                                </div>
                              </div>
                            </div>
                            {validationErrors.adults && (
                              <p className="mt-1 text-sm text-red-600">{validationErrors.adults}</p>
                            )}
                          </>
                        );
                      }
                    })()}
                    {validationErrors.passengers && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.passengers}</p>
                    )}

                    {(selectedService.calculatedCapacity || selectedService.maxCapacity) && (
                      <p className="text-xs text-gray-500 mt-2">
                        {t('maxCapacity')}: {selectedService.calculatedCapacity || selectedService.maxCapacity} {t('passengers')} ({t('total')}: {getTotalPassengers()})
                      </p>
                    )}
                    {(validationErrors.passengers || validationErrors.adults) && (
                      <p className="mt-1 text-sm text-red-600">
                        {validationErrors.passengers || validationErrors.adults}
                      </p>
                    )}
                  </div>

                  {/* Special Requests */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <span className="flex items-center">
                        <ChatBubbleLeftEllipsisIcon className="w-4 h-4 mr-2" />
                        {t('specialRequestsOptional')}
                      </span>
                    </label>
                    <textarea
                      value={bookingDetails.specialRequests}
                      onChange={(e) => setBookingDetails(prev => ({ ...prev, specialRequests: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                      placeholder={t('specialRequestsPlaceholder')}
                    />
                  </div>


                </div>
              </div>
            </div>

            {/* Booking Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-16">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {t('bookingSummary')}
                </h3>

                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">{t('service')}:</span>
                    <span className="font-medium">{selectedService.serviceType.name}</span>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:justify-between">
                    <span className="text-gray-600">{t('provider')}:</span>
                    <span className="font-medium sm:text-right">{provider.displayName || provider.companyName}</span>
                  </div>

                  {/* Package info (conditional) */}
                  {(getPricingScenario() === 'packages-only' || getPricingScenario() === 'full-variation') && (
                    <div className="flex flex-col sm:flex-row sm:justify-between">
                      <span className="text-gray-600">{t('package')}:</span>
                      <span className="font-medium sm:text-right">
                        {getSelectedPackageDetails().name}
                      </span>
                    </div>
                  )}

                  {selectedRoute && (
                    <div className="flex flex-col sm:flex-row sm:justify-between">
                      <span className="text-gray-600">{t('route')}:</span>
                      <span className="font-medium sm:text-right">
                        {selectedRoute.departureJetty?.name || 'Unknown departure'} → {selectedRoute.destination?.name || 'Unknown destination'}
                      </span>
                    </div>
                  )}

                  <div className="flex justify-between">
                    <span className="text-gray-600">{t('date')}:</span>
                    <span className="font-medium">{bookingDetails.date || t('notSelected')}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-gray-600">{t('time')}:</span>
                    <span className="font-medium">{bookingDetails.time || t('notSelected')}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-gray-600">{t('totalPassengers')}:</span>
                    <span className="font-medium">{getTotalPassengers()}</span>
                  </div>

                  <hr className="my-3" />

                  {/* Dynamic pricing breakdown */}
                  <div className="space-y-2 text-sm">
                    {(() => {
                      const scenario = getPricingScenario();

                      if (scenario === 'basic' || scenario === 'packages-only') {
                        // Scenarios 1 & 2: Show total passengers
                        return (
                          <div className="flex justify-between">
                            <span className="text-gray-600">{t('person')} × {getTotalPassengers()}:</span>
                            <span className="font-medium">
                              RM {(getPriceForAge('adult') * getTotalPassengers()).toFixed(2)}
                            </span>
                          </div>
                        );
                      } else {
                        // Scenarios 3 & 4: Show age-based breakdown
                        return (
                          <>
                            {bookingDetails.adults > 0 && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">{t('adults')} × {bookingDetails.adults}:</span>
                                <span className="font-medium">
                                  RM {(getPriceForAge('adult') * bookingDetails.adults).toFixed(2)}
                                </span>
                              </div>
                            )}

                            {bookingDetails.children > 0 && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">{t('children')} × {bookingDetails.children}:</span>
                                <span className="font-medium">
                                  RM {(getPriceForAge('child') * bookingDetails.children).toFixed(2)}
                                </span>
                              </div>
                            )}

                            {bookingDetails.toddlers > 0 && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">{t('toddlers')} × {bookingDetails.toddlers}:</span>
                                <span className="font-medium text-emerald-600">
                                  {getPriceForAge('toddler') === 0 ? t('free') : `RM ${(getPriceForAge('toddler') * bookingDetails.toddlers).toFixed(2)}`}
                                </span>
                              </div>
                            )}

                            {bookingDetails.seniors > 0 && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">{t('seniors')} × {bookingDetails.seniors}:</span>
                                <span className="font-medium">
                                  RM {(getPriceForAge('senior') * bookingDetails.seniors).toFixed(2)}
                                </span>
                              </div>
                            )}

                            {bookingDetails.pwd > 0 && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">PWD × {bookingDetails.pwd}:</span>
                                <span className="font-medium text-gray-600">
                                  RM {(getPriceForAge('pwd') * bookingDetails.pwd).toFixed(2)}
                                </span>
                              </div>
                            )}
                          </>
                        );
                      }
                    })()}
                  </div>

                  <hr className="my-3" />

                  <div className="flex justify-between text-lg font-semibold">
                    <span>{t('totalPrice')}:</span>
                    <span className="text-amber-600">RM {calculateTotalAmount()}</span>
                  </div>
                </div>

                {/* Confirm Booking Button */}
                <div className="mt-6">
                  <button
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    className="w-full px-6 py-3 bg-amber-500 text-white rounded-lg hover:bg-amber-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
                  >
                    {isSubmitting ? 'Creating Booking...' : `${t('confirmBooking')} - RM ${calculateTotalAmount()}`}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Footer />

        {/* Modal */}
        <StandardModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          type={modalConfig.type}
          title={modalConfig.title}
          message={modalConfig.message}
          details={modalConfig.details}
          onConfirm={modalConfig.onClose}
        />

        {/* Sign In Modal */}
        <SignInModal
          isOpen={showSignInModal}
          onClose={() => setShowSignInModal(false)}
          onSwitchToSignUp={() => {
            setShowSignInModal(false);
            setShowSignUpModal(true);
          }}
          onLoginSuccess={() => {
            // Login successful - no form data restoration needed
          }}
        />

        {/* Sign Up Modal */}
        <SignUpModal
          isOpen={showSignUpModal}
          onClose={() => {
            setShowSignUpModal(false);
          }}
          onSwitchToSignIn={() => {
            setShowSignUpModal(false);
            setShowSignInModal(true);
          }}
          onSignUpSuccess={() => {
            // Sign-up successful - no form data restoration needed
          }}
        />

        {/* Profile Completion Modal */}
        {showProfileCompletion && (
          <ProfileCompletionModal
            isOpen={showProfileCompletion}
            onClose={() => {
              setShowProfileCompletion(false);
              setIsMandatoryProfileCompletion(false);
              // Mark as seen so it doesn't show again
              // Use the same reliable key we used for checking
              const localStorageKey = user.id 
                ? `gosea_profile_completion_${user.id}`
                : user.email 
                  ? `gosea_profile_completion_${btoa(user.email)}`
                  : `gosea_profile_completion_unknown`;
              
              localStorage.setItem(localStorageKey, "true");
            }}
            user={user}
            mandatory={isMandatoryProfileCompletion}
          />
        )}

      </div>
    </>
  );
}
