import '../styles/globals.css';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from '../contexts/AuthContext';
import { LanguageProvider } from '../contexts/LanguageContext';
import { ModalProvider } from '../contexts/ModalContext';
import ScrollToTopButton from '../components/ScrollToTopButton';
import GlobalModals from '../components/GlobalModals';
import Head from 'next/head';

function GoSeaApp({ Component, pageProps }) {
  return (
    <LanguageProvider>
      <AuthProvider>
        <ModalProvider>
          <Head>
            <link rel="icon" href="/gosea_favicon.png" />
            <link rel="shortcut icon" href="/gosea_favicon.png" />
            <link rel="apple-touch-icon" href="/gosea_favicon.png" />
          </Head>
          <Component {...pageProps} />
          <ScrollToTopButton />
          <GlobalModals />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                theme: {
                  primary: 'green',
                  secondary: 'black',
                },
              },
            }}
          />
        </ModalProvider>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default GoSeaApp;
