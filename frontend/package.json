{"name": "gosea-frontend", "version": "0.1.0", "description": "GoSea Platform Frontend - Next.js Application", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start -H 0.0.0.0", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist"}, "dependencies": {"@heroicons/react": "^2.0.0", "axios": "^1.6.0", "clsx": "^2.0.0", "daisyui": "^4.0.0", "date-fns": "^2.30.0", "js-cookie": "^3.0.5", "lucide-react": "^0.525.0", "next": "^14.0.0", "playwright": "^1.55.1", "react": "^18.2.0", "react-calendar": "^4.6.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-image-gallery": "^1.3.0", "react-select": "^5.8.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "swr": "^2.2.0", "tailwindcss": "^3.3.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "autoprefixer": "^10.4.0", "cross-env": "^7.0.3", "cypress": "^13.3.0", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "next-router-mock": "^1.0.2", "postcss": "^8.4.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "rimraf": "^5.0.0", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.10.0"}