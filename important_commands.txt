view all pages available - find src/pages -type f \( -name "*.tsx" -o -name "*.js" \)

# Reset database and re-seed
docker exec -it gosea-backend npx prisma migrate reset --force

# Create a boat owner user
Using MCP playwright, create an individual boat owner account using details:
- First Name: Aaron
- Last Name: Azri
- Business Email: <EMAIL>
- Phone Number: **********
- Password: Azr!3000
- Operator Type: Individual Operator
